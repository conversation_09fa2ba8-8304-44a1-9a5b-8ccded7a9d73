<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'home';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

// Ana sayfa routes
$route['search'] = 'home/search';
$route['about'] = 'home/about';
$route['contact'] = 'home/contact';
$route['privacy'] = 'home/privacy';
$route['terms'] = 'home/terms';

// İlan routes
$route['ad/(:num)'] = 'home/ad_detail/$1';
$route['city/(:any)'] = 'home/city/$1';
$route['category/(:any)'] = 'home/category/$1';

// Kullanıcı routes
$route['login'] = 'auth/login';
$route['register'] = 'auth/register';
$route['logout'] = 'auth/logout';
$route['dashboard'] = 'dashboard/index';
$route['dashboard/create_ad'] = 'dashboard/create_ad';
$route['dashboard/my_ads'] = 'dashboard/my_ads';
$route['dashboard/edit_ad/(:num)'] = 'dashboard/edit_ad/$1';
$route['dashboard/delete_ad/(:num)'] = 'dashboard/delete_ad/$1';
$route['dashboard/profile'] = 'dashboard/profile';
$route['profile'] = 'dashboard/profile';
$route['messages'] = 'user/messages';

// Admin routes
$route['admin'] = 'admin/dashboard';
$route['admin/login'] = 'admin/auth/login';
$route['admin/logout'] = 'admin/auth/logout';
