<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Authentication Controller
 * 
 * <PERSON>llanı<PERSON><PERSON> giri<PERSON>, kayıt ve kimlik doğrulama işlemleri
 * 
 * @package EscortSite
 * <AUTHOR> Site Developer
 * @version 1.0
 */
class Auth extends CI_Controller {

    public function __construct() {
        parent::__construct();
        
        // Model'leri yükle
        $this->load->model('User_model');
        $this->load->model('Profile_model');
        
        // Library'leri yükle
        $this->load->library(['form_validation', 'email']);
        
        // Helper'ları yükle
        $this->load->helper(['form', 'url', 'security']);
    }

    /**
     * <PERSON><PERSON><PERSON> sayfası
     */
    public function login() {
        // Zaten giriş yapmışsa dashboard'a yönlendir
        if ($this->session->userdata('user_id')) {
            redirect('dashboard');
        }

        $data['page_title'] = '<PERSON><PERSON><PERSON> Yap - Escort İlan Sitesi';

        if ($this->input->post()) {
            $this->form_validation->set_rules('email', 'E-posta', 'required|valid_email');
            $this->form_validation->set_rules('password', 'Şifre', 'required|min_length[6]');

            if ($this->form_validation->run()) {
                $email = $this->input->post('email');
                $password = $this->input->post('password');
                $remember = $this->input->post('remember');

                $user = $this->User_model->login($email, $password);

                // Debug için
                log_message('debug', 'Auth login attempt - Email: ' . $email);
                log_message('debug', 'Auth login result: ' . ($user ? 'Success' : 'Failed'));

                if ($user) {
                    // Session verilerini ayarla
                    $session_data = [
                        'user_id' => $user->id,
                        'username' => $user->username,
                        'email' => $user->email,
                        'role' => $user->role,
                        'logged_in' => TRUE
                    ];
                    $this->session->set_userdata($session_data);

                    // Remember me çerezi
                    if ($remember) {
                        $this->input->set_cookie([
                            'name' => 'remember_token',
                            'value' => md5($user->id . $user->email),
                            'expire' => 86400 * 30 // 30 gün
                        ]);
                    }

                    // Son giriş bilgilerini güncelle
                    $this->User_model->update_last_login($user->id);

                    $this->session->set_flashdata('success', 'Başarıyla giriş yaptınız!');
                    
                    // Rol bazlı yönlendirme
                    if ($user->role === 'admin') {
                        redirect('admin/dashboard');
                    } else {
                        redirect('dashboard');
                    }
                } else {
                    $this->session->set_flashdata('error', 'E-posta veya şifre hatalı!');
                }
            }
        }

        $this->load->view('templates/header', $data);
        $this->load->view('auth/login', $data);
        $this->load->view('templates/footer', $data);
    }

    /**
     * Kayıt sayfası
     */
    public function register() {
        // Zaten giriş yapmışsa dashboard'a yönlendir
        if ($this->session->userdata('user_id')) {
            redirect('dashboard');
        }

        $data['page_title'] = 'Kayıt Ol - Escort İlan Sitesi';

        if ($this->input->post()) {
            $this->form_validation->set_rules('username', 'Kullanıcı Adı', 'required|min_length[3]|max_length[50]|is_unique[users.username]');
            $this->form_validation->set_rules('email', 'E-posta', 'required|valid_email|is_unique[users.email]');
            $this->form_validation->set_rules('password', 'Şifre', 'required|min_length[6]');
            $this->form_validation->set_rules('password_confirm', 'Şifre Tekrar', 'required|matches[password]');
            $this->form_validation->set_rules('role', 'Kullanıcı Tipi', 'required|in_list[escort,customer]');
            $this->form_validation->set_rules('terms', 'Kullanım Şartları', 'required');
            $this->form_validation->set_rules('age_confirm', 'Yaş Onayı', 'required');

            if ($this->form_validation->run()) {
                $user_data = [
                    'username' => $this->input->post('username'),
                    'email' => $this->input->post('email'),
                    'password' => password_hash($this->input->post('password'), PASSWORD_DEFAULT),
                    'role' => $this->input->post('role'),
                    'status' => 'pending',
                    'email_verified' => 0,
                    'email_verification_token' => bin2hex(random_bytes(32))
                ];

                $user_id = $this->User_model->create_user($user_data);

                if ($user_id) {
                    // E-posta doğrulama gönder
                    $this->send_verification_email($user_data['email'], $user_data['email_verification_token']);

                    // Escort ise boş profil oluştur
                    if ($user_data['role'] === 'escort') {
                        $profile_data = [
                            'user_id' => $user_id,
                            'display_name' => $user_data['username'],
                            'age' => 18,
                            'city_id' => 1, // Varsayılan şehir
                            'description' => ''
                        ];
                        $this->Profile_model->create_profile($profile_data);
                    }

                    $this->session->set_flashdata('success', 'Kayıt başarılı! E-posta adresinize doğrulama linki gönderildi.');
                    redirect('auth/login');
                } else {
                    $this->session->set_flashdata('error', 'Kayıt sırasında bir hata oluştu!');
                }
            }
        }

        // Şehirleri getir
        $this->load->model('City_model');
        $data['cities'] = $this->City_model->get_active_cities();

        $this->load->view('templates/header', $data);
        $this->load->view('auth/register', $data);
        $this->load->view('templates/footer', $data);
    }

    /**
     * Çıkış yap
     */
    public function logout() {
        // Session'ı temizle
        $this->session->sess_destroy();
        
        // Remember me çerezini sil
        delete_cookie('remember_token');
        
        $this->session->set_flashdata('success', 'Başarıyla çıkış yaptınız!');
        redirect('');
    }

    /**
     * E-posta doğrulama
     */
    public function verify_email($token) {
        if (empty($token)) {
            show_404();
        }

        $user = $this->User_model->get_user_by_verification_token($token);

        if ($user) {
            // E-posta doğrulandı olarak işaretle
            $this->User_model->verify_email($user->id);
            
            $this->session->set_flashdata('success', 'E-posta adresiniz başarıyla doğrulandı! Şimdi giriş yapabilirsiniz.');
            redirect('auth/login');
        } else {
            $this->session->set_flashdata('error', 'Geçersiz doğrulama linki!');
            redirect('auth/login');
        }
    }

    /**
     * Şifre sıfırlama talebi
     */
    public function forgot_password() {
        $data['page_title'] = 'Şifre Sıfırlama - Escort İlan Sitesi';

        if ($this->input->post()) {
            $this->form_validation->set_rules('email', 'E-posta', 'required|valid_email');

            if ($this->form_validation->run()) {
                $email = $this->input->post('email');
                $user = $this->User_model->get_user_by_email($email);

                if ($user) {
                    $reset_token = bin2hex(random_bytes(32));
                    $this->User_model->set_reset_token($user->id, $reset_token);
                    
                    // Şifre sıfırlama e-postası gönder
                    $this->send_reset_email($email, $reset_token);
                    
                    $this->session->set_flashdata('success', 'Şifre sıfırlama linki e-posta adresinize gönderildi.');
                } else {
                    $this->session->set_flashdata('error', 'Bu e-posta adresi ile kayıtlı kullanıcı bulunamadı.');
                }
            }
        }

        $this->load->view('templates/header', $data);
        $this->load->view('auth/forgot_password', $data);
        $this->load->view('templates/footer', $data);
    }

    /**
     * Şifre sıfırlama
     */
    public function reset_password($token) {
        if (empty($token)) {
            show_404();
        }

        $user = $this->User_model->get_user_by_reset_token($token);

        if (!$user) {
            $this->session->set_flashdata('error', 'Geçersiz veya süresi dolmuş şifre sıfırlama linki!');
            redirect('auth/forgot_password');
        }

        $data['page_title'] = 'Yeni Şifre Belirle - Escort İlan Sitesi';
        $data['token'] = $token;

        if ($this->input->post()) {
            $this->form_validation->set_rules('password', 'Yeni Şifre', 'required|min_length[6]');
            $this->form_validation->set_rules('password_confirm', 'Şifre Tekrar', 'required|matches[password]');

            if ($this->form_validation->run()) {
                $new_password = password_hash($this->input->post('password'), PASSWORD_DEFAULT);
                $this->User_model->update_password($user->id, $new_password);
                
                $this->session->set_flashdata('success', 'Şifreniz başarıyla güncellendi! Şimdi giriş yapabilirsiniz.');
                redirect('auth/login');
            }
        }

        $this->load->view('templates/header', $data);
        $this->load->view('auth/reset_password', $data);
        $this->load->view('templates/footer', $data);
    }

    /**
     * E-posta doğrulama gönder
     */
    private function send_verification_email($email, $token) {
        $this->email->from('<EMAIL>', 'Escort İlan Sitesi');
        $this->email->to($email);
        $this->email->subject('E-posta Doğrulama');
        
        $message = "Merhaba,\n\n";
        $message .= "E-posta adresinizi doğrulamak için aşağıdaki linke tıklayın:\n\n";
        $message .= site_url('auth/verify_email/' . $token) . "\n\n";
        $message .= "Bu linki siz talep etmediyseniz, bu e-postayı görmezden gelebilirsiniz.\n\n";
        $message .= "Teşekkürler,\nEscort İlan Sitesi";
        
        $this->email->message($message);
        $this->email->send();
    }

    /**
     * Şifre sıfırlama e-postası gönder
     */
    private function send_reset_email($email, $token) {
        $this->email->from('<EMAIL>', 'Escort İlan Sitesi');
        $this->email->to($email);
        $this->email->subject('Şifre Sıfırlama');
        
        $message = "Merhaba,\n\n";
        $message .= "Şifrenizi sıfırlamak için aşağıdaki linke tıklayın:\n\n";
        $message .= site_url('auth/reset_password/' . $token) . "\n\n";
        $message .= "Bu linki siz talep etmediyseniz, bu e-postayı görmezden gelebilirsiniz.\n\n";
        $message .= "Teşekkürler,\nEscort İlan Sitesi";
        
        $this->email->message($message);
        $this->email->send();
    }
}
