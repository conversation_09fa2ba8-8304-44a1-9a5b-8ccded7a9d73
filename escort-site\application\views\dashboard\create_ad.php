<!-- Create Ad Section -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="<?php echo site_url('dashboard'); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-home me-2"></i>Ana <PERSON>
                        </a>
                        <a href="<?php echo site_url('dashboard/create_ad'); ?>" class="list-group-item list-group-item-action active">
                            <i class="fas fa-plus me-2"></i><PERSON><PERSON>
                        </a>
                        <a href="<?php echo site_url('dashboard/my_ads'); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-list me-2"></i><PERSON><PERSON>larım
                        </a>
                        <a href="<?php echo site_url('dashboard/profile'); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-user me-2"></i>Profil
                        </a>
                        <a href="<?php echo site_url('auth/logout'); ?>" class="list-group-item list-group-item-action text-danger">
                            <i class="fas fa-sign-out-alt me-2"></i>Çıkış Yap
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-lg-9">
                <div class="card shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-plus me-2"></i>
                            Yeni İlan Oluştur
                        </h4>
                    </div>
                    
                    <div class="card-body p-4">
                        <?php echo validation_errors('<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>', '</div>'); ?>

                        <form action="<?php echo site_url('dashboard/create_ad'); ?>" method="POST" class="needs-validation" novalidate>
                            <!-- İlan Başlığı -->
                            <div class="mb-4">
                                <label for="title" class="form-label">
                                    <i class="fas fa-heading me-1"></i>İlan Başlığı <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control form-control-lg" id="title" name="title" 
                                       value="<?php echo set_value('title'); ?>" 
                                       placeholder="Örn: Güzel ve Zarif Escort Hizmeti" 
                                       maxlength="200" required>
                                <div class="form-text">
                                    Dikkat çekici ve açıklayıcı bir başlık yazın (10-200 karakter)
                                </div>
                                <div class="invalid-feedback">
                                    İlan başlığı en az 10 karakter olmalıdır.
                                </div>
                            </div>
                            
                            <!-- Şehir ve Kategori -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="city_id" class="form-label">
                                        <i class="fas fa-map-marker-alt me-1"></i>Şehir <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-select-lg" id="city_id" name="city_id" required>
                                        <option value="">Şehir Seçin</option>
                                        <?php if (isset($cities) && !empty($cities)): ?>
                                            <?php foreach ($cities as $city): ?>
                                                <option value="<?php echo $city->id; ?>" 
                                                        <?php echo (set_value('city_id') == $city->id) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($city->name); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Lütfen bir şehir seçin.
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="category_id" class="form-label">
                                        <i class="fas fa-tag me-1"></i>Kategori <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-select-lg" id="category_id" name="category_id" required>
                                        <option value="">Kategori Seçin</option>
                                        <?php if (isset($categories) && !empty($categories)): ?>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category->id; ?>" 
                                                        <?php echo (set_value('category_id') == $category->id) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($category->name); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Lütfen bir kategori seçin.
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Fiyat -->
                            <div class="mb-4">
                                <label for="price" class="form-label">
                                    <i class="fas fa-money-bill-wave me-1"></i>Fiyat (TL) <span class="text-muted">(İsteğe bağlı)</span>
                                </label>
                                <div class="input-group">
                                    <input type="number" class="form-control form-control-lg" id="price" name="price" 
                                           value="<?php echo set_value('price'); ?>" 
                                           placeholder="Örn: 500" min="0" max="99999">
                                    <span class="input-group-text">₺</span>
                                </div>
                                <div class="form-text">
                                    Fiyat belirtmek zorunda değilsiniz. Boş bırakabilirsiniz.
                                </div>
                            </div>
                            
                            <!-- Açıklama -->
                            <div class="mb-4">
                                <label for="description" class="form-label">
                                    <i class="fas fa-align-left me-1"></i>Açıklama <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="8" 
                                          placeholder="İlanınızın detaylı açıklamasını yazın..." 
                                          maxlength="2000" required><?php echo set_value('description'); ?></textarea>
                                <div class="form-text">
                                    Hizmetlerinizi, özelliklerinizi ve iletişim bilgilerinizi detaylı olarak açıklayın (50-2000 karakter)
                                </div>
                                <div class="invalid-feedback">
                                    Açıklama en az 50 karakter olmalıdır.
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">
                                        <span id="char-count">0</span>/2000 karakter
                                    </small>
                                </div>
                            </div>
                            
                            <!-- Uyarı Kutusu -->
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Önemli Uyarılar:</h6>
                                <ul class="mb-0">
                                    <li>İlanınız admin onayından sonra yayınlanacaktır</li>
                                    <li>Uygunsuz içerik ve fotoğraflar kabul edilmez</li>
                                    <li>Kişisel iletişim bilgilerinizi açıklamada belirtebilirsiniz</li>
                                    <li>Sahte bilgi ve fotoğraf kullanmak yasaktır</li>
                                </ul>
                            </div>
                            
                            <!-- Onay Kutuları -->
                            <div class="mb-4">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="terms_agree" required>
                                    <label class="form-check-label" for="terms_agree">
                                        <a href="<?php echo site_url('home/terms'); ?>" target="_blank">Kullanım Şartları</a>'nı okudum ve kabul ediyorum <span class="text-danger">*</span>
                                    </label>
                                    <div class="invalid-feedback">
                                        Kullanım şartlarını kabul etmelisiniz.
                                    </div>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="content_agree" required>
                                    <label class="form-check-label" for="content_agree">
                                        İlan içeriğimin gerçek ve doğru olduğunu beyan ederim <span class="text-danger">*</span>
                                    </label>
                                    <div class="invalid-feedback">
                                        Bu beyanı vermelisiniz.
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Butonlar -->
                            <div class="d-flex justify-content-between">
                                <a href="<?php echo site_url('dashboard'); ?>" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-arrow-left me-2"></i>Geri Dön
                                </a>
                                
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-check me-2"></i>İlanı Oluştur
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Yardım Kartı -->
                <div class="card mt-4 border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>İpuçları
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><i class="fas fa-check text-success me-2"></i>Başlığınızı dikkat çekici yapın</li>
                            <li><i class="fas fa-check text-success me-2"></i>Açıklamanızda hizmetlerinizi detaylandırın</li>
                            <li><i class="fas fa-check text-success me-2"></i>Fotoğraflarınızı profil bölümünden ekleyin</li>
                            <li><i class="fas fa-check text-success me-2"></i>İletişim bilgilerinizi açıklamaya ekleyin</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.form-control-lg, .form-select-lg {
    border-radius: 10px;
}

.btn-lg {
    border-radius: 10px;
    font-weight: 600;
}

.list-group-item.active {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.card {
    border-radius: 15px;
    overflow: hidden;
}

@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem !important;
    }
}
</style>

<script>
$(document).ready(function() {
    // Character counter
    $('#description').on('input', function() {
        const length = $(this).val().length;
        $('#char-count').text(length);
        
        if (length > 1800) {
            $('#char-count').addClass('text-warning');
        } else if (length > 1900) {
            $('#char-count').addClass('text-danger').removeClass('text-warning');
        } else {
            $('#char-count').removeClass('text-warning text-danger');
        }
    });
    
    // Form validation
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });
    
    // Title suggestions based on category
    $('#category_id').change(function() {
        const category = $(this).find('option:selected').text();
        const titleField = $('#title');
        
        if (titleField.val() === '') {
            let suggestion = '';
            switch(category) {
                case 'Escort':
                    suggestion = 'Profesyonel Escort Hizmeti - ';
                    break;
                case 'Masaj':
                    suggestion = 'Rahatlatıcı Masaj Hizmeti - ';
                    break;
                case 'Eşlik':
                    suggestion = 'Nezaket Eşlik Hizmeti - ';
                    break;
                case 'VIP':
                    suggestion = 'VIP Özel Hizmet - ';
                    break;
            }
            titleField.attr('placeholder', suggestion + 'Devamını yazın...');
        }
    });
    
    // Auto-save draft (localStorage)
    const saveInterval = setInterval(function() {
        const formData = {
            title: $('#title').val(),
            description: $('#description').val(),
            city_id: $('#city_id').val(),
            category_id: $('#category_id').val(),
            price: $('#price').val()
        };
        
        if (formData.title || formData.description) {
            localStorage.setItem('ad_draft', JSON.stringify(formData));
        }
    }, 30000); // Her 30 saniyede bir kaydet
    
    // Load draft on page load
    const draft = localStorage.getItem('ad_draft');
    if (draft) {
        const data = JSON.parse(draft);
        if (confirm('Kaydedilmiş taslak bulundu. Yüklemek ister misiniz?')) {
            $('#title').val(data.title);
            $('#description').val(data.description);
            $('#city_id').val(data.city_id);
            $('#category_id').val(data.category_id);
            $('#price').val(data.price);
            $('#description').trigger('input'); // Character count güncelle
        }
    }
    
    // Clear draft on successful submit
    $('form').on('submit', function() {
        localStorage.removeItem('ad_draft');
    });
});
</script>
