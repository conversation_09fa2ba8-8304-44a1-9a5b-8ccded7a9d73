<!-- Category Header -->
<section class="bg-gradient-primary text-white py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo base_url(); ?>" class="text-white">Ana Sayfa</a></li>
                        <li class="breadcrumb-item active text-white">Kategoriler</li>
                        <li class="breadcrumb-item active text-white"><?php echo isset($category) ? htmlspecialchars($category->name) : 'Kategori'; ?></li>
                    </ol>
                </nav>
                
                <h1 class="display-5 fw-bold mb-3">
                    <i class="fas fa-tag me-2"></i>
                    <?php echo isset($category) ? htmlspecialchars($category->name) : 'Kategori'; ?> <PERSON>lanları
                </h1>
                
                <?php if (isset($category) && !empty($category->description)): ?>
                    <p class="lead">
                        <?php echo htmlspecialchars($category->description); ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Filters -->
<section class="py-3 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form action="<?php echo current_url(); ?>" method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="keyword" class="form-label">Arama</label>
                                <input type="text" class="form-control" id="keyword" name="q" 
                                       value="<?php echo $this->input->get('q'); ?>" 
                                       placeholder="İsim veya açıklama...">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="city" class="form-label">Şehir</label>
                                <select name="city" id="city" class="form-select">
                                    <option value="">Tüm Şehirler</option>
                                    <option value="1" <?php echo ($this->input->get('city') == '1') ? 'selected' : ''; ?>>İstanbul</option>
                                    <option value="2" <?php echo ($this->input->get('city') == '2') ? 'selected' : ''; ?>>Ankara</option>
                                    <option value="3" <?php echo ($this->input->get('city') == '3') ? 'selected' : ''; ?>>İzmir</option>
                                    <option value="4" <?php echo ($this->input->get('city') == '4') ? 'selected' : ''; ?>>Bursa</option>
                                    <option value="5" <?php echo ($this->input->get('city') == '5') ? 'selected' : ''; ?>>Antalya</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="min_age" class="form-label">Min Yaş</label>
                                <select name="min_age" id="min_age" class="form-select">
                                    <option value="">Seçin</option>
                                    <?php for ($i = 18; $i <= 45; $i++): ?>
                                        <option value="<?php echo $i; ?>" <?php echo ($this->input->get('min_age') == $i) ? 'selected' : ''; ?>>
                                            <?php echo $i; ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="max_age" class="form-label">Max Yaş</label>
                                <select name="max_age" id="max_age" class="form-select">
                                    <option value="">Seçin</option>
                                    <?php for ($i = 18; $i <= 45; $i++): ?>
                                        <option value="<?php echo $i; ?>" <?php echo ($this->input->get('max_age') == $i) ? 'selected' : ''; ?>>
                                            <?php echo $i; ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="sort" class="form-label">Sıralama</label>
                                <select name="sort" id="sort" class="form-select">
                                    <option value="newest" <?php echo ($this->input->get('sort') == 'newest') ? 'selected' : ''; ?>>En Yeni</option>
                                    <option value="oldest" <?php echo ($this->input->get('sort') == 'oldest') ? 'selected' : ''; ?>>En Eski</option>
                                    <option value="price_low" <?php echo ($this->input->get('sort') == 'price_low') ? 'selected' : ''; ?>>Fiyat (Düşük)</option>
                                    <option value="price_high" <?php echo ($this->input->get('sort') == 'price_high') ? 'selected' : ''; ?>>Fiyat (Yüksek)</option>
                                </select>
                            </div>
                            
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Ads Grid -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <!-- Results Info -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="h4 mb-0">
                        İlanlar
                        <?php if (isset($ads) && is_array($ads)): ?>
                            <span class="badge bg-primary"><?php echo count($ads); ?> sonuç</span>
                        <?php endif; ?>
                    </h2>
                    
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="view_type" id="grid_view" autocomplete="off" checked>
                        <label class="btn btn-outline-secondary" for="grid_view">
                            <i class="fas fa-th"></i>
                        </label>
                        
                        <input type="radio" class="btn-check" name="view_type" id="list_view" autocomplete="off">
                        <label class="btn btn-outline-secondary" for="list_view">
                            <i class="fas fa-list"></i>
                        </label>
                    </div>
                </div>
                
                <!-- Ads -->
                <?php if (isset($ads) && !empty($ads)): ?>
                    <div class="row" id="ads_grid">
                        <?php foreach ($ads as $ad): ?>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                <div class="card ad-card h-100 shadow-sm">
                                    <div class="position-relative">
                                        <?php if (!empty($ad->profile_photo)): ?>
                                            <img src="<?php echo base_url('uploads/profiles/' . $ad->profile_photo); ?>" 
                                                 class="card-img-top" alt="<?php echo htmlspecialchars($ad->display_name); ?>" 
                                                 style="height: 250px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                                <i class="fas fa-user fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (isset($ad->is_featured) && $ad->is_featured): ?>
                                            <div class="position-absolute top-0 start-0 m-2">
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-star me-1"></i>Öne Çıkan
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (isset($ad->price) && $ad->price): ?>
                                            <div class="position-absolute top-0 end-0 m-2">
                                                <span class="badge bg-success">
                                                    <?php echo number_format($ad->price); ?> ₺
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo htmlspecialchars($ad->display_name); ?></h5>
                                        <p class="card-text text-muted small">
                                            <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($ad->city_name); ?>
                                            <?php if (isset($ad->age)): ?>
                                                <span class="ms-2">
                                                    <i class="fas fa-birthday-cake me-1"></i><?php echo $ad->age; ?> yaş
                                                </span>
                                            <?php endif; ?>
                                        </p>
                                        <p class="card-text"><?php echo character_limiter(strip_tags($ad->description), 80); ?></p>
                                    </div>
                                    
                                    <div class="card-footer bg-transparent">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-eye me-1"></i><?php echo number_format($ad->view_count); ?> görüntülenme
                                            </small>
                                            <a href="<?php echo base_url('home/ad_detail/' . $ad->id); ?>" class="btn btn-primary btn-sm">
                                                Detay Gör
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if (isset($pagination) && !empty($pagination)): ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-center">
                                    <?php echo $pagination; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <!-- No Ads -->
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h3 class="text-muted">Bu kategoride henüz ilan bulunmuyor</h3>
                        <p class="text-muted">
                            <?php echo isset($category) ? htmlspecialchars($category->name) : 'Bu kategori'; ?> için henüz ilan eklenmemiş. 
                            Diğer kategorileri kontrol edebilirsiniz.
                        </p>
                        <a href="<?php echo base_url(); ?>" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>Ana Sayfaya Dön
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Other Categories -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="mb-4 text-center">Diğer Kategoriler</h3>
                <div class="row">
                    <?php 
                    $other_categories = [
                        ['name' => 'Escort', 'slug' => 'escort', 'icon' => 'fa-user'],
                        ['name' => 'Masaj', 'slug' => 'masaj', 'icon' => 'fa-spa'],
                        ['name' => 'Eşlik', 'slug' => 'eslik', 'icon' => 'fa-users'],
                        ['name' => 'VIP', 'slug' => 'vip', 'icon' => 'fa-crown']
                    ];
                    ?>
                    
                    <?php foreach ($other_categories as $other_category): ?>
                        <?php if (!isset($category) || $other_category['slug'] !== $category->slug): ?>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="<?php echo base_url('home/category/' . $other_category['slug']); ?>" class="text-decoration-none">
                                    <div class="card category-card h-100 shadow-sm">
                                        <div class="card-body text-center">
                                            <i class="fas <?php echo $other_category['icon']; ?> fa-3x text-primary mb-3"></i>
                                            <h5 class="card-title"><?php echo $other_category['name']; ?></h5>
                                            <p class="card-text text-muted">İlanları Görüntüle</p>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.7);
}

.ad-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.category-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
}

.category-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1) !important;
}

.btn-check:checked + .btn-outline-secondary {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

@media (max-width: 768px) {
    .ad-card .card-img-top {
        height: 200px;
    }
    
    .display-5 {
        font-size: 1.5rem;
    }
}
</style>

<script>
$(document).ready(function() {
    // View type toggle
    $('input[name="view_type"]').change(function() {
        if ($(this).attr('id') === 'list_view') {
            $('#ads_grid').removeClass('row').addClass('list-view');
            $('#ads_grid .col-lg-3').removeClass('col-lg-3 col-md-4 col-sm-6').addClass('col-12');
        } else {
            $('#ads_grid').removeClass('list-view').addClass('row');
            $('#ads_grid .col-12').removeClass('col-12').addClass('col-lg-3 col-md-4 col-sm-6');
        }
    });
});
</script>
