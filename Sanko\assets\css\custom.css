@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-XXThin.eot');
  src: local('NeueHaasDisplay-XXThin'), local('NeueHaasDisplay-XXThin'),
    url('../font/NeueHaasDisplay-XXThin.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-XXThin.woff') format('woff'),
    url('../font/NeueHaasDisplayXXThin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-XXThinItalic.eot');
  src: local('NeueHaasDisplayXXThinItalic'), local('NeueHaasDisplayXXThinItalic'),
    url('../font/NeueHaasDisplay-XXThinItalic.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-XXThinItalic.woff') format('woff'),
    url('../font/NeueHaasDisplayXXThinItalic.ttf') format('truetype');
  font-weight: 100;
  font-style: italic;
}

@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-XThin.eot');
  src: local('NeueHaasDisplay-XThin'), local('NeueHaasDisplay-XThin'),
    url('../font/NeueHaasDisplay-XThin.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-XThin.woff') format('woff'),
    url('../font/NeueHaasDisplayXThin.ttf') format('truetype');
  font-weight: 200;
  font-style: normal;
}

@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-XThinItalic.eot');
  src: local('NeueHaasDisplay-XThinItalic'), local('NeueHaasDisplay-XThinItalic'),
    url('../font/NeueHaasDisplay-XThinItalic.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-XThinItalic.woff') format('woff'),
    url('../font/NeueHaasDisplayXThinItalic.ttf') format('truetype');
  font-weight: 200;
  font-style: italic;
}

@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-Thin.eot');
  src: local('NeueHaasDisplay-Thin'), local('NeueHaasDisplay-Thin'),
    url('../font/NeueHaasDisplay-Thin.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-Thin.woff') format('woff'),
    url('../font/NeueHaasDisplayThin.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-ThinItalic.eot');
  src: local('NeueHaasDisplayThinItalic'), local('NeueHaasDisplayThinItalic'),
    url('../font/NeueHaasDisplay-ThinItalic.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-ThinItalic.woff') format('woff'),
    url('../font/NeueHaasDisplayThinItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: italic;
}



@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-Light.eot');
  src: local('NeueHaasDisplayLight'), local('NeueHaasDisplayLight'),
    url('../font/NeueHaasDisplay-Light.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-Light.woff') format('woff'),
    url('../font/NeueHaasDisplayLight.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}


@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-LightItalic.eot');
  src: local('NeueHaasDisplayLightItalic'), local('NeueHaasDisplayLightItalic'),
    url('../font/NeueHaasDisplay-LightItalic.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-LightItalic.woff') format('woff'),
    url('../font/NeueHaasDisplayLightItalic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
}


@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-Roman.eot');
  src: local('NeueHaasDisplayRoman'), local('NeueHaasDisplayRoman'),
    url('../font/NeueHaasDisplay-Roman.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-Roman.woff') format('woff'),
    url('../font/NeueHaasDisplayRoman.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-RomanItalic.eot');
  src: local('NeueHaasDisplayRomanItalic'), local('NeueHaasDisplayRomanItalic'),
    url('../font/NeueHaasDisplay-RomanItalic.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-RomanItalic.woff') format('woff'),
    url('../font/NeueHaasDisplayRomanItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-Mediu.eot');
  src: local('NeueHaasDisplayMediu'), local('NeueHaasDisplayMediu'),
    url('../font/NeueHaasDisplay-Mediu.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-Mediu.woff') format('woff'),
    url('../font/NeueHaasDisplayMediu.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-MediumItalic.eot');
  src: local('NeueHaasDisplayMediumItalic'), local('NeueHaasDisplayMediumItalic'),
    url('../font/NeueHaasDisplay-MediumItalic.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-MediumItalic.woff') format('woff'),
    url('../font/NeueHaasDisplayMediumItalic.ttf') format('truetype');
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-Bold.eot');
  src: local('NeueHaasDisplayBold'), local('NeueHaasDisplayBold'),
    url('../font/NeueHaasDisplay-Bold.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-Bold.woff') format('woff'),
    url('../font/NeueHaasDisplayBold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-BoldItalic.eot');
  src: local('NeueHaasDisplayBoldItalic'), local('NeueHaasDisplayBoldItalic'),
    url('../font/NeueHaasDisplay-BoldItalic.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-BoldItalic.woff') format('woff'),
    url('../font/NeueHaasDisplayBoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-Black.eot');
  src: local('NeueHaasDisplayBlack'), local('NeueHaasDisplayBlack'),
    url('../font/NeueHaasDisplay-Black.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-Black.woff') format('woff'),
    url('../font/NeueHaasDisplayBlack.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'NeueHass';
  src: url('../font/NeueHaasDisplay-BlackItalic.eot');
  src: local('NeueHaasDisplayBlackItalic'), local('NeueHaasDisplayBlackItalic'),
    url('../font/NeueHaasDisplay-BlackItalic.eot?#iefix') format('embedded-opentype'),
    url('../font/NeueHaasDisplay-BlackItalic.woff') format('woff'),
    url('../font/NeueHaasDisplayBlackItalic.ttf') format('truetype');
  font-weight: 800;
  font-style: italic;
}

ul.sub_menu.active {
  display: block !important;
  visibility: visible;
  opacity: 1;
  margin-top: 0;
  z-index: 99;
}
html {
  scroll-behavior: smooth;
}
body {
  scroll-behavior: smooth;
  font-family: "NeueHass", sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 28px;
}

.common-color {
  color: #94B0D5;
}

.highlight h2 span {
  color: #223260;
  position: relative;
}

.home1-txt1 {
  color: #FFF;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
  letter-spacing: 1px;
}

.highlight h2 span:after {
  position: absolute;
  content: '';
  border-bottom: 2px solid;
  bottom: 5px;
  left: 0;
  width: 100%;
}

.underline {
  position: relative;
}

.underline:after {
  position: absolute;
  content: '';
  border-bottom: 2px solid;
  bottom: 5px;
  left: 0;
  width: 100%;
}

input.form-control.floating-placeholder:focus {
  outline: 0;
  box-shadow: none;
}

.nopadding {
  padding: 0 !important;
  margin: 0 !important;
}

.color-blue {
  color: #0A1D35 !important;
}

.color-skyblue {
  color: #94B0D5 !important;
}

.color-dark-blue {
  color: #45566C;
}

.color-white {
  color: #ffffff !important;
}

.color-red {
  color: #C12129 !important;
}

.back-white {
  background: #ffffff !important;
}

.back-red {
  background: #223260 !important;
}

.back-white-light {
  background: #F1F5F9;
}

.container-fluid {
  padding: 0 15px;
}

.container {
  max-width: 1500px;
  padding: 0 15px;
}

.carbon-blue {
  /* background: #0A1D35; */
}

.main-body {
  overflow: hidden;
}

.img-100 {
  width: 100%;
}

.line-height-63 {
  line-height: 63px;
}

.line-height-24 {
  line-height: 24px;
}

a {
  color: #223260;
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: #223260;
  text-decoration: none;
  /* font-weight: bold; */
}

.color-text span {
  color: #223260;
}

h1,
h2,
h3,
h4,
h5,
h6 {

  margin: 0;
}

h1 {

  font-weight: 400;
  font-size: 64px;
  line-height: 80px;
  color: #ffffff;
}

h2 {

  font-weight: 400;
  font-size: 48px;
  line-height: 60px;
  color: #ffffff;
}

h3 {
  color: #223260;

  font-size: 40px;
  font-weight: 400;
  line-height: normal;
}

h5 {

  font-weight: 400;
  font-size: 32px;
  line-height: 40px;
  color: #FFFFFF;
  display: unset;
}

h4 {
  color: #223260;
  font-size: 23px;
  font-weight: 400;
  line-height: normal;
}

h6 {
  font-weight: 600;
  font-size: 20px;
  line-height: 25px;
  color: #FFFFFF;
}

.text-left {
  text-align: left;
}

.m-auto {
  margin: 0 auto;
}

.section-m-100 {
  margin: 100px 0;
}

.section-p-50 {
  padding: 50px 0;
}

.section-p-100 {
  padding: 100px 0;
}

.section-pb-100 {
  padding-bottom: 100px;
}

.section-mb-100 {
  margin-bottom: 100px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-60 {
  margin-top: 60px;
}
.mt-100 {
  margin-top: 100px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-50 {
  margin-bottom: 50px;
}
.mb-100 {
  margin-bottom: 100px;
}


.pb-60 {
  padding-bottom: 60px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mt-30 {
  margin-top: 30px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mr-20 {
  margin-right: 20px;
}

.ml-30 {
  margin-left: 30px;
}

.mr-0 {
  margin-right: 0px !important;
}

.ml-0 {
  margin-left: 0px !important;
}

.container-570 {
  max-width: 570px;
  width: 100%;
}

.container-430 {
  max-width: 430px;
  width: 100%;
}

.container-970 {
  max-width: 970px;
  width: 100%;
}

.container-770 {
  max-width: 770px;
  width: 100%;
}

.pt-20 {
  padding-top: 20px;
}

.pb-60 {
  padding-bottom: 60px;
}

.shape-image {
  max-width: 276px !important;
}

.in-right {
  animation: fadeInRight 1s ease-in-out;
}

.text-right {
  text-align: right;
}

.hover-color a:hover {
  color: #223260 !important;
}


/************************ Image animation scroll ***************************/
section.sparkle-section,
section.meet-expert {
  border-top: 1px solid #ffffff;
  position: relative;
  top: -1px;
}

.image {
  display: block;
}

.splitting.cells img {
  display: block;
}

textarea:focus-visible {
  outline-offset: 0px !important;
}

textarea:focus {
  box-shadow: none !important;
}

/************************ 2. Button css ***************************/
.sidebar-btn .button-wrap {
  padding: 20px 90px;
}

.home4-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn {
  display: flex;
  justify-content: flex-end;
}

.tes1-load-btn {
  display: flex;
  justify-content: center;
}

.form-button.text-center.tes1-load-btn1 {
  margin-top: 0 !important;
  margin-top: 60px !important;
}

.contact-iframe {
  width: 1170px;
  height: 600px;
}

.button-box,
.button-box-body {
  line-height: 63px;
}

.form-button .button-box {
  line-height: 0;
}

.button-whole-wrap {
  line-height: 60px;
}

.button-wrap {
  cursor: pointer;
  background-color: #000;
  padding: 20px;
  position: relative;
  z-index: 0;
  background: #223260;
}

a.button-wrap.heder-btn {
  border: 1px solid transparent;
}

a.button-wrap.heder-btn:hover {
  border: 1px solid #223260;
}

.button-wrap span {
  display: inline-block;
  transform-origin: center left;
  transition: color 0.3s ease;
  position: relative;
  z-index: 1;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #FFFFFF;
}

.button-wrap:before,
.button-wrap:after {
  content: '';
  background: #fff;
  height: 50%;
  width: 0;
  position: absolute;
  transition: 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  -webkit-transition: 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

.button-wrap:before {
  top: 0;
  left: 0;
  right: auto;
}

.button-wrap:after {
  bottom: 0;
  right: 0;
  left: auto;
}

.button-wrap:hover:before {
  width: 100%;
  right: 0;
  left: auto;
}

.button-wrap:hover:after {
  width: 100%;
  left: 0;
  right: auto;
}

.button-wrap:hover span,
.button-wrap:hover i {
  color: #000;
}

.button-wrap i {
  color: #ffffff;
}

.white-hover .button-wrap:before,
.white-hover .button-wrap:after {
  background: #ffffff !important;
}

.white-hover .button-wrap:hover span,
.white-hover .button-wrap:hover i {
  color: #0A1D35 !important;
}

.btn-white .button-wrap {
  background: #ffffff;
  border: 2px solid #223260;
}

.btn-white .button-wrap span,
.btn-white .button-wrap i {
  color: #223260;
}

.btn-white .button-wrap:before,
.btn-white .button-wrap:after {
  background: #223260;
}

.btn-white .button-wrap:hover span,
.btn-white .button-wrap:hover i,
.pricing-plan-col:hover .btn-white .button-wrap span,
.pricing-plan-col:hover .btn-white .button-wrap i {
  color: #ffffff;
}

.pricing-plan-col:hover .btn-white .button-wrap {
  background: #223260 !important;
}

.button-box.btn-white {
  display: block !important;
  margin-bottom: 0;
}

.input-block {
  position: relative;
}

.input-block input,
.input-block textarea {
  padding: 18px 20px;
  border: inherit;
  border-radius: 10;
  background: #fff;
  color: #223260;
  border: 1px solid #223260;
}

.input-block select {
  padding: 18px 20px;
  border: 1px solid #94B0D5;
  border-radius: 10px;
  background: #fff;
  color: #000000;
  border: none;
  font-size: 18px;
  font-weight: 500;
  border: 1px solid #223260;
}

.input-block label {
  left: 0px;
  color: #000000 !important;
  font-size: 18px !important;
  font-weight: 500 !important;
  line-height: 24px;
  background: none !important;
}

.home-4-name::placeholder {
  color: #94B0D5 !important;
}

.home-4-name {
  color: #94B0D5 !important;
}

.form-button .button-wrap {
  transform: none;
  border-radius: 10px;
}

.form-button .button-wrap:before,
.form-button .button-wrap:after {
  background: #223260;
}

.form-button .button-wrap:hover span,
.form-button .button-wrap:hover i {
  color: #ffffff;
}

/************************ 3. Nav Menu ***************************/
.fixed {
  position: fixed !important;
  backdrop-filter: blur(5px);
  width: 100%;
  top: 0;
  left: 0;
  box-shadow: 0 0 22px -4px rgb(0 0 0 / 17%);
  animation: fixedheader 600ms ease 0ms 1 forwards;
}

.home-2-header.fixed {
  background: linear-gradient(344deg, rgb(202 154 103 / 10%) 0%, rgb(11 19 21) 100%) !important;
}

.home-3-header.fixed {
  background: #223260c7 !important;
}

.home6-header.fixed {
  background: #0A1D35;
  z-index: 99;
}

.home-5-header .nav-menu>li>a {
  color: #ffffff !important;
}

header {
  z-index: 99998;
  position: absolute;
  width: 100%;

}

header.detail {
  position: relative;
  background: #223260;
}

.home-1-header .nav-menu>li>a {
  color: #94B0D5;
}

.home-1-header {
  border-bottom: 1px solid #45566C;
}

.home-1-header .nav-toggle:before {
  background-color: #ffffff;
  box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
}

.common-header .submenu-indicator-chevron {
  border-color: #ffffff;
}

.home6-title {
  display: none;
}

.home-6-header-offcanvas {
  justify-content: right;
}

/************************ inner page menu ***************************/
.nav {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}
.navigation {
  width: 100%;
  height: 100px;
  display: table;
  position: relative;
  font-family: inherit;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navigation * {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

.navigation-portrait {
  height: 48px;
}

.navigation-fixed {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 19998;
}

.navigation-hidden {
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.nav-header {
  float: left;
}

.navigation-hidden .nav-header {
  display: none;
}

.nav-brand {
  line-height: 70px;
  padding: 0;
  color: #343a40;
  font-size: 24px;
  text-decoration: none !important;
}

.nav-brand:hover,
.nav-brand:focus {
  color: #343a40;
}

.navigation-portrait .nav-brand {
  font-size: 18px;
  line-height: 48px;
}

.nav-logo>img {
  height: 48px;
  margin: 11px auto;
  padding: 0 15px;
  float: left;
}

.nav-logo:focus>img {
  outline: initial;
}

.navigation-portrait .nav-logo>img {
  height: 36px;
  margin: 6px auto 6px 15px;
  padding: 0;
}

.nav-toggle {
  width: 30px;
  height: 30px;
  padding: 6px 2px 0;
  position: absolute;
  top: 50%;
  margin-top: -14px;
  right: 0px;
  display: none;
  cursor: pointer;
}

.nav-toggle:before {
  content: "";
  position: absolute;
  width: 24px;
  height: 2px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 0.5em 0 0 #ffffff, 0 1em 0 0 #ffffff;
}

.navigation-portrait .nav-toggle {
  display: block;
}

.navigation-portrait .nav-menus-wrapper {
  width: 100%;
  height: 100vh;
  top: 0;
  left: -4000px;
  position: fixed;
  background-color: #fff;
  z-index: 20000;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  transition-duration: 0.2s;
  transition-timing-function: ease;
}

.navigation-portrait .nav-menus-wrapper.nav-menus-wrapper-right {
  left: auto;
  right: -400px;
}

.navigation-portrait .nav-menus-wrapper.nav-menus-wrapper-open {
  left: 0;
}

.navigation-portrait .nav-menus-wrapper.nav-menus-wrapper-right.nav-menus-wrapper-open {
  left: auto;
  right: 0;
}

.nav-menus-wrapper-close-button {
  width: 30px;
  height: 40px;
  margin: 10px 7px;
  display: none;
  float: right;
  color: #343a40;
  font-size: 26px;
  cursor: pointer;
}

.navigation-portrait .nav-menus-wrapper-close-button {
  display: block;
}

.nav-menu {
  margin: 0;
  list-style: none;
  line-height: normal;
  font-size: 0;
  /* Rectangle 33 */
  margin-top: 10px;
  margin-bottom: 10px;
  
  /* Note: backdrop-filter has minimal browser support */
  padding: 0px 15px;
  margin-left: 20px;
}

.navigation-portrait .nav-menu {
  width: 100%;
  float: left;
  margin: 0px;
  padding: 20px;
  padding-top: 0px;
}

.navigation-landscape .nav-menu.nav-menu-centered {
  float: none;
  text-align: center;
}

.navigation-landscape .nav-menu.nav-menu-centered>li {
  float: none;
}

.nav-menu>li {
  display: inline-block;
  float: left;
  text-align: left;
}

.navigation-portrait .nav-menu>li {
  width: 100%;
  position: relative;
  border-top: solid 1px #f0f0f0;
}

.navigation-portrait .nav-menu>li:last-child {
  border-bottom: solid 1px #f0f0f0;
}

.nav-menu+.nav-menu>li:first-child {
  border-top: none;
}

.nav-padding {
  padding: 10px 80px;
  /* margin-top: 40px; */
}

.nav-menu>li>a {
  margin: 0px 10px;
  display: inline-block;
  text-decoration: none;
  transition: color 0.3s, background 0.3s;
  color: #ffffff;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 30px;
  letter-spacing: 1px;
  padding: 0px 0px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

.navigation-portrait .nav-menu>li>a {
  width: 100%;
  height: auto;
  padding: 12px 15px 12px 26px;
}

.nav-menu>li:hover>a,
.nav-menu>li.active>a,
.nav-menu>li.focus>a {
  /* color: #223260 !important; */
  /* border-top: 6px solid #223260; */
}

.nav-menu>li>a>i,
.nav-menu>li>a>[class*="ion-"] {
  width: 18px;
  height: 16px;
  line-height: 16px;
  transform: scale(1.4);
}

.nav-menu>li>a>[class*="ion-"] {
  width: 16px;
  display: inline-block;
  transform: scale(1.8);
}

.navigation-portrait .nav-menu.nav-menu-social {
  width: 100%;
  text-align: center;
}

.nav-menu.nav-menu-social>li {
  text-align: center;
  float: none;
  border: none !important;
}

.navigation-portrait .nav-menu.nav-menu-social>li {
  width: auto;
}

.nav-menu.nav-menu-social>li>a>[class*="ion-"] {
  font-size: 12px;
}

.nav-menu.nav-menu-social>li>a>.fa {
  font-size: 14px;
}

.navigation-portrait .nav-menu.nav-menu-social>li>a {
  padding: 15px;
}

.submenu-indicator {
  margin-left: 12px;
  margin-top: -7px;
  float: right;
  transition: all 0.2s;
  position: absolute;
  top: 50%;
  transform: translateX(-50%);
}

.navigation-portrait .submenu-indicator {
  /* width: 54px; */
  /* height: 44px; */
  margin-top: 20px;
  position: absolute;
  top: 0;
  right: 0;
  text-align: center;
  z-index: 20000;
  float: left;
}

.submenu-indicator-chevron {
  height: 8px;
  width: 8px;
  display: block;
  border-style: solid;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  transition: border 0.2s;
}

.home-5-header .submenu-indicator-chevron {
  border-color: #ffffff;
}

.home-5-header .nav-submenu .submenu-indicator-chevron {
  border-color: #223260;
}

.navigation-portrait .submenu-indicator-chevron {
  /* position: absolute; */
  /* top: 18px; */
  /* left: 24px; */
  border-color: #9f9f9f;
}

.nav-menu>li:hover>a .submenu-indicator-chevron,
.nav-menu>.active>a .submenu-indicator-chevron,
.nav-menu>.focus>a .submenu-indicator-chevron {
  border-color: #223260;
}

.navigation-portrait .submenu-indicator.submenu-indicator-up {
  /* transform: rotate(-180deg); */
  width: auto;
  height: auto;
}

.nav-overlay-panel {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: fixed;
  display: none;
  z-index: 19999;
}

.no-scroll {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.nav-search {
  height: 70px;
  float: right;
  z-index: 19998;
}

.navigation-portrait .nav-search {
  height: 48px;
  padding: 0 10px;
  margin-right: 52px;
}

.navigation-hidden .nav-search {
  display: none;
}

.nav-search-button {
  width: 70px;
  height: 70px;
  line-height: 70px;
  text-align: center;
  cursor: pointer;
  background-color: #fbfcfd;
}

.navigation-portrait .nav-search-button {
  width: 50px;
  height: 48px;
  line-height: 46px;
  font-size: 22px;
}

.nav-search-icon {
  width: 14px;
  height: 14px;
  margin: 2px 8px 8px 4px;
  display: inline-block;
  vertical-align: middle;
  position: relative;
  color: #343a40;
  text-align: left;
  text-indent: -9999px;
  border: 2px solid;
  border-radius: 50%;
  transform: rotate(-45deg);
}

.nav-search-icon:after,
.nav-search-icon:before {
  content: "";
  pointer-events: none;
}

.nav-search-icon:before {
  width: 2px;
  height: 11px;
  top: 11px;
  position: absolute;
  left: 50%;
  border-radius: 0 0 1px 1px;
  box-shadow: inset 0 0 0 32px;
  transform: translateX(-50%);
}

.nav-search-button:hover .nav-search-icon {
  color: #223260;
}

.nav-search>form {
  width: 100%;
  height: 100%;
  padding: 0 auto;
  display: none;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #fff;
  z-index: 99;
}

.nav-search-inner {
  width: 70%;
  height: 70px;
  margin: auto;
  display: table;
}

.navigation-portrait .nav-search-inner {
  height: 48px;
}

.nav-search-inner input[type="text"],
.nav-search-inner input[type="search"] {
  height: 70px;
  width: 100%;
  margin: 0;
  padding: 0 12px;
  font-size: 26px;
  text-align: center;
  color: #343a40;
  outline: none;
  line-height: 70px;
  border: none;
  background-color: transparent;
  transition: all 0.3s;
}

.navigation-portrait .nav-search-inner input[type="text"],
.navigation-portrait .nav-search-inner input[type="search"] {
  height: 48px;
  font-size: 18px;
  line-height: 48px;
}

.nav-search-close-button {
  width: 28px;
  height: 28px;
  display: block;
  position: absolute;
  right: 20px;
  top: 20px;
  line-height: normal;
  color: #343a40;
  font-size: 20px;
  cursor: pointer;
  text-align: center;
}

.navigation-portrait .nav-search-close-button {
  top: 10px;
  right: 14px;
}

.nav-button {
  margin: 18px 15px 0;
  padding: 8px 14px;
  display: inline-block;
  color: #fff;
  font-size: 14px;
  text-align: center;
  text-decoration: none;
  border-radius: 4px;
}

.nav-button:hover,
.nav-button:focus {
  color: #fff;
  text-decoration: none;
}

.navigation-portrait .nav-button {
  width: calc(100% - 52px);
  margin: 17px 26px;
}

.nav-text {
  margin: 25px 15px;
  display: inline-block;
  color: #343a40;
  font-size: 14px;
}

.navigation-portrait .nav-text {
  width: calc(100% - 52px);
  margin: 12px 26px 0;
}

.navigation-portrait .nav-text+ul {
  margin-top: 15px;
}

.nav-dropdown {
  margin: 0;
  padding: 0;
  /* display: none; */
  position: absolute;
  list-style: none;
  z-index: 98;
  white-space: nowrap;
  left: auto;
  top: 70px;
  background: rgb(255 255 255 / 92%);
  border: 0.875167px solid rgb(237 237 237);
  backdrop-filter: blur(50px);
  border-radius: 9px;
  padding: 19px;
  transition: opacity 2.5s ease-in-out;
  width: 300px;
}

.nav-dropdown img {
  width: 320px;
  height: 187px;
  position: absolute;
  right: 23px;
  top: 23px;
}

.navigation-portrait .nav-dropdown {
  width: 100%;
  position: static;
  left: 0;
  float: left;
  padding: 0px;
}

.nav-dropdown .nav-dropdown {
  left: 260px;
  position: absolute;
  background: none;
  top: 19px;
  padding: 0px;
  right: auto;
  border: none;
  backdrop-filter: inherit;
  min-width: auto;
  border-radius: inherit;
  height: 85%;
  border-left: 1px solid #dddddd;
  padding-left: 10px;
  transition: opacity 5.5s ease-in-out;
}

.nav-dropdown .nav-dropdown .nav-dropdown {
  left: 250px;
  position: absolute;
  background: none;
  top: 0px;
  padding: 0px;
  right: auto;
  border: none;
  backdrop-filter: none;
  width: 50%;
  min-width: auto;
  border-left: 1px solid #dddddd;
  padding-left: 10px;
}

.nav-dropdown>li {
  min-width: 240px;
  float: left;
  clear: both;
  /* position: relative; */
  text-align: left;
  /* display: flex; */
  flex-direction: row;
  padding-right: 10px;
  transition: opacity 5.5s ease-in-out;
}

.nav-dropdown>li>a {
  width: auto;
  padding: 16px 20px;
  display: inline-block;
  text-decoration: none;
  float: left;
  font-size: 15px;
  color: #343a40;
  width: 100%;
}

.nav-dropdown>li:hover>a,
.nav-dropdown>li.focus>a {
  color: #223260;
}

.nav-dropdown.nav-dropdown-left {
  right: 0;
}

.nav-dropdown>li>.nav-dropdown-left {
  left: auto;
  right: 100%;
}

.navigation-landscape .nav-dropdown.nav-dropdown-left>li>a {
  text-align: right;
}

.navigation-portrait .nav-dropdown>li>a {
  padding: 10px 20px 10px 20px;
  font-size: 18px;
  font-weight: 700;
}

.navigation-portrait .nav-dropdown>li>ul>li>a {
  padding-left: 50px;
}

.navigation-portrait .nav-dropdown>li>ul>li>ul>li>a {
  padding-left: 70px;
}

.navigation-portrait .nav-dropdown>li>ul>li>ul>li>ul>li>a {
  padding-left: 90px;
}

.navigation-portrait .nav-dropdown>li>ul>li>ul>li>ul>li>ul>li>a {
  padding-left: 110px;
}

.nav-dropdown .submenu-indicator {
  right: -5px;
  top: 12px;
  position: relative;
}

.navigation-portrait .nav-dropdown .submenu-indicator {
  right: 0;
  top: 0;
}

.nav-dropdown .submenu-indicator .submenu-indicator-chevron {
  transform: rotate(-45deg);
  border-color: #223260;
}

.navigation-portrait .nav-dropdown .submenu-indicator .submenu-indicator-chevron {
  transform: rotate(45deg);
}

.nav-dropdown>li:hover>a .submenu-indicator-chevron {
  border-color: transparent #223260 #223260 transparent;
}

.nav-dropdown>.focus>a .submenu-indicator-chevron {
  color: white !important;
}

.navigation-landscape .nav-dropdown.nav-dropdown-left .submenu-indicator {
  left: 10px;
}

.navigation-landscape .nav-dropdown.nav-dropdown-left .submenu-indicator .submenu-indicator-chevron {
  transform: rotate(135deg);
}

.nav-dropdown-horizontal {
  width: 100%;
  left: 0;
  background-color: #fdfdfd;
  border-top: solid 1px #f0f0f0;
}

.nav-dropdown-horizontal .nav-dropdown-horizontal {
  width: 100%;
  top: 100%;
  left: 0;
}

.navigation-portrait .nav-dropdown-horizontal .nav-dropdown-horizontal {
  border-top: none;
}

.nav-dropdown-horizontal>li {
  width: auto;
  clear: none;
  position: static;
}

.navigation-portrait .nav-dropdown-horizontal>li {
  width: 100%;
}

.nav-dropdown-horizontal>li>a {
  position: relative;
}

.nav-dropdown-horizontal .submenu-indicator {
  height: 18px;
  top: 11px;
  transform: rotate(90deg);
}

.navigation-portrait .nav-dropdown-horizontal .submenu-indicator {
  height: 42px;
  top: 0;
  -webkit-transform: rotate(0deg);
  transform: rotate(0deg);
}

.navigation-portrait .nav-dropdown-horizontal .submenu-indicator.submenu-indicator-up {
  transform: rotate(-180deg);
}

.megamenu-panel {
  width: 100%;
  padding: 15px;
  display: none;
  position: absolute;
  font-size: 14px;
  z-index: 98;
  text-align: left;
  color: inherit;
  border-top: solid 1px #f0f0f0;
  background-color: #fdfdfd;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.075);
}

.navigation-portrait .megamenu-panel {
  padding: 25px;
  position: static;
  display: block;
}

.megamenu-panel [class*="container"] {
  width: 100%;
}

.megamenu-panel [class*="container"] [class*="col-"] {
  padding: 0;
}

.megamenu-panel-half {
  width: 50%;
}

.megamenu-panel-quarter {
  width: 25%;
}

.navigation-portrait .megamenu-panel-half,
.navigation-portrait .megamenu-panel-quarter {
  width: 100%;
}

.megamenu-panel-row {
  width: 100%;
}

.megamenu-panel-row:before,
.megamenu-panel-row:after {
  content: "";
  display: table;
  line-height: 0;
}

.megamenu-panel-row:after {
  clear: both;
}

.megamenu-panel-row [class*="col-"] {
  display: block;
  min-height: 20px;
  float: left;
  margin-left: 3%;
}

.megamenu-panel-row [class*="col-"]:first-child {
  margin-left: 0;
}

.navigation-portrait .megamenu-panel-row [class*="col-"] {
  float: none;
  display: block;
  width: 100% !important;
  margin-left: 0;
  margin-top: 15px;
}

.navigation-portrait .megamenu-panel-row:first-child [class*="col-"]:first-child {
  margin-top: 0;
}

.megamenu-panel-row .col-1 {
  width: 5.583333333333%;
}

.megamenu-panel-row .col-2 {
  width: 14.166666666666%;
}

.megamenu-panel-row .col-3 {
  width: 22.75%;
}

.megamenu-panel-row .col-4 {
  width: 31.333333333333%;
}

.megamenu-panel-row .col-5 {
  width: 39.916666666667%;
}

.megamenu-panel-row .col-6 {
  width: 48.5%;
}

.megamenu-panel-row .col-7 {
  width: 57.083333333333%;
}

.megamenu-panel-row .col-8 {
  width: 65.666666666667%;
}

.megamenu-panel-row .col-9 {
  width: 74.25%;
}

.megamenu-panel-row .col-10 {
  width: 82.833333333334%;
}

.megamenu-panel-row .col-11 {
  width: 91.416666666667%;
}

.megamenu-panel-row .col-12 {
  width: 100%;
}

.megamenu-list>li>a span {
  display: block;
  margin: 0 auto;
  text-align: center;
  padding-top: 10px;
  font-size: 18px;
  font-weight: 600;
  color: #0A1D35;
}

.megamenu-list>li>a:hover span {
  color: #ffffff;
}

.megamenu-lists {
  width: 100%;
  display: table;
}

.megamenu-list {
  width: 100%;
  margin: 0 0 15px;
  padding: 0;
  display: inline-block;
  float: left;
  list-style: none;
}

.megamenu-list:last-child {
  margin: 0;
  border: none;
}

.navigation-landscape .megamenu-list {
  margin: -15px 0;
  padding: 20px 0;
}

.navigation-landscape .megamenu-list:last-child {
  border: none;
}

.megamenu-list>li>a {
  width: 100%;
  padding: 10px 15px;
  display: inline-block;
  color: #343a40;
  text-decoration: none;
  font-size: 13px;
}

.megamenu-list>li>a:hover {
  background-color: #223260;
  color: #fff;
}

.megamenu-list>li.megamenu-list-title>a {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: #343a40;
}

.megamenu-list>li.megamenu-list-title>a:hover {
  background-color: transparent;
}

.navigation-landscape .list-col-2 {
  width: 50%;
}

.navigation-landscape .list-col-3 {
  width: 33%;
}

.navigation-landscape .list-col-4 {
  width: 33.33%;
}

.page-header-v2 .list-col-4 {
  width: 33.33%;
}

.page-header-v2 .navigation-portrait .megamenu-panel {
  max-width: 1170px;
  margin: 0 auto;
}

.navigation-landscape .list-col-5 {
  width: 20%;
}

.nav-dropdown>li>a {
  color: #333;
  padding: 5px 7px;
}

.nav-dropdown>li.active>a {
  color: #223260;
}

.nav-dropdown>li>a:hover,
.nav-dropdown>li>a:focus {
  color: #223260;
}

.main_header_area.sticky {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: #fff;
  z-index: 9999;
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.1);
}

.transparent-menu {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
  z-index: 99;
}

.navigation-portrait .nav-menu>li>a {
  width: 100%;
  height: auto;
  padding: 10px 0px 10px 0px;
  color: #333;
  font-size: 21px;
}

.content-menu {
  position: relative;
}

.screen-menu {
  display: block;
  width: 100%;
  height: 350px;
  overflow: hidden;
  position: relative;
  margin: 0 auto;
}

.screen-menu-2 img {
  bottom: -1923px !important;
}

.screen-menu-3 img {
  bottom: -1645px !important;
}

.screen-menu-4 img {
  bottom: -2212px !important;
}

.screen-menu img {
  bottom: -1980px;
  width: 100%;
  height: auto;
  position: absolute;
  z-index: 0;
  margin: 0;
  padding: 0;
  -webkit-transition: top 5s;
  -moz-transition: top 5s;
  -ms-transition: top 5s;
  -o-transition: top 5s;
  transition: bottom 5s;
}

.screen-menu:hover img {
  bottom: 0 !important;
  -webkit-transition: all 5s;
  -moz-transition: all 5s;
  -ms-transition: all 5s;
  -o-transition: all 5s;
  transition: all 5s;
}

.page-header-v2 .navigation-portrait .nav-menus-wrapper {
  left: 0;
  width: 100%;
}

.page-header-v2 .navigation-portrait .nav-menus-wrapper {
  background-color: unset;
}

.page-header-v2 .navigation-portrait .nav-menu>li {
  border-top: solid 0px #f0f0f0;
}

.page-header-v2 .nav-menu>li {
  text-align: center;
}

.page-header-v2 .navigation-portrait .nav-menu>li:last-child {
  border: none;
  padding-left: 50px;
}

.page-header-v2 .navigation-portrait .nav-menu>li>a {
  width: auto;
  padding: 10px 0px 10px 0px;
  margin: 0 !important;
  position: relative;
}

.page-header-v2 .nav-dropdown>li>a {
  font-size: 24px;
  color: #FFF;
  text-align: center;
  background-color: unset;
  border: none;
  padding: 12px 20px 12px 20px;
}

.page-header-v2 .nav-menu>li>.nav-dropdown {
  border: none;
}

.page-header-v2 .submenu-indicator {
  display: none;
}

.page-header-v2 .pages_v2,
.page-header-v2 .pages_v3 {
  position: relative;
}

.page-header-v2 .pages_v2::after {
  font-family: "Font Awesome 5 Free";
  content: "\f107";
  font-weight: 900;
  position: absolute;
  left: 110%;
  padding-top: 5px;
}

.page-header-v2 .pages_v3::after {
  font-family: "Font Awesome 5 Free";
  content: "\f107";
  font-weight: 900;
  position: absolute;
  padding-left: 20px;
  padding-top: 5px;
}

.page-header-v2 .pages_v2.focus .pages_v2::after,
.page-header-v2 .focus .pages_v2::after,
.page-header-v2 .nav-submenu .focus>.pages_v3::after {
  content: "\f106";
}

.page-header-v2 .navigation-portrait .nav-dropdown>li>ul>li>a {
  padding: 12px 0px 12px 50px;
}

.page-header-v2 .nav-dropdown.nav-submenu li.focus>a {
  color: #223260;
}

.page-header-v2 .nav-dropdown.nav-submenu li a:hover {
  color: #223260;
}

/************************ 4. Hero section Home - V1 ***************************/
.hero-sec {
  position: relative;
}

.lawyer-col img {
  padding-top: 40px;
}

.hero-content-left {
  align-items: center;
  justify-content: center;
  display: flex;
}

.fade-in {
  animation: fadeIn ease 5s;
  -webkit-animation: fadeIn ease 5s;
  -moz-animation: fadeIn ease 5s;
  -o-animation: fadeIn ease 5s;
  -ms-animation: fadeIn ease 5s;
}

/* hero section version V3 */
.slider {
  position: relative;
}

#slider {
  position: relative;
  overflow: hidden;
  width: 100% !important;
  height: 84vh;
}

#slider ul {
  position: relative;
  margin: 0;
  padding: 0;
  height: 84vh;
  width: 99999px;
  overflow: hidden;
  list-style: none;
}

#slider ul li {
  position: relative;
  display: block;
  float: left;
  margin: 0;
  padding: 0;
  width: 100vw;
  height: 84vh;
  text-align: center;
}

#slider ul li .slide {
  background-size: cover;
  height: 100vh;
}

button.control_prev,
button.control_next {
  position: absolute;
  bottom: 0px;
  z-index: 10;
  border: none;
  width: 100px;
  height: 100px;
  background: #ffffff;
  text-align: center;
  text-decoration: none;
  font-weight: 600;
  font-size: 40px;
  cursor: pointer;
  transform: translateY(0);
  display: flex;
  justify-content: center;
  align-items: center;
}

button.control_prev:focus,
button.control_next:focus {
  outline: none;
  border: 1px rgba(255, 255, 255, 0.5) solid;
}

button.control_prev:hover,
button.control_next:hover {
  opacity: 1;
  -webkit-transition: all 0.2s ease;
}

button.control_prev {
  left: 0;
}

button.control_next {
  right: 0;
}




/* Version 4 */
.reveal img {
  object-fit: cover;
  transform-origin: left;
}

.reveal {
  visibility: hidden;
  position: relative;
  width: 100%;
  overflow: hidden;
}

.lawyers-advt {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.lawyers-advt li:before {
  content: "\eb7a";
  font-family: remixicon !important;
  color: #223260;
  font-size: 24px;
  margin-right: 15px;
}

.rotating-div {
  position: relative;
}

#circle {
  position: absolute;
  width: 200px;
  height: 200px;
  overflow: hidden;
  border-radius: 100px;
  background: linear-gradient(135deg, rgba(10, 29, 53, 0.40) 0%, rgba(10, 29, 53, 0.20) 100%);
  backdrop-filter: blur(7.5px);
  bottom: 60px;
  left: -95px;
}

#circle svg {
  position: absolute;
  left: 0;
  top: 18px;
  width: 100%;
  -webkit-animation-name: rotate;
  -moz-animation-name: rotate;
  -ms-animation-name: rotate;
  -o-animation-name: rotate;
  animation-name: rotate;
  -webkit-animation-duration: 10s;
  -moz-animation-duration: 10s;
  -ms-animation-duration: 10s;
  -o-animation-duration: 10s;
  animation-duration: 10s;
  -webkit-animation-iteration-count: infinite;
  -moz-animation-iteration-count: infinite;
  -ms-animation-iteration-count: infinite;
  -o-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  -moz-animation-timing-function: linear;
  -ms-animation-timing-function: linear;
  -o-animation-timing-function: linear;
  animation-timing-function: linear;
}

.inner-blue {
  width: 100px;
  height: 100px;
  border-radius: 50px;
  position: absolute;
  text-align: center;
  left: 50%;
  transform: translate(-50%, -50%);
  top: 50%;
  background-image: url('../images/client/arrowin-rotate.png');
  background-repeat: no-repeat;
  background-position: center center;
}

/************************ 6. Features css ***************************/
.feature-section {
  border-top: 2px solid #45566C;
  border-bottom: 2px solid #45566C;
}

.feature-wrap img {
  margin-right: 20px;
}

.most-reliable-col {
  border-left: 2px solid #45566C;
  border-right: 2px solid #45566C;
}

.feature-wrapper {
  padding: 40px;
}

/************************ 7. What we Offer css ***************************/
.offer-section.services-offer {
  background-color: #0A1D35;
}

.offer-wrap a.offer-links,
.offer-post-box {
  font-weight: 400;
  font-size: 48px;
  line-height: 64px;
  text-align: center;
  color: #FFFFFF;
  padding: 0 20px;
}

.offer-wrap a.offer-links:hover {
  color: #223260;
}

.offer-post-box .blog-post-box {
  display: contents;
}

.offer-wrap h6 {
  color: #94B0D5;
}


.close {
  position: absolute;
  top: -50px;
  right: 0px;
  cursor: pointer;
  z-index: 9999;
  height: 40px;
  width: 40px;
  background-size: 40px;
}

.close {
  background: #223260;
}

.close:hover {
  background: #0A1D35;
}

.close:after,
.close:before {
  content: "" "";
  width: 55%;
  height: 2px;
  background: #fafafa;
  position: absolute;
  top: 48%;
  left: 22%;
  transform: rotate(-45deg);
  transition: 0.3s ease-out;
}

.close:after {
  transform: rotate(45deg);
  transition: 0.3s ease-out;
}

.close:hover:after,
.close:hover:before {
  transform: rotate(180deg);
}

.home6-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.form-button.nopadding.mt-home3 {
  margin-top: 0 !important;
}



::-webkit-scrollbar {
  display: none;
}




/************************ 18. Footer Top ***************************/
footer,
.footer-top {
  overflow: hidden;
}

.newsletter-footer h2 {
  color: #0A1D35;
  max-width: 1170px;
  margin-right: auto;
  margin-left: auto;
}


/************************ 19. Footer ***************************/
.footer-block-home-1 {
  padding: 50px 0 40px;
  background: #fcfcfc;
}

.footer-list {
  margin-bottom: 10px;
  margin-right: 0;
  color: #223260;
  font-size: 18px;
  font-weight: 600;
}

.footer-V2 .footer-list {
  margin-bottom: 30px;
}

.footer-menu-list {
  line-height: 38px;
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 13px;
  margin-bottom: 20px;
}

.footer-menu-list a,
.footer-bottom-copyright a,
.hover-effect-blog a {
  color: #94B0D5;
  position: relative;
}

.footer-menu-list a:before,
.footer-bottom-copyright a:before,
.hover-effect-blog a:before {
  color: #223260;
  content: attr(data-hover);
  position: absolute;
  opacity: 0;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.3);
  -webkit-transform: scale(1.1) translateX(10px) translateY(-10px) rotate(4deg);
  -moz-transform: scale(1.1) translateX(10px) translateY(-10px) rotate(4deg);
  transform: scale(1.1) translateX(10px) translateY(-10px) rotate(4deg);
  -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
  -moz-transition: -moz-transform 0.3s, opacity 0.3s;
  transition: transform 0.3s, opacity 0.3s;
  pointer-events: none;
}

.footer-menu-list a:hover:before,
.footer-menu-list a:focus:before,
.footer-bottom-copyright a:hover:before,
.footer-bottom-copyright a:focus:before,
.blog__box:hover a:before,
.blog__box:focus a:before {
  -webkit-transform: scale(1) translateX(0px) translateY(0px) rotate(0deg);
  -moz-transform: scale(1) translateX(0px) translateY(0px) rotate(0deg);
  transform: scale(1) translateX(0px) translateY(0px) rotate(0deg);
  opacity: 1;
}

.footer-bottom-bar hr {
  margin: 20px 0 20px 0;
  border-color: #45566C;
  /* opacity: 1 !important; */
}

.footer-bottom-copyright,
.footer-bottom-copyright a {
  font-size: 16px;
  color: #94B0D5;
  transition: all .5s;
}

.footer-menu-last-list a,
.contact-information a {
  color: #94B0D5;
  transition: all .5s;
}

.footer-menu-last-list a:hover,
.contact-information a:hover {
  color: #223260 !important;
  transition: all .5s;
}

/* Footer version 2 */
.footer-V2 .wrapper .social {
  display: inline-block;
}

.footer-V2 ul.social li {
  display: inline-block;
  width: 50%;
  float: left;
}

.footer-V2 ul.social li:nth-child(1),
.footer-V2 ul.social li:nth-child(2) {
  margin-bottom: 20px;
}

.footer-menu-last-list.menu-v2 a {
  color: #94B0D5;
}

.footer-menu-last-list.menu-v2 a:hover {
  color: #223260;
}

/* Footer V3 */
.footer-V3 .newsletter .outer,
.footer-V3 .wrapper .social li .social-item {}

.footer-V3 .newsletter .inner {
  background-color: #0A1D35;
}

.footer-V3 .newsletter input::placeholder,
.footer-V3 .footer-menu-list a,
.footer-V3 .footer-bottom-copyright a,
.footer-V3 .hover-effect-blog a,
.footer-V3 .footer-menu-last-list a,
.footer-V3 .wrapper .social li .social-item i {
  color: #223260;
}

.footer-menu-last-list a .footer-V3 .wrapper .social li:hover .social-item {
  background-color: #223260;
  border-color: transparent;
}

.footer-V3 .wrapper .social li:hover .social-item i {
  color: #ffffff;
}

.footer-V3 h1 {
  color: #898A8D;
  font-size: 20px;
  font-weight: 600;
  line-height: 40px;
}

.footer-V3 p {
  color: #898A8D;
  font-size: 14px;
}

.footer-V3 i {
  color: #223260;
}

.footer-V3 hr {
  border-top: 1px solid #979797;
}

.footer-V3 .footer-bottom-copyright {
  color: #333;
  font-size: 13px;
}

.footer-V3 .footer-bottom-copyright a {
  color: #333;
  font-size: 13px;
}

.footer-V3 .wrapper {
  margin-top: 20px;
}

.wrapper .social {
  display: flex;
  justify-content: center;
  list-style: none;
  margin: 0px;
  padding: 0px;
  gap: 3px;
}

.wrapper .social li .social-item {
  display: table;
  color: #fff;
  background: #223260;
  width: 35px;
  height: 35px;
  font-size: 22px;
  text-align: center;
  border: 1px solid #fff;
  transition: all 0.3s ease-in-out;
  text-decoration: none;
  border-radius: 50%;
}

.wrapper .social li .social-item i {
  display: table-cell;
  vertical-align: middle;
  color: #ffffff !important;!i;!i4;!i;!;
  font-size: 16px;
}

.wrapper .social li:hover .social-item {
  background-color: rgb(237, 58, 66);
  border-color: transparent !important;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: #ffffff;
  box-shadow: 2px 2px 2px 1px #45566c14;
}

.home-1-header.fixed-header,
.page-header-v2.fixed-header {
  background: #0A1D35;
}

.header-top-bar {
  padding: 9px 50px;
}

.top-bar-img a {
  padding-left: 35px;
  position: relative;
  color: #ffffff;
}

.top-bar-text a {
  color: #223260;
  text-decoration: underline;
}

.top-bar-text a:hover {
  color: #ffffff;
}

.top-bar-img a::before {
  background: url(../images/header/dial-pad.png);
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  left: 0;
  top: 3px;
}

.page-header-v3 {
  padding: 0 !important;
}

.top-bar-mail a::before {
  background: url(../images/header/mail.png);
}

.page-header-v3 .form-button {
  margin-top: 0px;
}

.form-button {
  margin-top: 30px !important;
}

.page-header-v4 {
  border: none;
}

/* overlay menu */
.menu.header-2-overlay {
  width: 100%;
  height: 100vh;
  background: none;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 1;
  visibility: visible;
  position: unset;
}

.menu.header-2-overlay ul {
  list-style: none;
  margin: 0;
  padding: 0;
  text-align: center;
}

.menu.header-2-overlay ul li a {
  padding-left: 0px;
  position: relative;
  font-size: 53px;
  line-height: 60px;
}

.menu.header-2-overlay>ul>li>a:after {
  position: absolute;
  top: 10px;
  right: -50px;
}

.menu.header-2-overlay>ul>li:last-child a {
  border-bottom: none;
}

.caret:after {
  content: "\ea4e";
  font-family: remixicon !important;
  color: #fff;
}

.menu.header-2-overlay ul ul {
  display: none;
}

.menu.header-2-overlay ul ul a {
  text-transform: none;
  font-size: 24px;
}

.page-header-v2 .carbon-blue {
  background: transparent;
}

.page-header-v2 .nav-menu>li>a {
  font-size: 53px;
}

.page-header-v2 ul.nav-menu.align-to-right {
  gap: 80px;
  align-items: center;
  height: 100%;
}

.page-header-v2 .nav-menu>li>a {
  color: #FFFFFF;
}

.page-header-v2 .submenu-indicator {
  margin-left: 25px;
  margin-top: -10px;
}

.page-header-v2 .submenu-indicator-chevron {
  height: 20px;
  width: 20px;
  border-color: #FFFFFF;
}

.page-header-v2 .navigation {
  justify-content: center;
  max-width: 1655px;
  margin: 0 auto;
}

.page-header-v2 .nav-menu>li:hover>a,
.page-header-v2 .nav-menu>li.active>a,
.page-header-v2 .nav-menu>li.focus>a,
.page-header-v2 .nav-menu>li>a {
  border-top: none
}

.page-header-v2 .nav-dropdown li a .submenu-indicator-chevron {
  border-color: #000;
  height: 10px;
  width: 10px;
}

.page-header-v2 .nav-dropdown li a .submenu-indicator {
  margin-top: -2px;
}

.page-header-v2 .nav-dropdown li a .submenu-indicator-chevron {
  border-color: #FFF;
  height: 16px;
  width: 16px;
}

/************************ 21. Slider V2 ***************************/
.hero-sec-v2 {
  /* height: 100vh; */
}

.slider-v2 {
  position: relative;
  height: 100%;
}



.slider-v2 .hero-content {
  max-width: 380px;
  width: 100%;
  position: absolute;
  bottom: 150px;
  left: 90px;
}

.slider-v2 .hero-content h1 {
  color: #FFFFFF;
  font-size: 34px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 10px;
  position: relative;
  letter-spacing: 1px;
}

.slider-v2 .hero-content a {
  color: #FFFFFF;
  font-size: 12px;
}

.slider--control i {
  color: #ffffff;
  font-size: 30px;
  position: relative;
  z-index: 99999999;
}



/************************ 27. Header banner inner page  ***************************/
.banner-header-section .page-thumbnails {
  display: flex;
  justify-content: center;
  position: absolute;
  overflow: hidden;
  padding: 30px 0px;
  bottom: 0px;
  width: 100%;
  left: 0px;
}

.banner-header-section .black-overlay {
  text-align: center;
  color: #fff;
  width: 100%;
  height: 100%;
}

.banner-header-section h1.page-title {
  color: #223260;
  font-size: 35px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.banner-header-section span {
  color: #223260;
  font-size: 25px;
  font-weight: 700;
}

.banner-header-content {
  position: relative;
  width: 100%;
  padding: 30px 65px;
}

.banner-header-section .black-overlay,
.banner-header-section h1.page-title {
  transition: opacity .3s;
}

.breadcrumb li a,
.breadcrumb li {
  cursor: pointer;
  color: #ffffff;
  font-size: 15px;
  margin-right: 3px;
}



/************************ 31. Error page CSS ***************************/
.error h6 {
  font-weight: 500;
  line-height: normal;
  text-transform: none;
  letter-spacing: normal;
}




/************************ 33. Single Services CSS ***************************/
.single-content-36 {
  font-size: 36px;
  line-height: normal;

}

.single-content-30 {
  color: #0A1D35;

  font-size: 30px;
  line-height: normal;
}

.arrow-right,
.arrow-left {
  display: block;
}

.slick-custom-arrow {
  position: absolute;
  transform: translateY(-50%);
  z-index: 1;
  bottom: 120px;
}

.slick-custom-arrow-left {
  right: 140px;
}

.slick-custom-arrow-right {
  right: 65px;
  bottom: 120px;
}


.news_slider .slick-custom-arrow {
  position: absolute;
  transform: translateY(-50%);
  z-index: 1;
  bottom: 160px;
}

.news_slider .slick-custom-arrow-left {
  right: 80px;
}

.news_slider .slick-custom-arrow-right {
  right: 25px;
  bottom: 160px;
}

.slick-custom-arrow i {
  font-size: 60px;
  line-height: normal;
  color: #0A1D35;
  position: relative;
  top: 26px;
}

.footer-menu-list.single-services-list a:hover:before,
.footer-menu-list.single-services-list a:focus:before {
  left: 28px;
}

.footer-menu-list.single-services-list li a i {
  margin-right: 10px;
}

.footer-menu-list.single-services-list li:hover i {
  color: #223260;
}

.right-bar-services {
  padding: 30px;
  background: #F1F5F9;
}

.right-bar-services .footer-list {
  margin-bottom: 10px;
}

.download-pdf {
  padding: 30px;
}

.download-btn {
  color: #94B0D5;
  font-size: 16px;
  margin-top: 5px;
}

.download-btn:hover {
  color: #223260;
}

.download-icon {
  width: 54px;
  height: 54px;
  float: right;
  position: relative;
  top: -26px;
}


/************************ 37. Contact Us CSS ***************************/
.calls p a {
  color: #45566C;
}

.img-calls {
  margin-right: 20px;
}

.map iframe {
  -webkit-filter: grayscale(100%);
}

/************************ 38. Bottom to Top Button ***************************/
.back-to-top,
.back-to-top::after,
.back-to-top-text {
  transition: all 0.25s ease-in-out;
}

.back-to-top,
.back-to-top::after {
  position: fixed;
  height: 50px;
  width: 50px;
  color: #FFF;
  opacity: 0.75;
  padding: 3px 5px;
  font-size: 12px;
  font-weight: bold;
  border: 1px solid #223260;
}

.back-to-top {
  right: 2.5%;
  bottom: -12%;
  z-index: 3;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  background: #223260;
  border-radius: 100%;
}

.back-to-top-text {
  color: #fff;
  font-size: 19px;
  line-height: 30px;
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.back-to-top:hover,
.back-to-top:hover::after,
.back-to-top:hover .back-to-top-text {
  opacity: 1;
}

.show-back-to-top {
  bottom: 4%;
}

.row.sub_class_card.attorneys-1 {
  gap: 30px 0px;
}

.home-2-case-card {
  max-width: 100% !important;
  width: 100% !important;
}

.error-text {
  color: red;
}

.case-center {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
}

.case-back-btn {
  border: 2px solid #223260 !important;
  background: transparent !important;
  color: #223260 !important;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  width: 136px !important;
  height: 60px;
}

.case-btn1 {
  display: flex;
  justify-content: center;
  column-gap: 30px;
}

.blog1-iframe1 {
  width: 770px;
  height: 450px;
}

/************************ Preloader Css ***************************/
.preloader {
  background-color: #fff;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 99999;
  overflow: hidden;
}

.preloader .vertical-centered-box {
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center;
}

.preloader .vertical-centered-box:after {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -0.25em;
}

.preloader .vertical-centered-box .content {
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  text-align: left;
  font-size: 0;
}

.preloader * {
  transition: all 0.3s;
}

.preloader .loader-circle {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  -webkit-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  margin-left: -60px;
  margin-top: -60px;
}

.preloader .loader-line-mask {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 60px;
  height: 120px;
  margin-left: -60px;
  margin-top: -60px;
  overflow: hidden;
  transform-origin: 60px 60px;
  -webkit-mask-image: -webkit-linear-gradient(top, #000000, rgba(0, 0, 0, 0));
  animation: rotate1 1.2s infinite linear;
}

.preloader .loader-line-mask .loader-line {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5);
}

.preloader #particles-background,
.preloader #particles-foreground {
  left: -51%;
  top: -51%;
  width: 202%;
  height: 202%;
  transform: scale3d(0.5, 0.5, 1);
}

.preloader #particles-background {
  background: #2c2d44;
  background-image: linear-gradient(45deg, #3f3251 2%, #002025 100%);
}

.preloader lesshat-selector {
  -lh-property: 0;
}

.preloader [not-existing] {
  zoom: 1;
}

.preloader lesshat-selector {
  -lh-property: 0;
}

.preloader [not-existing] {
  zoom: 1;
}

.preloader lesshat-selector {
  -lh-property: 0;
}


.main-body {
  animation: mainZoom 2s ease forwards;
}
.nav-brand img {
  animation: logoZoom 3s ease forwards;
}
.preloader img {
  animation: preloaderZoom 0.5s ease forwards;
}

@keyframes preloaderZoom {
  0% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0);
  }
  
  80% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0);
  }
  
  100% {
    opacity: 0;
    transform: scale(2.2);
    filter: blur(2px);
  }
}
@keyframes logoZoom {
  0% {
    opacity: 0;
    transform: scale(2.2);
    filter: blur(2px);
  }
  
  80% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0);
  }
  
  100% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0);
  }
}
@keyframes mainZoom {
  0% {
    opacity: 0;
    transform: scale(1.1);
    filter: blur(2px);
  }
  
  80% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0);
  }
  
  100% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0);
  }
}

/************************ Menu Css ***************************/
ul,
ol {
  list-style-type: none;
}

.home6-navbar {
  background: white;
  box-shadow: 0px 5px 15px 0px rgba(212, 201, 201, 0.75);
  width: 100%;
  height: calc(100vh - 110px);
}

.logo a {
  font-size: 20px;
  font-weight: 700;
  color: #353535;
  text-transform: uppercase;
}

.main_menu>ul>li {
  position: relative;
  margin: 0 -2px;
}

.main_menu ul li {
  position: relative;
}

.main_menu ul li a {
  padding: 20px 25px;
  display: block;
  color: #0A1D35;

  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
}

.main_menu ul li .active,
.main_menu ul li:hover>a {
  color: #223260;
}

.main_menu ul li ul {
  width: 200px;
  background: #fff;
  transition: 0.5s;
  box-shadow: 0px 5px 15px 0px rgba(212, 201, 201, 0.75);
}

.main_menu ul li ul li a {
  padding: 10px 10px;
  font-size: 15px;
}

.main_menu ul li ul li a i {
  float: right;
}

.mega_menu_dropdown {
  position: static !important;
}

.mega_menu {
  left: 0;
  right: 0;
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  transition: 0.5s;
  box-shadow: 0px 5px 15px 0px rgba(212, 201, 201, 0.75);
}

.mega_menu_item {
  width: 25%;
  padding: 30px 20px;
}

.main_menu ul li .mega_menu_item a {
  padding: 10px 0;
}

.main_menu ul li .mega_menu_item a:hover {
  color: var(--hover-color);
}

.mega_menu_item h3 {
  margin-bottom: 15px;
}

.mega_menu_item img {
  width: 100%;
}

.mega_menu_demo_2 .mega_menu {
  left: 50%;
  transform: translateX(-50%);
  width: 1140px;
}

.mobile_btn {
  display: none;
}

.main_menu ul li ul {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  margin-top: 50px;
  display: none;
}

.main_menu ul li .mega_menu {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  margin-top: 50px;
}

.offcanvas-body .home6-navbar {
  overflow-y: auto !important;
}

.main_menu ul {
  padding-left: 20px !important;
}

.underline1 {
  background: linear-gradient(90deg, #223260, #223260);
  background-size: 100% 3px, 0 3px;
  background-repeat: no-repeat;
  background-position: 100% 100%, 0 100%;
  transition: 0.5s all;
  color: #223260;
}

.site-content {
  position: relative;
  overflow: hidden;
}

.nav-menu li a.active {
  color: yellow;
}



.about-us-home {
  position: relative;
}

.about-us-home span {
  font-weight: 400;
  font-size: 14.5723px;
  line-height: 50px;
  letter-spacing: 0;
  color: #ffffff;
  margin-top: 100px;
}

.about-us-home h1 {
  position: relative;
  font-size: 37px;
  color: #414141;
  line-height: 50px;
  margin-bottom: 15px;
  font-weight: 200;
}

.about-us-home h1 strong {
  position: relative;
  color: #F62D32;
  display: block;
  width: 100%;
  font-weight: 700;
}

.about-us-home p {
  position: relative;
  font-size: 15px;
  color: #414141;
  font-weight: 400;
  line-height: 33px;
  margin-bottom: 0px;
}

.about-home-content {
  /* margin-top: 100px; */
}

.about-us-home .home-aboutus-button:before {
  content: "";
  background-image: url(../images/arrow_home.png);
  margin-right: 10px;
  width: 36px;
  height: 12px;
  display: inline-block;
}

.title-sections-home {
  font-size: 35px;
  color: #000000;
  /* padding: 30px 0px; */
  /* margin-top: 50px; */
  font-weight: 700;
  line-height: 45px;
  margin-bottom: 50px;
}

.title-sections-home:before {
  width: 15px;
  height: 15px;
  background-color: #223260;
  display: inline-block;
  margin-right: 10px;
}


@keyframes ripple {
  0% {
    box-shadow: 0 0 0 .1rem rgb(255 255 255 / 68%);
  }

  100% {
    box-shadow: 0 0 0 1rem rgb(255 255 255 / 8%);
  }
}


.aboutdetail h2 strong {
  color: #223260;
}

.about-counter {
  position: relative;
  width: 100%;
  padding: 100px 160px;
  background: url(../images/aboutbonusbg.svg) no-repeat center 20px;
  background-size: contain;
}

.detail_content {
  font-size: 15px;
  padding: 40px;
  line-height: 28px;
}

.detail_content h1 {
  color: #223260;
  font-size: 70px;
  line-height: normal;
  font-weight: bold;
}

.detail_content h2 {
  color: #223260;
  font-size: 40px;
  line-height: normal;
}

.detail_content p {
  color: #7E7E7E;
  font-size:23px;
  line-height: 35px;
}

.detail_content ul {
  list-style-type: disc;
}

.detail_content .before01:before {
  content: '';
  width: 330px;
  height: 120px;
  background: url(../images/01.svg) no-repeat;
  background-size: cover;
  display: block;
  position: relative;
  top: 30px;
}

.detail_content .before02:before {
  content: '';
  width: 250px;
  height: 120px;
  background: url(../images/02.svg) no-repeat;
  background-size: cover;
  display: block;
  position: relative;
  top: 30px;
}

.detail_content .before03:before {
  content: '';
  width: 250px;
  height: 120px;
  background: url(../images/03.svg) no-repeat;
  background-size: cover;
  display: block;
  position: relative;
  top: 30px;
}

.detail_content .before04:before {
  content: '';
  width: 250px;
  height: 120px;
  background: url(../images/04.svg) no-repeat;
  background-size: cover;
  display: block;
  position: relative;
  top: 30px;
}

.detail_content .before05:before {
  content: '';
  width: 370px;
  height: 120px;
  background: url(../images/05.svg) no-repeat;
  background-size: cover;
  display: block;
  position: relative;
  top: 30px;
}



.referans_detay {
  position: relative;
  margin-bottom: 30px;
  overflow: hidden;
  border-radius: 10px;
}

.referans_detay .title {
  position: absolute;
  bottom: 0px;
  padding: 40px;
  z-index: 99;
}

.referans_detay img {
  width: 100%;
}

.referans_detay .title h3 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
}

.referans_detay .title span {
  color: #223260;
  font-size: 20px;
  font-weight: 600;
}

.referans_detay a {
  overflow: hidden;
}

.referans_detay a img {
  transition: transform 3.5s ease;
}

.referans_detay a:hover img {
  transform: scale(1.2);
}

.referans_detay a:after {
  content: "";
  background: -webkit-gradient(linear, left top, left bottom, color-stop(35.63%, rgba(3, 36, 107, 0)), to(#000000));
  background: -webkit-linear-gradient(top, rgba(3, 36, 107, 0) 35.63%, #000000);
  background: -moz-linear-gradient(top, rgba(3, 36, 107, 0) 35.63%, #223260 100%);
  background: linear-gradient(180deg, rgba(3, 36, 107, 0) 35.63%, #000000);
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 0px;
  left: 0px;
}

.section {
  transition: transform 3.5s ease;
}

.projeler .page-title {
  color: #223260;
  font-size: 35px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  padding: 20px 0px;
  border-bottom: 1px solid #223260;
  margin-bottom: 20px;
}

.products-detail {
  background: #f9f9f9;
}

.slider-nav .items {
  background: #f9f9f9;
}



.contact_list {
  width: 100%;
  padding: 25px;
  border-radius: 10px;
  border: 1px solid #F0F0F0;
}

.contact_list h4 {
  width: 100%;
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.contact_list p {
  font-size: 14px;
  color: #383938;
  line-height: 23px;
}

.contact_list i {
  font-size: 25px;
  color: #223260;
}

.form-contact {
  padding: 0px;
}

.sayfalamaStyle {
  width: 100%;
  display: flex;
  text-align: center;
  margin-top: 20px;
  justify-content: center;
}

.sayfalamaStyle a {
  padding: 10px 13px 13px 10px;
  margin: 0 2px;
  height: 30px !important;
  display: inline-block;
  box-sizing: border-box;
  background: #f3f3f3;
  color: #353535;
  line-height: 12px;
  font-size: 15px;
}

.sayfalamaStyle a:hover {
  text-decoration: none;
  background: #f39c12;
}

.sayfalamaStyle a.activeSayfa {
  background: #223260;
  text-decoration: none;
  color: #fff;
}

.sayfalamaStyle a.number {
  height: 25px;
  display: inline-block;
  text-align: center;
}

.searchBar {
  width: 100%;
  height: 100%;
  position: fixed;
  background-color: #223260c9;
  z-index: 99999999;
  padding: 50px 20px;
  display: none;
  overflow-x: auto;
}

.searchBar .close {
  position: fixed;
  top: 10px;
  right: 10px;
  color: #fff;
  font-size: 30px;
}

.searchBar h6 {
  color: #fff;
  font-weight: bold;
  font-size: 25px;
  margin: 20px 0 0 0;
}

.searchBar hr {
  border: none;
  border-top: #fff 1px dotted;
  margin: 10px 0 20px 0;
}

.searchBar ul,
li {
  margin: 0 0 0 0;
  padding: 0;
}

.searchBar li {
  list-style-type: none;
  margin: 10px 0 0 0px;
  color: #fff;
}

.searchBar li:hover {
  color: #fff;
}

.searchBar li a {
  color: #fff;
  font: normal 14px 'Poppins', sans-serif;
  text-decoration: none;
}

.searchBar li a:hover {
  color: #fff;
  font: normal 14px 'Poppins', sans-serif;
  text-decoration: none;
}

.searchBar input[type=text] {
  width: 100%;
  background: none;
  outline: none;
  border: none;
  border-bottom: #fff 4px solid;
  padding: 30px 10px;
  color: #fff;
  font: normal 30px 'Poppins', sans-serif;
}

.searchBar input[type=text]::-webkit-input-placeholder {
  color: #fff;
}

.searchBar input[type=text]:-moz-placeholder {
  color: #fff;
}

.searchBar input[type=text]::-moz-placeholder {
  color: #fff;
}

.searchBar input[type=text]:-ms-input-placeholder {
  color: #fff;
}

#targetDiv {
  transition: height 0.3s ease;
}

.image-holder img {
  transition: opacity 0.3s ease;
  opacity: 1;
}

.image-holder img.fade-out {
  opacity: 0;
}

.table {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
}

.slider_mobil {
  display: none;
}

.slider--el video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -2;
  position: absolute;
}
  .ust_menu a{
    font-size:12px !important;
  }




.info-box {
  border-left: 3px solid #223260;
  padding-left: 15px;
}

.info-title {
  font-weight: 600;
  color: #1b0a2a;
}

.info-text {
  color: #7a7a7a;
}

.btn-red {
  background-color: #223260;
  color: white;
  border-radius: 8px;
  padding: 0px 20px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 14px;
}
.btn-red:hover {
  background-color: #223260;
  color: white;
}



.header_img {
  width: 100%;
  position: relative;
  top: 0;
  left: 0;
  z-index: -1;
}

.detail_content p strong {
  color: #223260;
  font-size: 19px;
}


.accordion-button:not(.collapsed) {
    color: #fff;
    background-color:#223260;
    border-color: inherit;
}

.accordion-item:first-of-type .accordion-button {
    border:none;
    border-top-left-radius: inherit;
    border-top-right-radius: inherit;
}

.accordion-button {
  background-color: inherit;
}

.accordion-item:first-of-type {
   
}
.accordion-item:last-of-type .accordion-button.collapsed {
    border-radius: 20px;
}

.accordion-button::after {
  content: "";
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 16 16'><path fill-rule='evenodd' d='M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8'/></svg>");
  background-repeat: no-repeat;
  background-size: 20px;
  background-position: center;
  width: 30px;
  height: 30px;
  background-color: #223260;
  border-radius: 50%;
  padding: 20px;
}
.accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 16 16'><path fill-rule='evenodd' d='M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8'/></svg>");
  transform:rotate(-270deg);
}

.accordion-item .accordion-button span::before{
    content: "";
    width: 15px;
    height: 15px;
    background-color: #223260;
    display: inline-block;
    margin-right: 5px;
}

.slider_animation_text {
  background: url('../images/slider_down.svg') 100% no-repeat;
  padding:50px 0px;
  margin-top: -130px;
  z-index: 9999;
  position: relative;
  background-position: center top;
  padding-bottom: 0px;
  background-size: cover;
}
.slider_animation_text h2{
    font-style: normal;
    font-weight: 500;
    font-size: 40px;
    line-height: normal;
    text-align: center;
    color: #ffffff40;
    padding: 70px 250px;
}

.slider_animation_text h2 span {
  display: inline-block; 
  transition: opacity 0.1s ease;
}
.slider_animation_text h2 span.space {
  width: 9px;
}

.custom-tabs {
  display: flex;
  border-bottom: none;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  padding: 0px 0;
  flex-direction: row;
  flex-wrap: nowrap;
  cursor: grab;
  transform: translateX(-100px);
}
.custom-tabs.dragging {
  cursor: grabbing;
}
.custom-tabs .nav-link {
  font-size: 88px;
  font-weight: 700;
  color: #ccc;
  white-space: nowrap;
  margin: 0 0px;
  background: none;
  border: none;
  padding: 40px;
}
.custom-tabs .nav-link.active {
  color: #b00000;
}
.custom-tabs::-webkit-scrollbar {
  display: none;
}
.tab-pane {
  padding-top: 20px;
}

.slick-dots {
  display: flex !important;
  justify-content: center;
  align-items: center;
  list-style: none;
  margin-top: 15px;
  padding: 0;
}
.slick-dots li {
  margin: 0 6px;
  list-style-type: none;
}

.slick-dots li button {
  width: 24px;
  height: 24px;
  border: 1px solid #223260;
  border-radius: 50%;
  background: transparent;
  padding: 0;
  position: relative;
  cursor: pointer;
  font-size: 0px;
}

.slick-dots li button:before {
  content: "";
  display: block;
  width: 8px;
  height: 8px;
  background-color: #223260;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
}

.slick-dots li.slick-active button:before {
  opacity: 1;
}


.okullar_slider .slide-item h3{
  font-size: 24px;
  color:#C12129;
  font-weight: 600;
  margin-bottom: 30px;
}
.okullar_slider .slide-item p{
  font-size: 19px;
  color:#7E7E7E;
  font-weight: 500;
  line-height: 30px;
}
.okullar_slider .slide-item a{
  font-size:13px;
  font-weight: 600;
}
.okullar_slider .slide-item a i{
  font-size:13px;
  font-weight: 500;
  margin-right: 4px;
}


.slick-slide {
  outline: none;
}

.slick-cloned button {
  pointer-events: none;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

.tab-pane.active .slick-slide {
  width: auto;
}

.ayricalik a {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  display: flex;
}
.ayricalik img {
  transition: transform 3.5s ease, background 0.5s ease;
}
.ayricalik a span {
  position: absolute;
  top:0px;
  background: #081a4e9e;
  display:flex;
  align-items: center;
  justify-content: center;
  color: white;
  overflow: hidden;
  font-size: 18px;
  font-weight: 600;
  padding: 10px;
  text-align: center;
  border-radius: 10px;
  width: 100%;
  height: 100%;
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 3.5s ease, background 0.5s ease;
}
.ayricalik a span:hover {
  background: #00144e6b;
  transition: transform 3.5s ease, background 0.5s ease;
}
.ayricalik a:hover img{
  transform: scale(1.5);
  transition: transform 3.5s ease, background 0.5s ease;
}

.ayricalik span a{
  color: white;
}


.veliler .sol h3{
  font-size: 19px;
  color: #C12129;
  font-weight: 600;
  margin-bottom: 10px;
  line-height: 33px;
}
.veliler .sol p{
  font-size: 14px;
  color:#7E7E7E;
  font-weight: 500;
  line-height: 20px;
}
.veliler .sol a{
  font-size:13px;
  font-weight: 600;
}

.veliler .sag {
  padding: 50px;
  background:#F7F7F6;
  border-radius: 10px;
}
.veliler .sag h3{
  font-size: 19px;
  color: #C12129;
  font-weight: 600;
  margin-bottom: 10px;
  line-height: 33px;
}
.veliler .sag p{
  font-size: 14px;
  color:#7E7E7E;
  font-weight: 500;
  line-height: 20px;
}
.veliler .sag a{
  font-size:13px;
  font-weight: 600;
}



.sosyal .sol h3{
  font-size: 19px;
  color: #C12129;
  font-weight: 600;
  margin-bottom: 10px;
  line-height: 33px;
}
.sosyal .sol p{
  font-size: 14px;
  color:#7E7E7E;
  font-weight: 500;
  line-height: 20px;
}
.sosyal .sol span{
  font-size: 20px;
  color:#223260;
  display: block;
}
.sosyal .sol a{
  font-size:13px;
  font-weight: 600;
}

.sosyal .sag {
  padding: 30px;
  background: #F7F7F6;
  border-radius: 20px;
}

.sosyal .sag .kulup {
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  height: auto;
  display: flex;
}

.sosyal .sag .kulup span {
  position: absolute;
  bottom: 0;
  padding: 20px;
  height: 100%;
  display: flex;
  text-align: left;
  top: 0;
  width: 100%;
  background: linear-gradient(180deg, rgba(3, 36, 107, 0) 0%, rgba(3, 36, 107, 0) 35%, #293861 100%);
  flex-direction: column;
  justify-content: flex-end;
}

.sosyal .sag .kulup span::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(41, 56, 97, 0.8) 0%, rgba(41, 56, 97, 0.8) 35%, #293861 100%);
  opacity: 0;
  transition: opacity 1s ease-in-out; /* Yumuşak opacity geçişi */
  pointer-events: none;
}

.sosyal .sag .kulup img {
  transition: transform 3.5s ease;
}

.sosyal .sag h3 {
  font-size: 19px;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 10px;
  line-height: 33px;
  position: relative;
  transform: translateY(0px); /* Başlangıçta normal pozisyonda */
  opacity: 1;
  transition: transform 0.6s ease, opacity 0.6s ease; 
}

.sosyal .sag p {
  font-size: 11px;
  color: #ffffff;
  font-weight: 500;
  line-height: 12px;
  position: relative;
  transform: translateY(20px); /* Başlangıçta biraz aşağıda */
  opacity: 0;
  transition: transform 0.6s ease, opacity 0.6s ease;
}

/* Hover durumları */
.sosyal .kulup span:hover::before {
  opacity: 1; /* Overlay yavaşça görünür oluyor */
  transition: opacity 1s ease-in-out;
}


.sosyal .kulup span:hover h3 {
  transform: translateY(-70px); /* H3 yukarı çıkacak */
  opacity: 1;
  transition: transform 0.6s ease, opacity 0.6s ease;
}

.sosyal .kulup span:hover p {
  transform: translateY(-70px); /* P de yukarı çıkacak ama h3'ten az */
  opacity: 1;
  transition: transform 0.6s ease, opacity 0.6s ease;
}

.sosyal .kulup:hover img {
  transform: scale(1.2);
  transition: transform 3.5s ease;
}
.sosyal .slick-dots {
    display: flex !important;
    justify-content: center;
    align-items: center;
    list-style: none;
    /* margin-top: 21px; */
    padding: 0;
    position: absolute;
    top: -66px;
    right: 0px;
}

.gelecek p {
  font-size: 12px;
  line-height: 18px;
  margin-top: 20px;
}


.haberler .haberimg {
  margin-bottom: 20px;
}

.slider_sosyal .slide_item {
  display:flex;
}

.news_slider .slick-track{
  display: flex;
}
.news_slider .haberler {
  padding:5px;
}


.ikons {
  justify-content: center;
}
.ikons .ikon {
  border:1px solid #223260;
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  transition: transform 3.5s ease, background 0.5s ease;
  margin-bottom: 20px;
}
.ikons .ikon svg{
  margin-bottom:10px;
}
.ikons .ikon span{
  font-size:13px;
  color:#223260;
  width: 100%;
  display: block;
}
.ikons .ikon:hover {
  background: #223260;
  transition: transform 3.5s ease, background 0.5s ease;
}
.ikons .ikon:hover span{
  color: #fff;
  transition: transform 3.5s ease, background 0.5s ease;
}
.ikons .ikon:hover path{
  transition: transform 3.5s ease, background 0.5s ease;
  stroke:#fff
}
.ikons .saat:hover path{
  fill:#fff;
}
.ikons .yemek:hover path{
  fill:#fff;
}
.ikons .note:hover path{
  fill:#fff;
}
.footer_link1 {
  display: flex;
  gap:10px
}
.footer_link2 {
  display: flex;
  gap:10px
}

/************************ Responsive css ***************************/
@media (min-width: 992px) and (max-width: 1199.98px) {
  .mega_menu_demo_2 .mega_menu {
    width: 940px;
  }

  .main_menu ul li a {
    border-bottom: 1px solid #ddd;
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .mega_menu_demo_2 .mega_menu {
    width: 700px;
  }

  .main_menu ul li a {
    font-size: 15px;
    padding: 20px 16px;
  }

  .main_menu ul li ul {
    width: 100%;
  }

  .main_menu ul li a {
    border-bottom: 1px solid #ddd;
  }
}

@media (max-width: 767.98px) {

  .mega_menu_demo_2 .mega_menu,
  .mobile_btn {
    cursor: pointer;
    display: block;
  }

  .main_menu {
    width: 100%;
  }

  .main_menu ul li {
    display: block;
  }

  .main_menu ul li a i {
    float: right;
  }

  .main_menu ul li a {
    border-bottom: 1px solid #ddd;
  }

  .main_menu ul li ul {
    width: 100%;
  }

  .main_menu ul li ul li ul {
    left: 0;
    top: auto;
  }

  .mega_menu .mega_menu_item {
    width: 50%;
  }

  .main_menu ul li ul {
    display: none;
    transition: none;
  }

  .main_menu ul li .mega_menu {
    display: none;
    transition: none;
  }

  .mega_menu_demo_2 .mega_menu {
    transform: translateX(0);
  }
}
@media (max-width: 1300px) {
  .counter-section.counter-v3 li {
    padding: 20px 0;
  }

  .counter-wrapper ul li span {
    font-size: 45px;
  }

  .home-references .references-content p {
    font-size: 19px;
    line-height: 30px;
  }

  .home-references .list li {
    margin-top: 5px;
    margin-bottom: 5px;
    line-height: 25px;
  }

  .neden_onemli .line1 {
    left: calc(320px / 2 + 6px) !important;
  }

  .home-references .list ul:before {
    height: 357px;
  }

  .home-references .list a {
    font-size: 15px;
  }

  .banner-header-content {
    position: relative;
    width: 100%;
    padding: 10px 35px;
  }

  .banner-header-content ul {
    margin-bottom: 0px;
    justify-content: flex-start;
  }
}

@media (max-width: 1400px) {
  .counter-wrapper {
    position: relative;
    width: 100%;
    margin-top: 30px;
  }
}

@media (max-width: 1300px) {
  .ust_menu {
    display:none;
  }
  .slider_animation_text h2 {
    font-size: 40px;
    padding: 70px 50px;
  }
}

@media (max-width: 1000px) {
  .detail_content p {
    font-size: 14px;
    line-height: 20px;
    }
  
  .about-home-content {
    margin-top: 50px;
  }
  .custom-tabs .nav-link {
    font-size: 28px;
    padding: 5px;
  }

  .slider_animation_text h2 {
    font-size: 30px;
    padding: 70px 50px;
}

  .section-p-50 {
    padding: 10px;
    margin-bottom: 10px !important;
  }

  .nav-dropdown .submenu-indicator {
    top: -10px !important;
    position: absolute;
  }

  .nav-dropdown>li {
    position: relative;
    display: block;
    width: 100%;
    padding: 0px;
  }

  .nav-dropdown .nav-dropdown {
    top: 0px;
    left: 0px;
    width: 100%;
    position: relative;
  }

  .nav-dropdown .nav-dropdown .nav-dropdown {
    left: 20px;
    top: 0px;
    width: 100%;
    position: relative;
  }

  .navigation-portrait .nav-dropdown>li>a {
    padding: 10px 30px 10px 0px;
    padding-left: 10px !important;
  }

  .navigation-portrait .submenu-indicator {
    margin-top: 19px;
    right: 1px;
  }

  .custom-tabs {
    transform: translateX(0px);
  }

  #targetDiv {
    height: auto !important;
    border: none;
  }

  .navigation-portrait .nav-dropdown>li>ul>li>a {
    font-weight: 500;
  }

  .nav-dropdown {
    border: none;
  }
  .slider-v2 .hero-content {
    max-width: 90%;
    width: 100%;
    position: absolute;
    bottom: 10px;
    left: 20px;
  }
  .detail_content {
    padding: 0px;
  }
  .veliler .sag {
    padding: 10px;
    background:none;
    border-radius: 10px;
    margin-top: 30px;
  }
  .veliler .sol .cevir {
    margin-top: 20px;
    flex-direction: column-reverse;
  }
  .footer_logo {
    display: none;
  }
}



@media (max-width: 768px) {
  .slick-custom-arrow-right {
      left: 85px;
      bottom: -20px;
  }
  .slick-custom-arrow-left {
    left: 0px;
    bottom: -20px;
  }

  .footer-block-home-1 .text-left{
      text-align: center;
  }
  .footer-block-home-1 .text-left img{
      height: 75px;
      margin-bottom:40px;
  }
  .okullar_slider .slide-item p {
    font-size: 13px;
  }
  .okullar_slider .slide-item h3 {
    font-size: 20px;
    margin-bottom: 20px;
  }
  .detail_content h2 {
    font-size: 20px;
    text-align: left !important;!i;!;
  }


  .footer-V3 .footer-bottom-copyright a {
    text-align: center;
    margin-top: 30px;
    width: 100%;
    display: block;
  }

  .banner-header-section .page-thumbnails {
    display: none;
  }

  .container-fluid {
    padding: 0 0px;
  }

  .title-sections-home {
    font-size: 30px;
    text-align: left;
    margin-bottom: 10px;
  }

  .about-us-home h1 strong {
    font-size: 30px;
    text-align: left;
  }

  .footerust img {
    width: 100%;
    height: auto;
  }
  .slider_animation_text h2 {
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: normal;
    text-align: center;
    color: #ffffff40;
    padding: 50px 20px;
  }
  .slider_animation_text {
    background: #223260;
    padding: 0px 0px;
    margin-top: 0px;
    z-index: 9999;
    position: relative;
    background-position: center top;
    padding-bottom: 0px;
    background-size: cover;
  }
  .slideimg {
    height:100vh;
    width: auto;
  }

}

@media (max-width: 600px) {
  .banner-header-section .page-thumbnails {
    display: flex;
    justify-content: center;
    position: absolute;
    overflow: hidden;
    padding: 30px 0px;
    bottom: 0px;
    width: 100%;
    left: 0px;
  }
  .ayricalik {
    flex: auto;
    margin-bottom: 20px;
  }

  .banner-header-content {
    position: relative;
    width: 100%;
    padding: 10px 15px;
  }

  .detail_content {
    font-size: 14px;
    padding: 0px;
  }

  .section-p-50 {
    padding: 0px;
    margin-bottom: 50px !important;
  }

  .detail_content p {
    font-size: 13px;
    line-height: 20px;
  }

  .aboutdetail h2 {
    font-size: 22px;
    color: #222222;
    line-height: normal;
    font-weight: 500;
  }
  .about-home-content {
    margin-top: 50px;
    margin-bottom: 20px;
  }

  .nav-brand img {
    height: 50px;
  }

  .nav-padding {
    padding: 0px 15px !important;
  }

  .nav-dropdown .nav-dropdown {
    position: relative;
  }


  #targetDiv {
    height: auto !important;
    border: none;
  }

  .nav-dropdown {
    border: none;
  }

  .navigation-portrait .nav-dropdown .nav-dropdown .submenu-indicator {
    margin-top: 23px;
  }

  .tarihler {
    margin-bottom: 80px;
  }

  .slider_buyuk {
    display: none;
  }

  .slider_mobil {
    display: block;
    height: 100vh;
    object-fit: cover;
  }

  .eucookie-pop-up .content img {
    width: 100%;
    height: auto;
  }

  .sosyal .sag {
    padding: 10px;
  }

}