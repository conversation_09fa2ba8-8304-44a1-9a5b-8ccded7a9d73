<?php
/**
 * Influencer CMS - Automatic Installation Script
 * Bu script veritabanını otomatik olarak oluşturur ve gerekli tabloları kurar
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/database.php';

// Installation status
$installation_steps = [
    'database_connection' => false,
    'database_creation' => false,
    'tables_creation' => false,
    'admin_user_creation' => false,
    'sample_data_insertion' => false
];

$messages = [];
$errors = [];

// Check if already installed
if (file_exists(CMS_ROOT . '/config/installed.lock')) {
    $messages[] = "✅ Sistem zaten kurulmuş! Eğer yeniden kurmak istiyorsanız 'installed.lock' dosyasını silin.";
}

// Process installation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !file_exists(CMS_ROOT . '/config/installed.lock')) {
    
    // Step 1: Test database connection
    try {
        $db = new Database();
        $config = $db->getConfig();
        
        // Test connection to MySQL server
        $dsn = "mysql:host=" . $config['host'] . ";charset=" . $config['charset'];
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $installation_steps['database_connection'] = true;
        $messages[] = "✅ MySQL bağlantısı başarılı";
        
    } catch(PDOException $e) {
        $errors[] = "❌ MySQL bağlantı hatası: " . $e->getMessage();
    }
    
    // Step 2: Create database
    if ($installation_steps['database_connection']) {
        try {
            if ($db->createDatabase()) {
                $installation_steps['database_creation'] = true;
                $messages[] = "✅ Veritabanı oluşturuldu: " . $config['db_name'];
            } else {
                $errors[] = "❌ Veritabanı oluşturulamadı";
            }
        } catch(Exception $e) {
            $errors[] = "❌ Veritabanı oluşturma hatası: " . $e->getMessage();
        }
    }
    
    // Step 3: Create tables
    if ($installation_steps['database_creation']) {
        try {
            $sql_file = CMS_ROOT . '/database_schema.sql';
            if ($db->executeSQLFile($sql_file)) {
                $installation_steps['tables_creation'] = true;
                $messages[] = "✅ Veritabanı tabloları oluşturuldu";
            } else {
                $errors[] = "❌ Tablolar oluşturulamadı";
            }
        } catch(Exception $e) {
            $errors[] = "❌ Tablo oluşturma hatası: " . $e->getMessage();
        }
    }
    
    // Step 4: Create admin user
    if ($installation_steps['tables_creation']) {
        try {
            $admin_username = $_POST['admin_username'] ?? 'admin';
            $admin_email = $_POST['admin_email'] ?? '<EMAIL>';
            $admin_password = $_POST['admin_password'] ?? 'admin123';
            $admin_full_name = $_POST['admin_full_name'] ?? 'System Administrator';
            
            $pdo = $db->connect();
            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password, full_name, role, status) VALUES (?, ?, ?, ?, 'admin', 'active')");
            $stmt->execute([$admin_username, $admin_email, $hashed_password, $admin_full_name]);
            
            $installation_steps['admin_user_creation'] = true;
            $messages[] = "✅ Admin kullanıcısı oluşturuldu: " . $admin_username;
            
        } catch(Exception $e) {
            $errors[] = "❌ Admin kullanıcısı oluşturma hatası: " . $e->getMessage();
        }
    }
    
    // Step 5: Insert sample data
    if ($installation_steps['admin_user_creation']) {
        try {
            $pdo = $db->connect();
            
            // Sample tags
            $tags = [
                ['Moda', '#FF6B6B'],
                ['Güzellik', '#4ECDC4'],
                ['Lifestyle', '#45B7D1'],
                ['Teknoloji', '#96CEB4'],
                ['Yemek', '#FFEAA7'],
                ['Seyahat', '#DDA0DD'],
                ['Fitness', '#98D8C8'],
                ['Sanat', '#F7DC6F']
            ];
            
            $stmt = $pdo->prepare("INSERT INTO tags (name, color) VALUES (?, ?)");
            foreach ($tags as $tag) {
                $stmt->execute($tag);
            }
            
            // Sample brands
            $brands = [
                ['Nike', null, 'https://nike.com', 'Spor', 'Ahmet Yılmaz', '<EMAIL>', '+90 ************'],
                ['Adidas', null, 'https://adidas.com', 'Spor', 'Ayşe Demir', '<EMAIL>', '+90 ************'],
                ['Zara', null, 'https://zara.com', 'Moda', 'Mehmet Kaya', '<EMAIL>', '+90 ************'],
                ['L\'Oreal', null, 'https://loreal.com', 'Güzellik', 'Fatma Özkan', '<EMAIL>', '+90 ************']
            ];
            
            $stmt = $pdo->prepare("INSERT INTO brands (name, logo, website, industry, contact_person, contact_email, contact_phone) VALUES (?, ?, ?, ?, ?, ?, ?)");
            foreach ($brands as $brand) {
                $stmt->execute($brand);
            }
            
            // Sample settings
            $settings = [
                ['company_name', 'Influencer Ajansı', 'Şirket adı'],
                ['company_email', '<EMAIL>', 'Şirket e-posta adresi'],
                ['company_phone', '+90 ************', 'Şirket telefon numarası'],
                ['default_currency', 'TRY', 'Varsayılan para birimi'],
                ['items_per_page', '20', 'Sayfa başına gösterilecek öğe sayısı'],
                ['file_upload_max_size', '10485760', 'Maksimum dosya yükleme boyutu (byte)']
            ];
            
            $stmt = $pdo->prepare("INSERT INTO settings (`key`, `value`, description) VALUES (?, ?, ?)");
            foreach ($settings as $setting) {
                $stmt->execute($setting);
            }
            
            $installation_steps['sample_data_insertion'] = true;
            $messages[] = "✅ Örnek veriler eklendi";
            
        } catch(Exception $e) {
            $errors[] = "❌ Örnek veri ekleme hatası: " . $e->getMessage();
        }
    }
    
    // Create installation lock file
    if (all_steps_completed($installation_steps)) {
        file_put_contents(CMS_ROOT . '/config/installed.lock', date('Y-m-d H:i:s'));
        $messages[] = "🎉 Kurulum başarıyla tamamlandı!";
    }
}

function all_steps_completed($steps) {
    foreach ($steps as $step) {
        if (!$step) return false;
    }
    return true;
}

// Create uploads directory
if (!is_dir(CMS_ROOT . '/uploads')) {
    mkdir(CMS_ROOT . '/uploads', 0755, true);
    mkdir(CMS_ROOT . '/uploads/profiles', 0755, true);
    mkdir(CMS_ROOT . '/uploads/files', 0755, true);
    mkdir(CMS_ROOT . '/uploads/brands', 0755, true);
}
?>

<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Influencer CMS - Kurulum</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .installation-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin: 2rem 0;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 1rem;
            border-radius: 10px;
            margin: 0 0.5rem;
            background: #f8f9fa;
        }
        .step.completed {
            background: #d4edda;
            color: #155724;
        }
        .step.failed {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="installation-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1><i class="fas fa-users me-3"></i>Influencer CMS</h1>
                    <p class="lead">Otomatik Kurulum Sihirbazı</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container my-5">
        <?php if (!file_exists(CMS_ROOT . '/config/installed.lock')): ?>
            
            <!-- Installation Steps Indicator -->
            <div class="step-indicator">
                <div class="step <?php echo $installation_steps['database_connection'] ? 'completed' : ''; ?>">
                    <i class="fas fa-database fa-2x mb-2"></i>
                    <div>Veritabanı Bağlantısı</div>
                </div>
                <div class="step <?php echo $installation_steps['database_creation'] ? 'completed' : ''; ?>">
                    <i class="fas fa-plus-circle fa-2x mb-2"></i>
                    <div>Veritabanı Oluşturma</div>
                </div>
                <div class="step <?php echo $installation_steps['tables_creation'] ? 'completed' : ''; ?>">
                    <i class="fas fa-table fa-2x mb-2"></i>
                    <div>Tablo Oluşturma</div>
                </div>
                <div class="step <?php echo $installation_steps['admin_user_creation'] ? 'completed' : ''; ?>">
                    <i class="fas fa-user-shield fa-2x mb-2"></i>
                    <div>Admin Kullanıcısı</div>
                </div>
                <div class="step <?php echo $installation_steps['sample_data_insertion'] ? 'completed' : ''; ?>">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <div>Örnek Veriler</div>
                </div>
            </div>

            <!-- Messages -->
            <?php if (!empty($messages)): ?>
                <div class="alert alert-success">
                    <?php foreach ($messages as $message): ?>
                        <div><?php echo $message; ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <?php foreach ($errors as $error): ?>
                        <div><?php echo $error; ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <!-- Installation Form -->
            <?php if (!all_steps_completed($installation_steps)): ?>
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-cog me-2"></i>Kurulum Ayarları</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="admin_username" class="form-label">Admin Kullanıcı Adı</label>
                                        <input type="text" class="form-control" id="admin_username" name="admin_username" value="admin" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="admin_email" class="form-label">Admin E-posta</label>
                                        <input type="email" class="form-control" id="admin_email" name="admin_email" value="<EMAIL>" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="admin_password" class="form-label">Admin Şifre</label>
                                        <input type="password" class="form-control" id="admin_password" name="admin_password" value="admin123" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="admin_full_name" class="form-label">Admin Tam Adı</label>
                                        <input type="text" class="form-control" id="admin_full_name" name="admin_full_name" value="System Administrator" required>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-rocket me-2"></i>Kurulumu Başlat
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <div class="text-center">
                    <div class="alert alert-success">
                        <h4><i class="fas fa-check-circle me-2"></i>Kurulum Tamamlandı!</h4>
                        <p>Influencer CMS başarıyla kuruldu. Artık sistemi kullanmaya başlayabilirsiniz.</p>
                        <a href="login.php" class="btn btn-success btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Giriş Yap
                        </a>
                    </div>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <div class="text-center">
                <div class="alert alert-info">
                    <h4><i class="fas fa-info-circle me-2"></i>Sistem Zaten Kurulmuş</h4>
                    <p>Influencer CMS zaten kurulmuş durumda.</p>
                    <a href="login.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Giriş Yap
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
