<!-- Reset Password Section -->
<section class="py-5 bg-light min-vh-100 d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-success text-white text-center py-4">
                        <h2 class="mb-0">
                            <i class="fas fa-lock me-2"></i>
                            Yeni Şifre Belirle
                        </h2>
                        <p class="mb-0 mt-2">Hesabınız için yeni bir şifre oluşturun</p>
                    </div>
                    
                    <div class="card-body p-5">
                        <?php echo validation_errors('<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>', '</div>'); ?>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Ye<PERSON> şifreniz en az 6 karakter olmalı ve güçlü olmalıdır.
                        </div>

                        <form action="<?php echo site_url('auth/reset_password/' . $token); ?>" method="POST" class="needs-validation" novalidate>
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Yeni Şifre
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-lg" id="password" name="password" 
                                           placeholder="Yeni şifrenizi girin" minlength="6" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Şifre en az 6 karakter olmalıdır.
                                </div>
                                <div class="form-text">
                                    <small id="password-strength" class="text-muted">Şifre gücü: <span id="strength-text">Zayıf</span></small>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password_confirm" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Şifre Tekrar
                                </label>
                                <input type="password" class="form-control form-control-lg" id="password_confirm" name="password_confirm" 
                                       placeholder="Şifrenizi tekrar girin" required>
                                <div class="invalid-feedback">
                                    Şifreler eşleşmiyor.
                                </div>
                            </div>
                            
                            <!-- Password Requirements -->
                            <div class="mb-4">
                                <small class="text-muted">Şifre gereksinimleri:</small>
                                <ul class="list-unstyled small text-muted">
                                    <li id="length-req"><i class="fas fa-times text-danger me-1"></i>En az 6 karakter</li>
                                    <li id="letter-req"><i class="fas fa-times text-danger me-1"></i>En az bir harf</li>
                                    <li id="number-req"><i class="fas fa-times text-danger me-1"></i>En az bir rakam</li>
                                    <li id="match-req"><i class="fas fa-times text-danger me-1"></i>Şifreler eşleşmeli</li>
                                </ul>
                            </div>
                            
                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-success btn-lg" id="submit-btn" disabled>
                                    <i class="fas fa-check me-2"></i>
                                    Şifreyi Güncelle
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center">
                            <a href="<?php echo site_url('auth/login'); ?>" class="text-decoration-none">
                                <i class="fas fa-arrow-left me-1"></i>Giriş Sayfasına Dön
                            </a>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-light text-center py-3">
                        <p class="mb-0">
                            <i class="fas fa-shield-alt text-success me-1"></i>
                            Güvenli bağlantı ile korunmaktasınız
                        </p>
                    </div>
                </div>
                
                <!-- Security Tips -->
                <div class="card mt-4 border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            Güvenlik İpuçları
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><i class="fas fa-check text-success me-2"></i>Güçlü bir şifre seçin</li>
                            <li><i class="fas fa-check text-success me-2"></i>Şifrenizi kimseyle paylaşmayın</li>
                            <li><i class="fas fa-check text-success me-2"></i>Düzenli olarak şifrenizi değiştirin</li>
                            <li><i class="fas fa-check text-success me-2"></i>Farklı siteler için farklı şifreler kullanın</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.min-vh-100 {
    min-height: 100vh;
}

.card {
    border-radius: 15px;
    overflow: hidden;
}

.form-control-lg {
    border-radius: 10px;
    padding: 15px;
}

.btn-lg {
    border-radius: 10px;
    padding: 15px;
    font-weight: 600;
}

.input-group .btn {
    border-radius: 0 10px 10px 0;
}

.strength-weak { color: #dc3545; }
.strength-medium { color: #ffc107; }
.strength-strong { color: #28a745; }

@media (max-width: 768px) {
    .card-body {
        padding: 2rem !important;
    }
}
</style>

<script>
$(document).ready(function() {
    // Password toggle
    $('#togglePassword').click(function() {
        const password = $('#password');
        const icon = $(this).find('i');
        
        if (password.attr('type') === 'password') {
            password.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            password.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Password strength checker
    $('#password').on('input', function() {
        const password = $(this).val();
        checkPasswordStrength(password);
        checkPasswordRequirements(password);
        checkPasswordMatch();
    });
    
    $('#password_confirm').on('input', function() {
        checkPasswordMatch();
    });
    
    function checkPasswordStrength(password) {
        let strength = 0;
        let strengthText = 'Zayıf';
        let strengthClass = 'strength-weak';
        
        if (password.length >= 6) strength++;
        if (password.match(/[a-zA-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;
        
        if (strength >= 3) {
            strengthText = 'Güçlü';
            strengthClass = 'strength-strong';
        } else if (strength >= 2) {
            strengthText = 'Orta';
            strengthClass = 'strength-medium';
        }
        
        $('#strength-text').text(strengthText).attr('class', strengthClass);
    }
    
    function checkPasswordRequirements(password) {
        // Length check
        if (password.length >= 6) {
            updateRequirement('length-req', true);
        } else {
            updateRequirement('length-req', false);
        }
        
        // Letter check
        if (password.match(/[a-zA-Z]/)) {
            updateRequirement('letter-req', true);
        } else {
            updateRequirement('letter-req', false);
        }
        
        // Number check
        if (password.match(/[0-9]/)) {
            updateRequirement('number-req', true);
        } else {
            updateRequirement('number-req', false);
        }
        
        checkFormValidity();
    }
    
    function checkPasswordMatch() {
        const password = $('#password').val();
        const confirm = $('#password_confirm').val();
        
        if (password && confirm && password === confirm) {
            updateRequirement('match-req', true);
            $('#password_confirm')[0].setCustomValidity('');
        } else {
            updateRequirement('match-req', false);
            if (confirm) {
                $('#password_confirm')[0].setCustomValidity('Şifreler eşleşmiyor');
            }
        }
        
        checkFormValidity();
    }
    
    function updateRequirement(id, met) {
        const element = $('#' + id);
        const icon = element.find('i');
        
        if (met) {
            icon.removeClass('fa-times text-danger').addClass('fa-check text-success');
        } else {
            icon.removeClass('fa-check text-success').addClass('fa-times text-danger');
        }
    }
    
    function checkFormValidity() {
        const password = $('#password').val();
        const confirm = $('#password_confirm').val();
        
        const lengthOk = password.length >= 6;
        const letterOk = password.match(/[a-zA-Z]/);
        const numberOk = password.match(/[0-9]/);
        const matchOk = password && confirm && password === confirm;
        
        if (lengthOk && letterOk && numberOk && matchOk) {
            $('#submit-btn').prop('disabled', false);
        } else {
            $('#submit-btn').prop('disabled', true);
        }
    }
    
    // Form validation
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });
});
</script>
