<?php
/**
 * Campaign Management
 * Kampanya yönetimi ve geçmişi
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Check permissions
requirePermission('campaigns_view');

// Page settings
$page_title = 'Ka<PERSON>anya Yönetimi';
$action = $_GET['action'] ?? 'list';
$campaign_id = $_GET['id'] ?? null;
$influencer_id = $_GET['influencer_id'] ?? null;

// Campaign statuses
$campaign_statuses = [
    'planned' => 'Planlandı',
    'in_progress' => 'Devam Ediyor',
    'completed' => 'Tamamlandı',
    'cancelled' => 'İptal Edildi',
    'delayed' => 'Ertelendi'
];

// Content types
$content_types = [
    'story' => 'Story',
    'post' => 'Post',
    'reels' => 'Reels/Video',
    'video' => 'Video',
    'live' => 'Canlı Yayın',
    'package' => 'Paket'
];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        global $database;
        $pdo = $database->connect();
        
        if ($action === 'add' && hasPermission('campaigns_manage')) {
            // Add new campaign
            $stmt = $pdo->prepare("
                INSERT INTO campaigns (influencer_id, brand_id, title, description, platform, 
                                     content_type, price, currency, start_date, end_date, 
                                     delivery_date, status, brief_url, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $_POST['influencer_id'],
                $_POST['brand_id'],
                sanitizeInput($_POST['title']),
                sanitizeInput($_POST['description']),
                $_POST['platform'],
                $_POST['content_type'],
                floatval($_POST['price']),
                $_POST['currency'] ?? 'TRY',
                $_POST['start_date'] ?: null,
                $_POST['end_date'] ?: null,
                $_POST['delivery_date'] ?: null,
                $_POST['status'] ?? 'planned',
                sanitizeInput($_POST['brief_url']),
                $_SESSION['user_id']
            ]);
            
            $_SESSION['success_message'] = 'Kampanya başarıyla eklendi.';
            redirectTo('campaigns.php');
            
        } elseif ($action === 'edit' && hasPermission('campaigns_manage')) {
            // Update campaign
            $stmt = $pdo->prepare("
                UPDATE campaigns 
                SET title = ?, description = ?, platform = ?, content_type = ?, price = ?, 
                    currency = ?, start_date = ?, end_date = ?, delivery_date = ?, status = ?, 
                    brief_url = ?, content_url = ?, performance_notes = ?, rating = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                sanitizeInput($_POST['title']),
                sanitizeInput($_POST['description']),
                $_POST['platform'],
                $_POST['content_type'],
                floatval($_POST['price']),
                $_POST['currency'] ?? 'TRY',
                $_POST['start_date'] ?: null,
                $_POST['end_date'] ?: null,
                $_POST['delivery_date'] ?: null,
                $_POST['status'],
                sanitizeInput($_POST['brief_url']),
                sanitizeInput($_POST['content_url']),
                sanitizeInput($_POST['performance_notes']),
                $_POST['rating'] ? intval($_POST['rating']) : null,
                $campaign_id
            ]);
            
            $_SESSION['success_message'] = 'Kampanya güncellendi.';
            redirectTo('campaigns.php');
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'İşlem sırasında bir hata oluştu: ' . $e->getMessage();
        error_log('Campaign operation error: ' . $e->getMessage());
    }
}

// Handle delete action
if ($action === 'delete' && $campaign_id && hasPermission('campaigns_manage')) {
    try {
        global $database;
        $pdo = $database->connect();
        
        $stmt = $pdo->prepare("DELETE FROM campaigns WHERE id = ?");
        $stmt->execute([$campaign_id]);
        
        $_SESSION['success_message'] = 'Kampanya silindi.';
        redirectTo('campaigns.php');
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Silme işlemi sırasında bir hata oluştu.';
        error_log('Campaign delete error: ' . $e->getMessage());
    }
}

// Get data based on action
if ($action === 'list') {
    try {
        global $database;
        $pdo = $database->connect();
        
        $search = $_GET['search'] ?? '';
        $status_filter = $_GET['status'] ?? '';
        $brand_filter = $_GET['brand'] ?? '';
        
        $where_conditions = [];
        $params = [];
        
        if (!empty($search)) {
            $where_conditions[] = "(c.title LIKE ? OR c.description LIKE ? OR CONCAT(i.first_name, ' ', i.last_name) LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($status_filter)) {
            $where_conditions[] = "c.status = ?";
            $params[] = $status_filter;
        }
        
        if (!empty($brand_filter)) {
            $where_conditions[] = "c.brand_id = ?";
            $params[] = $brand_filter;
        }
        
        if ($influencer_id) {
            $where_conditions[] = "c.influencer_id = ?";
            $params[] = $influencer_id;
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        $stmt = $pdo->prepare("
            SELECT c.*, 
                   CONCAT(i.first_name, ' ', i.last_name) as influencer_name,
                   i.profile_photo as influencer_photo,
                   b.name as brand_name,
                   b.logo as brand_logo,
                   u.full_name as created_by_name
            FROM campaigns c 
            LEFT JOIN influencers i ON c.influencer_id = i.id
            LEFT JOIN brands b ON c.brand_id = b.id
            LEFT JOIN users u ON c.created_by = u.id
            $where_clause
            ORDER BY c.created_at DESC
        ");
        $stmt->execute($params);
        $campaigns = $stmt->fetchAll();
        
        // Get brands for filter
        $stmt = $pdo->query("SELECT id, name FROM brands WHERE status = 'active' ORDER BY name");
        $brands = $stmt->fetchAll();
        
    } catch (Exception $e) {
        $campaigns = [];
        $brands = [];
        error_log('Campaign list error: ' . $e->getMessage());
    }
    
} elseif ($action === 'add' || $action === 'edit') {
    try {
        global $database;
        $pdo = $database->connect();
        
        // Get influencers
        $stmt = $pdo->query("SELECT id, first_name, last_name FROM influencers WHERE status = 'active' ORDER BY first_name, last_name");
        $influencers = $stmt->fetchAll();
        
        // Get brands
        $stmt = $pdo->query("SELECT id, name FROM brands WHERE status = 'active' ORDER BY name");
        $brands = $stmt->fetchAll();
        
        // Get campaign for editing
        if ($action === 'edit' && $campaign_id) {
            $stmt = $pdo->prepare("
                SELECT c.*, 
                       CONCAT(i.first_name, ' ', i.last_name) as influencer_name,
                       b.name as brand_name
                FROM campaigns c 
                LEFT JOIN influencers i ON c.influencer_id = i.id
                LEFT JOIN brands b ON c.brand_id = b.id
                WHERE c.id = ?
            ");
            $stmt->execute([$campaign_id]);
            $campaign = $stmt->fetch();
            
            if (!$campaign) {
                $_SESSION['error_message'] = 'Kampanya bulunamadı.';
                redirectTo('campaigns.php');
            }
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Veri alınırken hata oluştu.';
        error_log('Campaign data error: ' . $e->getMessage());
        redirectTo('campaigns.php');
    }
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <!-- Campaign List -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-bullhorn me-2"></i>
                        Kampanya Listesi (<?php echo count($campaigns); ?>)
                    </h5>
                    <?php if (hasPermission('campaigns_manage')): ?>
                        <a href="campaigns.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Yeni Kampanya
                        </a>
                    <?php endif; ?>
                </div>
                
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" class="row g-3 mb-4">
                        <div class="col-md-4">
                            <input type="text" class="form-control" name="search" 
                                   value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                                   placeholder="Kampanya, influencer ara...">
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="">Tüm Durumlar</option>
                                <?php foreach ($campaign_statuses as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" <?php echo ($_GET['status'] ?? '') === $key ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="brand" class="form-select">
                                <option value="">Tüm Markalar</option>
                                <?php foreach ($brands as $brand): ?>
                                    <option value="<?php echo $brand['id']; ?>" 
                                            <?php echo ($_GET['brand'] ?? '') == $brand['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($brand['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search"></i> Filtrele
                            </button>
                        </div>
                    </form>
                    
                    <!-- Campaign Table -->
                    <?php if (!empty($campaigns)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Kampanya</th>
                                        <th>Influencer</th>
                                        <th>Marka</th>
                                        <th>Platform</th>
                                        <th>Fiyat</th>
                                        <th>Durum</th>
                                        <th>Tarih</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($campaigns as $camp): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($camp['title']); ?></strong>
                                                    <br><small class="text-muted">
                                                        <?php echo $content_types[$camp['content_type']] ?? $camp['content_type']; ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($camp['influencer_photo']): ?>
                                                        <img src="<?php echo UPLOAD_URL . 'profiles/' . $camp['influencer_photo']; ?>" 
                                                             class="rounded-circle me-2" width="32" height="32" style="object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                             style="width: 32px; height: 32px;">
                                                            <i class="fas fa-user text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <small><?php echo htmlspecialchars($camp['influencer_name']); ?></small>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($camp['brand_name']); ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="<?php echo PLATFORMS[$camp['platform']]['icon'] ?? 'fas fa-globe'; ?> me-1" 
                                                       style="color: <?php echo PLATFORMS[$camp['platform']]['color'] ?? '#6c757d'; ?>"></i>
                                                    <small><?php echo PLATFORMS[$camp['platform']]['name'] ?? ucfirst($camp['platform']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    <?php echo formatCurrency($camp['price'], $camp['currency']); ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo STATUS_COLORS[$camp['status']] ?? 'secondary'; ?>">
                                                    <?php echo $campaign_statuses[$camp['status']] ?? $camp['status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php if ($camp['start_date']): ?>
                                                        <?php echo date('d.m.Y', strtotime($camp['start_date'])); ?>
                                                    <?php else: ?>
                                                        <?php echo date('d.m.Y', strtotime($camp['created_at'])); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <?php if (hasPermission('campaigns_manage')): ?>
                                                        <a href="campaigns.php?action=edit&id=<?php echo $camp['id']; ?>" 
                                                           class="btn btn-outline-warning" data-bs-toggle="tooltip" title="Düzenle">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="campaigns.php?action=delete&id=<?php echo $camp['id']; ?>" 
                                                           class="btn btn-outline-danger" data-bs-toggle="tooltip" title="Sil"
                                                           onclick="return confirmDelete('Bu kampanyayı silmek istediğinizden emin misiniz?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Henüz kampanya eklenmemiş</h5>
                            <p class="text-muted">İlk kampanyanızı oluşturmak için yukarıdaki butonu kullanın.</p>
                            <?php if (hasPermission('campaigns_manage')): ?>
                                <a href="campaigns.php?action=add" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>İlk Kampanyayı Oluştur
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Campaign Form will be added in next part -->
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        Kampanya formu bir sonraki adımda eklenecek.
    </div>
<?php endif; ?>

<?php
// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
