<?php
/**
 * Campaign Management
 * Kampanya yönetimi ve geçmişi
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Check permissions
requirePermission('campaigns_view');

// Page settings
$page_title = 'Ka<PERSON>anya Yönetimi';
$action = $_GET['action'] ?? 'list';
$campaign_id = $_GET['id'] ?? null;
$influencer_id = $_GET['influencer_id'] ?? null;

// Campaign statuses
$campaign_statuses = [
    'planned' => 'Planlandı',
    'in_progress' => 'Devam Ediyor',
    'completed' => 'Tamamlandı',
    'cancelled' => 'İptal Edildi',
    'delayed' => 'Ertelendi'
];

// Content types
$content_types = [
    'story' => 'Story',
    'post' => 'Post',
    'reels' => 'Reels/Video',
    'video' => 'Video',
    'live' => 'Canlı Yayın',
    'package' => 'Paket'
];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        global $database;
        $pdo = $database->connect();
        
        if ($action === 'add' && hasPermission('campaigns_manage')) {
            // Validate dates
            $start_date = $_POST['start_date'] ?: null;
            $end_date = $_POST['end_date'] ?: null;
            $delivery_date = $_POST['delivery_date'] ?: null;

            $date_error = false;

            if ($start_date && $end_date && $start_date > $end_date) {
                $_SESSION['error_message'] = 'Bitiş tarihi başlangıç tarihinden önce olamaz!';
                $date_error = true;
            }

            if ($delivery_date && $end_date && $delivery_date > $end_date) {
                $_SESSION['error_message'] = 'Teslim tarihi bitiş tarihinden sonra olamaz!';
                $date_error = true;
            }

            if (!$date_error) {
                // Add new campaign
                $stmt = $pdo->prepare("
                    INSERT INTO campaigns (influencer_id, brand_id, title, description, platform,
                                         content_type, price, currency, start_date, end_date,
                                         delivery_date, status, brief_url, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");

                $stmt->execute([
                    $_POST['influencer_id'],
                    $_POST['brand_id'],
                    sanitizeInput($_POST['title']),
                    sanitizeInput($_POST['description']),
                    $_POST['platform'],
                    $_POST['content_type'],
                    floatval($_POST['price']),
                    $_POST['currency'] ?? 'TRY',
                    $start_date,
                    $end_date,
                    $delivery_date,
                    $_POST['status'] ?? 'planned',
                    sanitizeInput($_POST['brief_url']),
                    $_SESSION['user_id']
                ]);

                $_SESSION['success_message'] = 'Kampanya başarıyla eklendi.';
                redirectTo('campaigns.php');
            }
            
        } elseif ($action === 'edit' && hasPermission('campaigns_manage')) {
            // Validate dates
            $start_date = $_POST['start_date'] ?: null;
            $end_date = $_POST['end_date'] ?: null;
            $delivery_date = $_POST['delivery_date'] ?: null;

            $date_error = false;

            if ($start_date && $end_date && $start_date > $end_date) {
                $_SESSION['error_message'] = 'Bitiş tarihi başlangıç tarihinden önce olamaz!';
                $date_error = true;
            }

            if ($delivery_date && $end_date && $delivery_date > $end_date) {
                $_SESSION['error_message'] = 'Teslim tarihi bitiş tarihinden sonra olamaz!';
                $date_error = true;
            }

            if (!$date_error) {
                // Update campaign
                $stmt = $pdo->prepare("
                    UPDATE campaigns
                    SET title = ?, description = ?, platform = ?, content_type = ?, price = ?,
                        currency = ?, start_date = ?, end_date = ?, delivery_date = ?, status = ?,
                        brief_url = ?, content_url = ?, performance_notes = ?, rating = ?
                    WHERE id = ?
                ");

                $stmt->execute([
                    sanitizeInput($_POST['title']),
                    sanitizeInput($_POST['description']),
                    $_POST['platform'],
                    $_POST['content_type'],
                    floatval($_POST['price']),
                    $_POST['currency'] ?? 'TRY',
                    $start_date,
                    $end_date,
                    $delivery_date,
                    $_POST['status'],
                    sanitizeInput($_POST['brief_url']),
                    sanitizeInput($_POST['content_url']),
                    sanitizeInput($_POST['performance_notes']),
                    $_POST['rating'] ? intval($_POST['rating']) : null,
                    $campaign_id
                ]);

                $_SESSION['success_message'] = 'Kampanya güncellendi.';
                redirectTo('campaigns.php');
            }
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'İşlem sırasında bir hata oluştu: ' . $e->getMessage();
        error_log('Campaign operation error: ' . $e->getMessage());
    }
}

// Handle delete action
if ($action === 'delete' && $campaign_id && hasPermission('campaigns_manage')) {
    try {
        global $database;
        $pdo = $database->connect();
        
        $stmt = $pdo->prepare("DELETE FROM campaigns WHERE id = ?");
        $stmt->execute([$campaign_id]);
        
        $_SESSION['success_message'] = 'Kampanya silindi.';
        redirectTo('campaigns.php');
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Silme işlemi sırasında bir hata oluştu.';
        error_log('Campaign delete error: ' . $e->getMessage());
    }
}

// Get data based on action
if ($action === 'list') {
    try {
        global $database;
        $pdo = $database->connect();
        
        $search = $_GET['search'] ?? '';
        $status_filter = $_GET['status'] ?? '';
        $brand_filter = $_GET['brand'] ?? '';
        
        $where_conditions = [];
        $params = [];
        
        if (!empty($search)) {
            $where_conditions[] = "(c.title LIKE ? OR c.description LIKE ? OR CONCAT(i.first_name, ' ', i.last_name) LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($status_filter)) {
            $where_conditions[] = "c.status = ?";
            $params[] = $status_filter;
        }
        
        if (!empty($brand_filter)) {
            $where_conditions[] = "c.brand_id = ?";
            $params[] = $brand_filter;
        }
        
        if ($influencer_id) {
            $where_conditions[] = "c.influencer_id = ?";
            $params[] = $influencer_id;
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        $stmt = $pdo->prepare("
            SELECT c.*, 
                   CONCAT(i.first_name, ' ', i.last_name) as influencer_name,
                   i.profile_photo as influencer_photo,
                   b.name as brand_name,
                   b.logo as brand_logo,
                   u.full_name as created_by_name
            FROM campaigns c 
            LEFT JOIN influencers i ON c.influencer_id = i.id
            LEFT JOIN brands b ON c.brand_id = b.id
            LEFT JOIN users u ON c.created_by = u.id
            $where_clause
            ORDER BY c.created_at DESC
        ");
        $stmt->execute($params);
        $campaigns = $stmt->fetchAll();
        
        // Get brands for filter
        $stmt = $pdo->query("SELECT id, name FROM brands WHERE status = 'active' ORDER BY name");
        $brands = $stmt->fetchAll();
        
    } catch (Exception $e) {
        $campaigns = [];
        $brands = [];
        error_log('Campaign list error: ' . $e->getMessage());
    }
    
} elseif ($action === 'add' || $action === 'edit') {
    try {
        global $database;
        $pdo = $database->connect();
        
        // Get influencers
        $stmt = $pdo->query("SELECT id, first_name, last_name FROM influencers WHERE status = 'active' ORDER BY first_name, last_name");
        $influencers = $stmt->fetchAll();
        
        // Get brands
        $stmt = $pdo->query("SELECT id, name FROM brands WHERE status = 'active' ORDER BY name");
        $brands = $stmt->fetchAll();
        
        // Get campaign for editing
        if ($action === 'edit' && $campaign_id) {
            $stmt = $pdo->prepare("
                SELECT c.*, 
                       CONCAT(i.first_name, ' ', i.last_name) as influencer_name,
                       b.name as brand_name
                FROM campaigns c 
                LEFT JOIN influencers i ON c.influencer_id = i.id
                LEFT JOIN brands b ON c.brand_id = b.id
                WHERE c.id = ?
            ");
            $stmt->execute([$campaign_id]);
            $campaign = $stmt->fetch();
            
            if (!$campaign) {
                $_SESSION['error_message'] = 'Kampanya bulunamadı.';
                redirectTo('campaigns.php');
            }
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Veri alınırken hata oluştu.';
        error_log('Campaign data error: ' . $e->getMessage());
        redirectTo('campaigns.php');
    }
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <!-- Campaign List -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-bullhorn me-2"></i>
                        Kampanya Listesi (<?php echo count($campaigns); ?>)
                    </h5>
                    <?php if (hasPermission('campaigns_manage')): ?>
                        <a href="campaigns.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Yeni Kampanya
                        </a>
                    <?php endif; ?>
                </div>
                
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" class="row g-3 mb-4">
                        <div class="col-md-4">
                            <input type="text" class="form-control" name="search" 
                                   value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                                   placeholder="Kampanya, influencer ara...">
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="">Tüm Durumlar</option>
                                <?php foreach ($campaign_statuses as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" <?php echo ($_GET['status'] ?? '') === $key ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="brand" class="form-select">
                                <option value="">Tüm Markalar</option>
                                <?php foreach ($brands as $brand): ?>
                                    <option value="<?php echo $brand['id']; ?>" 
                                            <?php echo ($_GET['brand'] ?? '') == $brand['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($brand['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search"></i> Filtrele
                            </button>
                        </div>
                    </form>
                    
                    <!-- Campaign Table -->
                    <?php if (!empty($campaigns)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Kampanya</th>
                                        <th>Influencer</th>
                                        <th>Marka</th>
                                        <th>Platform</th>
                                        <th>Fiyat</th>
                                        <th>Durum</th>
                                        <th>Tarih</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($campaigns as $camp): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($camp['title']); ?></strong>
                                                    <br><small class="text-muted">
                                                        <?php echo $content_types[$camp['content_type']] ?? $camp['content_type']; ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($camp['influencer_photo']): ?>
                                                        <img src="<?php echo UPLOAD_URL . 'profiles/' . $camp['influencer_photo']; ?>" 
                                                             class="rounded-circle me-2" width="32" height="32" style="object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                             style="width: 32px; height: 32px;">
                                                            <i class="fas fa-user text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <small><?php echo htmlspecialchars($camp['influencer_name']); ?></small>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($camp['brand_name']); ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="<?php echo PLATFORMS[$camp['platform']]['icon'] ?? 'fas fa-globe'; ?> me-1" 
                                                       style="color: <?php echo PLATFORMS[$camp['platform']]['color'] ?? '#6c757d'; ?>"></i>
                                                    <small><?php echo PLATFORMS[$camp['platform']]['name'] ?? ucfirst($camp['platform']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    <?php echo formatCurrency($camp['price'], $camp['currency']); ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo STATUS_COLORS[$camp['status']] ?? 'secondary'; ?>">
                                                    <?php echo $campaign_statuses[$camp['status']] ?? $camp['status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php if ($camp['start_date']): ?>
                                                        <?php echo date('d.m.Y', strtotime($camp['start_date'])); ?>
                                                    <?php else: ?>
                                                        <?php echo date('d.m.Y', strtotime($camp['created_at'])); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <?php if (hasPermission('campaigns_manage')): ?>
                                                        <a href="campaigns.php?action=edit&id=<?php echo $camp['id']; ?>" 
                                                           class="btn btn-outline-warning" data-bs-toggle="tooltip" title="Düzenle">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="campaigns.php?action=delete&id=<?php echo $camp['id']; ?>" 
                                                           class="btn btn-outline-danger" data-bs-toggle="tooltip" title="Sil"
                                                           onclick="return confirmDelete('Bu kampanyayı silmek istediğinizden emin misiniz?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Henüz kampanya eklenmemiş</h5>
                            <p class="text-muted">İlk kampanyanızı oluşturmak için yukarıdaki butonu kullanın.</p>
                            <?php if (hasPermission('campaigns_manage')): ?>
                                <a href="campaigns.php?action=add" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>İlk Kampanyayı Oluştur
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Campaign Form -->
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                        <?php echo $action === 'add' ? 'Yeni Kampanya Oluştur' : 'Kampanya Düzenle'; ?>
                    </h5>
                </div>

                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="title" class="form-label">Kampanya Başlığı <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title"
                                       value="<?php echo htmlspecialchars($campaign['title'] ?? ''); ?>" required>
                                <div class="invalid-feedback">Kampanya başlığı zorunludur.</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">Durum</label>
                                <select class="form-select" id="status" name="status">
                                    <?php foreach ($campaign_statuses as $key => $name): ?>
                                        <option value="<?php echo $key; ?>"
                                                <?php echo ($campaign['status'] ?? 'planned') === $key ? 'selected' : ''; ?>>
                                            <?php echo $name; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="influencer_id" class="form-label">Influencer <span class="text-danger">*</span></label>
                                <select class="form-select" id="influencer_id" name="influencer_id" required>
                                    <option value="">Influencer Seçin</option>
                                    <?php foreach ($influencers as $inf): ?>
                                        <option value="<?php echo $inf['id']; ?>"
                                                <?php echo ($campaign['influencer_id'] ?? $_GET['influencer_id'] ?? '') == $inf['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($inf['first_name'] . ' ' . $inf['last_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Influencer seçimi zorunludur.</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="brand_id" class="form-label">Marka <span class="text-danger">*</span></label>
                                <select class="form-select" id="brand_id" name="brand_id" required>
                                    <option value="">Marka Seçin</option>
                                    <?php foreach ($brands as $brand): ?>
                                        <option value="<?php echo $brand['id']; ?>"
                                                <?php echo ($campaign['brand_id'] ?? '') == $brand['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($brand['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Marka seçimi zorunludur.</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="platform" class="form-label">Platform <span class="text-danger">*</span></label>
                                <select class="form-select" id="platform" name="platform" required>
                                    <option value="">Platform Seçin</option>
                                    <?php foreach (PLATFORMS as $key => $config): ?>
                                        <option value="<?php echo $key; ?>"
                                                <?php echo ($campaign['platform'] ?? '') === $key ? 'selected' : ''; ?>>
                                            <?php echo $config['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Platform seçimi zorunludur.</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="content_type" class="form-label">İçerik Türü <span class="text-danger">*</span></label>
                                <select class="form-select" id="content_type" name="content_type" required>
                                    <option value="">İçerik Türü Seçin</option>
                                    <?php foreach ($content_types as $key => $name): ?>
                                        <option value="<?php echo $key; ?>"
                                                <?php echo ($campaign['content_type'] ?? '') === $key ? 'selected' : ''; ?>>
                                            <?php echo $name; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">İçerik türü seçimi zorunludur.</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="price" class="form-label">Fiyat <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="price" name="price"
                                           value="<?php echo $campaign['price'] ?? ''; ?>"
                                           min="0" step="0.01" required>
                                    <select class="form-select" name="currency" style="max-width: 100px;">
                                        <?php foreach (CURRENCIES as $code => $currency): ?>
                                            <option value="<?php echo $code; ?>"
                                                    <?php echo ($campaign['currency'] ?? 'TRY') === $code ? 'selected' : ''; ?>>
                                                <?php echo $code; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="invalid-feedback">Fiyat zorunludur.</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="delivery_date" class="form-label">Teslim Tarihi</label>
                                <input type="date" class="form-control" id="delivery_date" name="delivery_date"
                                       value="<?php echo $campaign['delivery_date'] ?? ''; ?>"
                                       min="<?php echo date('Y-m-d'); ?>">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="start_date" class="form-label">Başlangıç Tarihi</label>
                                <input type="date" class="form-control" id="start_date" name="start_date"
                                       value="<?php echo $campaign['start_date'] ?? ''; ?>"
                                       min="<?php echo date('Y-m-d'); ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="end_date" class="form-label">Bitiş Tarihi</label>
                                <input type="date" class="form-control" id="end_date" name="end_date"
                                       value="<?php echo $campaign['end_date'] ?? ''; ?>"
                                       min="<?php echo date('Y-m-d'); ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Kampanya Açıklaması</label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="Kampanya detayları, hedefler, özel talepler..."><?php echo htmlspecialchars($campaign['description'] ?? ''); ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="brief_url" class="form-label">Brief URL'si</label>
                                <input type="url" class="form-control" id="brief_url" name="brief_url"
                                       value="<?php echo htmlspecialchars($campaign['brief_url'] ?? ''); ?>"
                                       placeholder="https://...">
                                <div class="form-text">Kampanya brief'i veya dökümanların bulunduğu link</div>
                            </div>

                            <?php if ($action === 'edit'): ?>
                                <div class="col-md-6 mb-3">
                                    <label for="content_url" class="form-label">İçerik URL'si</label>
                                    <input type="url" class="form-control" id="content_url" name="content_url"
                                           value="<?php echo htmlspecialchars($campaign['content_url'] ?? ''); ?>"
                                           placeholder="https://...">
                                    <div class="form-text">Yayınlanan içeriğin linki</div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <?php if ($action === 'edit'): ?>
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="performance_notes" class="form-label">Performans Notları</label>
                                    <textarea class="form-control" id="performance_notes" name="performance_notes" rows="3"
                                              placeholder="Kampanya performansı, sonuçlar, geri bildirimler..."><?php echo htmlspecialchars($campaign['performance_notes'] ?? ''); ?></textarea>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="rating" class="form-label">Değerlendirme</label>
                                    <select class="form-select" id="rating" name="rating">
                                        <option value="">Değerlendirme Yok</option>
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <option value="<?php echo $i; ?>"
                                                    <?php echo ($campaign['rating'] ?? '') == $i ? 'selected' : ''; ?>>
                                                <?php echo $i; ?> Yıldız
                                            </option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        <?php endif; ?>

                        <hr>

                        <div class="d-flex justify-content-between">
                            <a href="campaigns.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Geri Dön
                            </a>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                <?php echo $action === 'add' ? 'Kampanya Oluştur' : 'Değişiklikleri Kaydet'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
// Page specific scripts
$page_scripts = '
<script>
// Form validation with date checking
(function() {
    "use strict";
    window.addEventListener("load", function() {
        var forms = document.getElementsByClassName("needs-validation");
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener("submit", function(event) {
                var isValid = form.checkValidity();

                // Additional date validation
                const startDateValue = document.getElementById("start_date")?.value;
                const endDateValue = document.getElementById("end_date")?.value;
                const deliveryDateValue = document.getElementById("delivery_date")?.value;

                if (startDateValue && endDateValue) {
                    const startDate = new Date(startDateValue);
                    const endDate = new Date(endDateValue);

                    if (startDate > endDate) {
                        alert("Bitiş tarihi başlangıç tarihinden önce olamaz!");
                        isValid = false;
                    }
                }

                if (deliveryDateValue && endDateValue) {
                    const deliveryDate = new Date(deliveryDateValue);
                    const endDate = new Date(endDateValue);

                    if (deliveryDate > endDate) {
                        alert("Teslim tarihi bitiş tarihinden sonra olamaz!");
                        isValid = false;
                    }
                }

                if (!isValid) {
                    event.preventDefault();
                    event.stopPropagation();
                }

                form.classList.add("was-validated");
            }, false);
        });
    }, false);
})();

// Auto-set end date when start date changes - Fixed version
document.getElementById("start_date")?.addEventListener("change", function() {
    const startDateValue = this.value;
    const endDateField = document.getElementById("end_date");
    const deliveryDateField = document.getElementById("delivery_date");

    if (startDateValue) {
        try {
            const startDate = new Date(startDateValue + "T00:00:00");
            if (!isNaN(startDate.getTime())) {
                // Set minimum date for end date
                endDateField.min = startDateValue;

                // Auto-set end date if empty
                if (!endDateField.value) {
                    const endDate = new Date(startDate);
                    endDate.setDate(endDate.getDate() + 7);
                    endDateField.value = endDate.toISOString().split("T")[0];
                }

                // Set minimum date for delivery date
                deliveryDateField.min = startDateValue;

                // Auto-set delivery date if empty
                if (!deliveryDateField.value && endDateField.value) {
                    const endDate = new Date(endDateField.value + "T00:00:00");
                    const deliveryDate = new Date(endDate);
                    deliveryDate.setDate(deliveryDate.getDate() - 3);
                    if (deliveryDate >= startDate) {
                        deliveryDateField.value = deliveryDate.toISOString().split("T")[0];
                    }
                }
            }
        } catch (e) {
            console.log("Date parsing error:", e);
        }
    }
});

// Auto-set delivery date when end date changes - Fixed version
document.getElementById("end_date")?.addEventListener("change", function() {
    const endDateValue = this.value;
    const deliveryDateField = document.getElementById("delivery_date");
    const startDateValue = document.getElementById("start_date").value;

    if (endDateValue) {
        try {
            const endDate = new Date(endDateValue + "T00:00:00");
            if (!isNaN(endDate.getTime())) {
                // Set maximum date for delivery date
                deliveryDateField.max = endDateValue;

                // Auto-set delivery date if empty
                if (!deliveryDateField.value) {
                    const deliveryDate = new Date(endDate);
                    deliveryDate.setDate(deliveryDate.getDate() - 3);

                    // Make sure delivery date is not before start date
                    if (startDateValue) {
                        const startDate = new Date(startDateValue + "T00:00:00");
                        if (deliveryDate >= startDate) {
                            deliveryDateField.value = deliveryDate.toISOString().split("T")[0];
                        }
                    } else {
                        deliveryDateField.value = deliveryDate.toISOString().split("T")[0];
                    }
                }
            }
        } catch (e) {
            console.log("Date parsing error:", e);
        }
    }
});

// Load influencer platforms when influencer is selected
document.getElementById("influencer_id")?.addEventListener("change", function() {
    const influencerId = this.value;
    const platformSelect = document.getElementById("platform");

    if (influencerId) {
        // You can implement AJAX call here to get influencer platforms
        // For now, we keep all platforms available
        console.log("Selected influencer:", influencerId);
    }
});

// Suggested pricing based on platform and content type
const suggestedPrices = {
    "instagram": {
        "story": 1500,
        "post": 3000,
        "reels": 5000
    },
    "tiktok": {
        "video": 2500,
        "live": 4000
    },
    "youtube": {
        "video": 8000,
        "live": 6000
    },
    "twitter": {
        "post": 800,
        "thread": 1200
    }
};

function updateSuggestedPrice() {
    const platform = document.getElementById("platform").value;
    const contentType = document.getElementById("content_type").value;
    const priceField = document.getElementById("price");

    if (platform && contentType && suggestedPrices[platform] && suggestedPrices[platform][contentType]) {
        if (!priceField.value || priceField.value == 0) {
            priceField.value = suggestedPrices[platform][contentType];

            // Show suggestion message
            const suggestion = document.createElement("div");
            suggestion.className = "alert alert-info alert-dismissible fade show mt-2";
            suggestion.innerHTML = `
                <i class="fas fa-lightbulb me-2"></i>
                Önerilen fiyat: ${suggestedPrices[platform][contentType]} TL
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            const priceGroup = priceField.closest(".mb-3");
            const existingSuggestion = priceGroup.querySelector(".alert");
            if (existingSuggestion) {
                existingSuggestion.remove();
            }
            priceGroup.appendChild(suggestion);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (suggestion.parentNode) {
                    suggestion.remove();
                }
            }, 5000);
        }
    }
}

document.getElementById("platform")?.addEventListener("change", updateSuggestedPrice);
document.getElementById("content_type")?.addEventListener("change", updateSuggestedPrice);

// Date validation - Fixed version
function validateDates() {
    const startDateValue = document.getElementById("start_date").value;
    const endDateValue = document.getElementById("end_date").value;
    const deliveryDateValue = document.getElementById("delivery_date").value;

    // Only validate if both dates are filled
    if (startDateValue && endDateValue) {
        const startDate = new Date(startDateValue);
        const endDate = new Date(endDateValue);

        if (startDate > endDate) {
            alert("Bitiş tarihi başlangıç tarihinden önce olamaz!");
            document.getElementById("end_date").value = "";
            return false;
        }
    }

    // Validate delivery date
    if (deliveryDateValue && endDateValue) {
        const deliveryDate = new Date(deliveryDateValue);
        const endDate = new Date(endDateValue);

        if (deliveryDate > endDate) {
            alert("Teslim tarihi bitiş tarihinden sonra olamaz!");
            document.getElementById("delivery_date").value = "";
            return false;
        }
    }

    return true;
}

// Improved date event listeners with debouncing
let dateValidationTimeout;

function debouncedValidation() {
    clearTimeout(dateValidationTimeout);
    dateValidationTimeout = setTimeout(validateDates, 300);
}

document.getElementById("start_date")?.addEventListener("change", debouncedValidation);
document.getElementById("end_date")?.addEventListener("change", debouncedValidation);
document.getElementById("delivery_date")?.addEventListener("change", debouncedValidation);
</script>
';

// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
