/**
 * Influencer CMS - Custom Styles
 * Ana stil dosyası
 */

/* Global Styles */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.2s ease-in-out;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8f9fa;
    color: #495057;
    line-height: 1.6;
}

/* Sidebar Styles */
.sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 12px 20px;
    margin: 2px 10px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-right: 10px;
}

/* Header Styles */
.navbar {
    background: white;
    box-shadow: var(--box-shadow);
    border-bottom: 1px solid #e9ecef;
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 8px 16px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

/* Form Styles */
.form-control,
.form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Table Styles */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Badge Styles */
.badge {
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 20px;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
}

.alert-success {
    border-left-color: var(--success-color);
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    border-left-color: var(--danger-color);
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    border-left-color: var(--warning-color);
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    border-left-color: var(--info-color);
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Dashboard Stats Cards */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border-left: 4px solid;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-card.primary {
    border-left-color: var(--primary-color);
}

.stats-card.success {
    border-left-color: var(--success-color);
}

.stats-card.warning {
    border-left-color: var(--warning-color);
}

.stats-card.info {
    border-left-color: var(--info-color);
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        z-index: 1050;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.shadow-sm {
    box-shadow: var(--box-shadow) !important;
}

.border-radius {
    border-radius: var(--border-radius) !important;
}

/* Custom X (Twitter) Icon - Fallback if Font Awesome doesn't have it */
.fab.fa-x-twitter:before {
    content: "𝕏";
    font-family: "Times New Roman", serif;
    font-weight: bold;
    font-style: normal;
}

/* Alternative: If Unicode X doesn't work, use simple X */
.fab.fa-x-twitter.fallback:before {
    content: "X";
    font-family: Arial, sans-serif;
    font-weight: 900;
    font-style: normal;
}

/* X Platform specific styles */
.platform-x {
    background-color: #000000;
    color: white;
    border-radius: 8px;
    padding: 8px 12px;
}

.platform-x:hover {
    background-color: #333333;
}

/* X icon in different contexts */
.x-icon-small {
    font-size: 0.9em;
}

.x-icon-large {
    font-size: 1.5em;
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}
