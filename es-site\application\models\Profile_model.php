<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Profil Model
 * 
 * Escort profil işlemleri için model sınıfı
 * 
 * @package EscortSite
 * <AUTHOR> Site Developer
 * @version 1.0
 */
class Profile_model extends CI_Model {

    protected $table = 'profiles';

    public function __construct() {
        parent::__construct();
    }

    /**
     * Doğrulanmış profil sayısını getir
     */
    public function count_verified_profiles() {
        $this->db->where('is_verified', 1);
        return $this->db->count_all_results($this->table);
    }

    /**
     * Kullanıcının profilini getir
     */
    public function get_profile_by_user_id($user_id) {
        $this->db->select('p.*, c.name as city_name');
        $this->db->from($this->table . ' p');
        $this->db->join('cities c', 'c.id = p.city_id', 'left');
        $this->db->where('p.user_id', $user_id);
        return $this->db->get()->row();
    }

    /**
     * Profil oluştur
     */
    public function create_profile($data) {
        return $this->db->insert($this->table, $data);
    }

    /**
     * Profil güncelle
     */
    public function update_profile($user_id, $data) {
        $this->db->where('user_id', $user_id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Profil sil
     */
    public function delete_profile($user_id) {
        $this->db->where('user_id', $user_id);
        return $this->db->delete($this->table);
    }

    /**
     * Profil görüntülenme sayısını artır
     */
    public function increment_view_count($user_id) {
        $this->db->set('view_count', 'view_count + 1', FALSE);
        $this->db->where('user_id', $user_id);
        return $this->db->update($this->table);
    }

    /**
     * Premium profilleri getir
     */
    public function get_premium_profiles($limit = 10) {
        $this->db->select('p.*, c.name as city_name, u.username');
        $this->db->from($this->table . ' p');
        $this->db->join('cities c', 'c.id = p.city_id', 'left');
        $this->db->join('users u', 'u.id = p.user_id', 'left');
        $this->db->where('p.is_premium', 1);
        $this->db->where('(p.premium_expires IS NULL OR p.premium_expires > NOW())');
        $this->db->where('u.status', 'active');
        $this->db->order_by('p.created_at', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }

    /**
     * Şehir bazlı profiller
     */
    public function get_profiles_by_city($city_id, $limit = 20, $offset = 0) {
        $this->db->select('p.*, c.name as city_name, u.username');
        $this->db->from($this->table . ' p');
        $this->db->join('cities c', 'c.id = p.city_id', 'left');
        $this->db->join('users u', 'u.id = p.user_id', 'left');
        $this->db->where('p.city_id', $city_id);
        $this->db->where('u.status', 'active');
        $this->db->order_by('p.is_premium', 'DESC');
        $this->db->order_by('p.created_at', 'DESC');
        $this->db->limit($limit, $offset);
        
        return $this->db->get()->result();
    }

    /**
     * Profil arama
     */
    public function search_profiles($params, $limit = 20, $offset = 0) {
        $this->db->select('p.*, c.name as city_name, u.username');
        $this->db->from($this->table . ' p');
        $this->db->join('cities c', 'c.id = p.city_id', 'left');
        $this->db->join('users u', 'u.id = p.user_id', 'left');
        $this->db->where('u.status', 'active');

        // Arama filtreleri
        if (!empty($params['keyword'])) {
            $this->db->group_start();
            $this->db->like('p.display_name', $params['keyword']);
            $this->db->or_like('p.description', $params['keyword']);
            $this->db->group_end();
        }

        if (!empty($params['city_id'])) {
            $this->db->where('p.city_id', $params['city_id']);
        }

        if (!empty($params['min_age'])) {
            $this->db->where('p.age >=', $params['min_age']);
        }

        if (!empty($params['max_age'])) {
            $this->db->where('p.age <=', $params['max_age']);
        }

        if (!empty($params['is_verified'])) {
            $this->db->where('p.is_verified', 1);
        }

        if (!empty($params['is_premium'])) {
            $this->db->where('p.is_premium', 1);
            $this->db->where('(p.premium_expires IS NULL OR p.premium_expires > NOW())');
        }

        $this->db->order_by('p.is_premium', 'DESC');
        $this->db->order_by('p.is_verified', 'DESC');
        $this->db->order_by('p.created_at', 'DESC');
        $this->db->limit($limit, $offset);

        return $this->db->get()->result();
    }

    /**
     * Profil arama sonuç sayısı
     */
    public function count_search_profiles($params) {
        $this->db->from($this->table . ' p');
        $this->db->join('users u', 'u.id = p.user_id', 'left');
        $this->db->where('u.status', 'active');

        // Arama filtreleri (search_profiles ile aynı)
        if (!empty($params['keyword'])) {
            $this->db->group_start();
            $this->db->like('p.display_name', $params['keyword']);
            $this->db->or_like('p.description', $params['keyword']);
            $this->db->group_end();
        }

        if (!empty($params['city_id'])) {
            $this->db->where('p.city_id', $params['city_id']);
        }

        if (!empty($params['min_age'])) {
            $this->db->where('p.age >=', $params['min_age']);
        }

        if (!empty($params['max_age'])) {
            $this->db->where('p.age <=', $params['max_age']);
        }

        if (!empty($params['is_verified'])) {
            $this->db->where('p.is_verified', 1);
        }

        if (!empty($params['is_premium'])) {
            $this->db->where('p.is_premium', 1);
            $this->db->where('(p.premium_expires IS NULL OR p.premium_expires > NOW())');
        }

        return $this->db->count_all_results();
    }

    /**
     * Profil istatistikleri
     */
    public function get_profile_stats($user_id) {
        $profile = $this->get_profile_by_user_id($user_id);
        
        if (!$profile) {
            return null;
        }

        // İlan sayısı
        $this->db->where('user_id', $user_id);
        $this->db->where('status', 'active');
        $ad_count = $this->db->count_all_results('ads');

        // Mesaj sayısı
        $this->db->group_start();
        $this->db->where('sender_id', $user_id);
        $this->db->or_where('receiver_id', $user_id);
        $this->db->group_end();
        $message_count = $this->db->count_all_results('messages');

        return [
            'profile' => $profile,
            'ad_count' => $ad_count,
            'message_count' => $message_count,
            'view_count' => $profile->view_count,
            'rating' => $profile->rating,
            'review_count' => $profile->review_count
        ];
    }

    /**
     * Galeri fotoğraflarını güncelle
     */
    public function update_gallery_photos($user_id, $photos) {
        $data = ['gallery_photos' => json_encode($photos)];
        $this->db->where('user_id', $user_id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Profil fotoğrafını güncelle
     */
    public function update_profile_photo($user_id, $photo_path) {
        $data = ['profile_photo' => $photo_path];
        $this->db->where('user_id', $user_id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Premium üyelik güncelle
     */
    public function update_premium_status($user_id, $is_premium, $expires_at = null) {
        $data = [
            'is_premium' => $is_premium,
            'premium_expires' => $expires_at
        ];
        $this->db->where('user_id', $user_id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Doğrulama durumunu güncelle
     */
    public function update_verification_status($user_id, $is_verified) {
        $data = ['is_verified' => $is_verified];
        $this->db->where('user_id', $user_id);
        return $this->db->update($this->table, $data);
    }
}
