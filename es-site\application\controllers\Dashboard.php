<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Dashboard Controller
 * 
 * Kullanıcı dashboard işlemleri
 * 
 * @package EscortSite
 * <AUTHOR> Site Developer
 * @version 1.0
 */
class Dashboard extends CI_Controller {

    public function __construct() {
        parent::__construct();
        
        // Giriş kontrolü
        if (!$this->session->userdata('user_id')) {
            $this->session->set_flashdata('error', 'Bu sayfaya erişmek için giriş yapmalısınız.');
            redirect('auth/login');
        }
        
        // Model'leri yükle
        $this->load->model(['User_model', 'Ad_model', 'Profile_model', 'City_model', 'Category_model']);
        
        // Library'leri yükle
        $this->load->library(['upload', 'image_lib']);
    }

    /**
     * Dashboard ana sayfa
     */
    public function index() {
        $user_id = $this->session->userdata('user_id');
        $user_role = $this->session->userdata('role');
        
        $data['page_title'] = 'Dashboard - Escort İlan Sitesi';
        $data['user'] = $this->User_model->get_user_by_id($user_id);
        
        if ($user_role === 'escort') {
            // Escort dashboard verileri
            $data['profile'] = $this->Profile_model->get_profile_by_user_id($user_id);
            $data['ads'] = $this->Ad_model->get_user_ads($user_id, 5);
            $data['stats'] = [
                'total_ads' => count($this->Ad_model->get_user_ads($user_id)),
                'active_ads' => count(array_filter($this->Ad_model->get_user_ads($user_id), function($ad) {
                    return $ad->status === 'active';
                })),
                'total_views' => array_sum(array_map(function($ad) {
                    return $ad->view_count;
                }, $this->Ad_model->get_user_ads($user_id))),
                'messages' => 0 // Mesajlaşma sistemi eklendiğinde güncellenecek
            ];
            
            $this->load->view('templates/header', $data);
            $this->load->view('dashboard/escort_dashboard', $data);
            $this->load->view('templates/footer', $data);
            
        } else {
            // Müşteri dashboard verileri
            $data['recent_searches'] = []; // Arama geçmişi eklendiğinde güncellenecek
            $data['favorites'] = []; // Favori sistem eklendiğinde güncellenecek
            $data['stats'] = [
                'searches' => 0,
                'favorites' => 0,
                'messages' => 0
            ];
            
            $this->load->view('templates/header', $data);
            $this->load->view('dashboard/customer_dashboard', $data);
            $this->load->view('templates/footer', $data);
        }
    }

    /**
     * İlan oluşturma sayfası
     */
    public function create_ad() {
        // Sadece escort'lar ilan oluşturabilir
        if ($this->session->userdata('role') !== 'escort') {
            $this->session->set_flashdata('error', 'Bu işlemi yapmaya yetkiniz yok.');
            redirect('dashboard');
        }

        $user_id = $this->session->userdata('user_id');
        $data['page_title'] = 'Yeni İlan Oluştur - Dashboard';
        
        // Profil kontrolü
        $profile = $this->Profile_model->get_profile_by_user_id($user_id);
        if (!$profile) {
            $this->session->set_flashdata('error', 'İlan oluşturmak için önce profilinizi tamamlamalısınız.');
            redirect('dashboard/profile');
        }

        if ($this->input->post()) {
            $this->form_validation->set_rules('title', 'İlan Başlığı', 'required|min_length[10]|max_length[200]');
            $this->form_validation->set_rules('description', 'Açıklama', 'required|min_length[50]|max_length[2000]');
            $this->form_validation->set_rules('city_id', 'Şehir', 'required|numeric');
            $this->form_validation->set_rules('category_id', 'Kategori', 'required|numeric');
            $this->form_validation->set_rules('price', 'Fiyat', 'numeric');

            if ($this->form_validation->run()) {
                $ad_data = [
                    'user_id' => $user_id,
                    'title' => $this->input->post('title'),
                    'description' => $this->input->post('description'),
                    'city_id' => $this->input->post('city_id'),
                    'category_id' => $this->input->post('category_id'),
                    'price' => $this->input->post('price') ?: null,
                    'status' => 'pending', // Admin onayı bekliyor
                    'view_count' => 0,
                    'is_featured' => 0,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $ad_id = $this->Ad_model->create_ad($ad_data);

                if ($ad_id) {
                    $this->session->set_flashdata('success', 'İlanınız başarıyla oluşturuldu. Admin onayından sonra yayınlanacaktır.');
                    redirect('dashboard/my_ads');
                } else {
                    $this->session->set_flashdata('error', 'İlan oluşturulurken bir hata oluştu.');
                }
            }
        }

        // Form verileri
        $data['cities'] = $this->City_model->get_active_cities();
        $data['categories'] = $this->Category_model->get_active_categories();
        $data['profile'] = $profile;

        $this->load->view('templates/header', $data);
        $this->load->view('dashboard/create_ad', $data);
        $this->load->view('templates/footer', $data);
    }

    /**
     * İlanlarım sayfası
     */
    public function my_ads() {
        if ($this->session->userdata('role') !== 'escort') {
            $this->session->set_flashdata('error', 'Bu işlemi yapmaya yetkiniz yok.');
            redirect('dashboard');
        }

        $user_id = $this->session->userdata('user_id');
        $data['page_title'] = 'İlanlarım - Dashboard';
        $data['ads'] = $this->Ad_model->get_user_ads($user_id);

        $this->load->view('templates/header', $data);
        $this->load->view('dashboard/my_ads', $data);
        $this->load->view('templates/footer', $data);
    }

    /**
     * İlan düzenleme
     */
    public function edit_ad($ad_id) {
        if ($this->session->userdata('role') !== 'escort') {
            $this->session->set_flashdata('error', 'Bu işlemi yapmaya yetkiniz yok.');
            redirect('dashboard');
        }

        $user_id = $this->session->userdata('user_id');
        
        // İlan kontrolü
        $ad = $this->Ad_model->get_ad_with_profile($ad_id);
        if (!$ad || $ad->user_id != $user_id) {
            $this->session->set_flashdata('error', 'İlan bulunamadı veya bu ilana erişim yetkiniz yok.');
            redirect('dashboard/my_ads');
        }

        $data['page_title'] = 'İlan Düzenle - Dashboard';
        $data['ad'] = $ad;

        if ($this->input->post()) {
            $this->form_validation->set_rules('title', 'İlan Başlığı', 'required|min_length[10]|max_length[200]');
            $this->form_validation->set_rules('description', 'Açıklama', 'required|min_length[50]|max_length[2000]');
            $this->form_validation->set_rules('city_id', 'Şehir', 'required|numeric');
            $this->form_validation->set_rules('category_id', 'Kategori', 'required|numeric');
            $this->form_validation->set_rules('price', 'Fiyat', 'numeric');

            if ($this->form_validation->run()) {
                $update_data = [
                    'title' => $this->input->post('title'),
                    'description' => $this->input->post('description'),
                    'city_id' => $this->input->post('city_id'),
                    'category_id' => $this->input->post('category_id'),
                    'price' => $this->input->post('price') ?: null,
                    'status' => 'pending', // Düzenleme sonrası tekrar onay bekler
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if ($this->Ad_model->update_ad($ad_id, $update_data)) {
                    $this->session->set_flashdata('success', 'İlan başarıyla güncellendi. Admin onayından sonra yayınlanacaktır.');
                    redirect('dashboard/my_ads');
                } else {
                    $this->session->set_flashdata('error', 'İlan güncellenirken bir hata oluştu.');
                }
            }
        }

        // Form verileri
        $data['cities'] = $this->City_model->get_active_cities();
        $data['categories'] = $this->Category_model->get_active_categories();

        $this->load->view('templates/header', $data);
        $this->load->view('dashboard/edit_ad', $data);
        $this->load->view('templates/footer', $data);
    }

    /**
     * İlan silme
     */
    public function delete_ad($ad_id) {
        if ($this->session->userdata('role') !== 'escort') {
            $this->session->set_flashdata('error', 'Bu işlemi yapmaya yetkiniz yok.');
            redirect('dashboard');
        }

        $user_id = $this->session->userdata('user_id');
        
        // İlan kontrolü
        $ad = $this->Ad_model->get_ad_with_profile($ad_id);
        if (!$ad || $ad->user_id != $user_id) {
            $this->session->set_flashdata('error', 'İlan bulunamadı veya bu ilana erişim yetkiniz yok.');
            redirect('dashboard/my_ads');
        }

        if ($this->Ad_model->delete_ad($ad_id)) {
            $this->session->set_flashdata('success', 'İlan başarıyla silindi.');
        } else {
            $this->session->set_flashdata('error', 'İlan silinirken bir hata oluştu.');
        }

        redirect('dashboard/my_ads');
    }

    /**
     * Profil sayfası
     */
    public function profile() {
        $user_id = $this->session->userdata('user_id');
        $data['page_title'] = 'Profil - Dashboard';
        $data['user'] = $this->User_model->get_user_by_id($user_id);
        
        if ($this->session->userdata('role') === 'escort') {
            $data['profile'] = $this->Profile_model->get_profile_by_user_id($user_id);
            $data['cities'] = $this->City_model->get_active_cities();
            
            $this->load->view('templates/header', $data);
            $this->load->view('dashboard/escort_profile', $data);
            $this->load->view('templates/footer', $data);
        } else {
            $this->load->view('templates/header', $data);
            $this->load->view('dashboard/customer_profile', $data);
            $this->load->view('templates/footer', $data);
        }
    }

    /**
     * Profil güncelleme
     */
    public function update_profile() {
        $user_id = $this->session->userdata('user_id');
        
        if ($this->input->post()) {
            if ($this->session->userdata('role') === 'escort') {
                // Escort profil güncelleme
                $this->form_validation->set_rules('display_name', 'Görünen Ad', 'required|min_length[2]|max_length[100]');
                $this->form_validation->set_rules('age', 'Yaş', 'required|numeric|greater_than[17]|less_than[100]');
                $this->form_validation->set_rules('city_id', 'Şehir', 'required|numeric');
                $this->form_validation->set_rules('description', 'Açıklama', 'max_length[1000]');

                if ($this->form_validation->run()) {
                    $profile_data = [
                        'display_name' => $this->input->post('display_name'),
                        'age' => $this->input->post('age'),
                        'city_id' => $this->input->post('city_id'),
                        'description' => $this->input->post('description'),
                        'height' => $this->input->post('height'),
                        'weight' => $this->input->post('weight'),
                        'hair_color' => $this->input->post('hair_color'),
                        'eye_color' => $this->input->post('eye_color'),
                        'services' => json_encode($this->input->post('services') ?: []),
                        'price_range' => $this->input->post('price_range')
                    ];

                    if ($this->Profile_model->update_profile($user_id, $profile_data)) {
                        $this->session->set_flashdata('success', 'Profil başarıyla güncellendi.');
                    } else {
                        $this->session->set_flashdata('error', 'Profil güncellenirken bir hata oluştu.');
                    }
                }
            } else {
                // Müşteri profil güncelleme
                $this->form_validation->set_rules('username', 'Kullanıcı Adı', 'required|min_length[3]|max_length[50]');

                if ($this->form_validation->run()) {
                    $user_data = [
                        'username' => $this->input->post('username')
                    ];

                    if ($this->User_model->update_user($user_id, $user_data)) {
                        $this->session->set_userdata('username', $user_data['username']);
                        $this->session->set_flashdata('success', 'Profil başarıyla güncellendi.');
                    } else {
                        $this->session->set_flashdata('error', 'Profil güncellenirken bir hata oluştu.');
                    }
                }
            }
        }

        redirect('dashboard/profile');
    }
}
