<?php
/**
 * 403 Forbidden Error Page
 * Yetkisiz erişim hatası sayfası
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Set HTTP status code
http_response_code(403);

$page_title = '<PERSON><PERSON><PERSON><PERSON>gellendi - 403';

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<div class="container-fluid">
    <div class="row justify-content-center align-items-center min-vh-100">
        <div class="col-md-6 text-center">
            <div class="error-page">
                <div class="error-code mb-4">
                    <h1 class="display-1 text-danger fw-bold">403</h1>
                </div>
                
                <div class="error-message mb-4">
                    <h2 class="h3 mb-3">E<PERSON><PERSON><PERSON>gellendi</h2>
                    <p class="text-muted mb-4">
                        Bu sayfaya erişim yetkiniz bulunmuyor. Bu işlemi gerçekleştirmek için gerekli izinlere sahip değilsiniz.
                    </p>
                </div>
                
                <div class="error-actions">
                    <a href="<?php echo BASE_URL; ?>" class="btn btn-primary me-3">
                        <i class="fas fa-home me-2"></i>Ana Sayfaya Dön
                    </a>
                    
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <a href="dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-outline-secondary">
                            <i class="fas fa-sign-in-alt me-2"></i>Giriş Yap
                        </a>
                    <?php endif; ?>
                </div>
                
                <div class="error-details mt-5">
                    <details class="text-start">
                        <summary class="btn btn-link text-muted">Teknik Detaylar</summary>
                        <div class="mt-3 p-3 bg-light rounded">
                            <small class="text-muted">
                                <strong>Hata Kodu:</strong> 403 Forbidden<br>
                                <strong>Zaman:</strong> <?php echo date('d.m.Y H:i:s'); ?><br>
                                <strong>IP Adresi:</strong> <?php echo $_SERVER['REMOTE_ADDR'] ?? 'Bilinmiyor'; ?><br>
                                <strong>User Agent:</strong> <?php echo htmlspecialchars($_SERVER['HTTP_USER_AGENT'] ?? 'Bilinmiyor'); ?><br>
                                <strong>Talep Edilen URL:</strong> <?php echo htmlspecialchars($_SERVER['REQUEST_URI'] ?? ''); ?>
                            </small>
                        </div>
                    </details>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.min-vh-100 {
    min-height: 100vh;
}

.error-code h1 {
    font-size: 8rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-page {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .error-code h1 {
        font-size: 4rem;
    }
}
</style>

<?php
// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
