<?php
/**
 * User Management
 * Kullanıcı yönetimi ve yetkilendirme
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Check permissions
requirePermission('users_manage');

// Page settings
$page_title = 'Kullanıcı Yönetimi';
$action = $_GET['action'] ?? 'list';
$user_id = $_GET['id'] ?? null;

// User roles
$roles = [
    ROLE_ADMIN => 'Yönetici',
    ROLE_MANAGER => 'Müdür',
    ROLE_EDITOR => 'Editör',
    ROLE_VIEWER => 'Görüntüleyici'
];

// User statuses
$user_statuses = [
    'active' => 'Aktif',
    'inactive' => 'Pasif',
    'suspended' => 'Askıya Alınmış'
];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        global $database;
        $pdo = $database->connect();
        
        if ($action === 'add') {
            // Add new user
            $stmt = $pdo->prepare("
                INSERT INTO users (username, email, password, full_name, role, status) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $password_hash = password_hash($_POST['password'], PASSWORD_DEFAULT);
            
            $stmt->execute([
                sanitizeInput($_POST['username']),
                sanitizeInput($_POST['email']),
                $password_hash,
                sanitizeInput($_POST['full_name']),
                $_POST['role'],
                $_POST['status'] ?? 'active'
            ]);
            
            $_SESSION['success_message'] = 'Kullanıcı başarıyla eklendi.';
            redirectTo('users.php');
            
        } elseif ($action === 'edit') {
            // Update user
            if (!empty($_POST['password'])) {
                // Update with new password
                $stmt = $pdo->prepare("
                    UPDATE users 
                    SET username = ?, email = ?, password = ?, full_name = ?, role = ?, status = ?
                    WHERE id = ?
                ");
                
                $password_hash = password_hash($_POST['password'], PASSWORD_DEFAULT);
                
                $stmt->execute([
                    sanitizeInput($_POST['username']),
                    sanitizeInput($_POST['email']),
                    $password_hash,
                    sanitizeInput($_POST['full_name']),
                    $_POST['role'],
                    $_POST['status'],
                    $user_id
                ]);
            } else {
                // Update without password change
                $stmt = $pdo->prepare("
                    UPDATE users 
                    SET username = ?, email = ?, full_name = ?, role = ?, status = ?
                    WHERE id = ?
                ");
                
                $stmt->execute([
                    sanitizeInput($_POST['username']),
                    sanitizeInput($_POST['email']),
                    sanitizeInput($_POST['full_name']),
                    $_POST['role'],
                    $_POST['status'],
                    $user_id
                ]);
            }
            
            $_SESSION['success_message'] = 'Kullanıcı bilgileri güncellendi.';
            redirectTo('users.php');
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'İşlem sırasında bir hata oluştu: ' . $e->getMessage();
        error_log('User operation error: ' . $e->getMessage());
    }
}

// Handle delete action
if ($action === 'delete' && $user_id) {
    try {
        global $database;
        $pdo = $database->connect();
        
        // Prevent deleting own account
        if ($user_id == $_SESSION['user_id']) {
            $_SESSION['error_message'] = 'Kendi hesabınızı silemezsiniz.';
        } else {
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            
            $_SESSION['success_message'] = 'Kullanıcı silindi.';
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Silme işlemi sırasında bir hata oluştu.';
        error_log('User delete error: ' . $e->getMessage());
    }
    
    redirectTo('users.php');
}

// Get data based on action
if ($action === 'list') {
    try {
        global $database;
        $pdo = $database->connect();
        
        $search = $_GET['search'] ?? '';
        $role_filter = $_GET['role'] ?? '';
        $status_filter = $_GET['status'] ?? '';
        
        $where_conditions = [];
        $params = [];
        
        if (!empty($search)) {
            $where_conditions[] = "(username LIKE ? OR email LIKE ? OR full_name LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($role_filter)) {
            $where_conditions[] = "role = ?";
            $params[] = $role_filter;
        }
        
        if (!empty($status_filter)) {
            $where_conditions[] = "status = ?";
            $params[] = $status_filter;
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        $stmt = $pdo->prepare("
            SELECT * FROM users 
            $where_clause
            ORDER BY created_at DESC
        ");
        $stmt->execute($params);
        $users = $stmt->fetchAll();
        
    } catch (Exception $e) {
        $users = [];
        error_log('User list error: ' . $e->getMessage());
    }
    
} elseif ($action === 'edit' && $user_id) {
    try {
        global $database;
        $pdo = $database->connect();
        
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
        
        if (!$user) {
            $_SESSION['error_message'] = 'Kullanıcı bulunamadı.';
            redirectTo('users.php');
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Kullanıcı bilgileri alınamadı.';
        redirectTo('users.php');
    }
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <!-- User List -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-users-cog me-2"></i>
                        Kullanıcı Listesi (<?php echo count($users); ?>)
                    </h5>
                    <a href="users.php?action=add" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>Yeni Kullanıcı
                    </a>
                </div>
                
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" class="row g-3 mb-4">
                        <div class="col-md-4">
                            <input type="text" class="form-control" name="search" 
                                   value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                                   placeholder="Kullanıcı adı, e-posta, isim ara...">
                        </div>
                        <div class="col-md-3">
                            <select name="role" class="form-select">
                                <option value="">Tüm Roller</option>
                                <?php foreach ($roles as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" <?php echo ($_GET['role'] ?? '') === $key ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="">Tüm Durumlar</option>
                                <?php foreach ($user_statuses as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" <?php echo ($_GET['status'] ?? '') === $key ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search"></i> Filtrele
                            </button>
                        </div>
                    </form>
                    
                    <!-- User Table -->
                    <?php if (!empty($users)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Kullanıcı</th>
                                        <th>E-posta</th>
                                        <th>Rol</th>
                                        <th>Durum</th>
                                        <th>Son Giriş</th>
                                        <th>Kayıt Tarihi</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($user['avatar']): ?>
                                                        <img src="<?php echo UPLOAD_URL . 'avatars/' . $user['avatar']; ?>" 
                                                             class="rounded-circle me-2" width="32" height="32" style="object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                             style="width: 32px; height: 32px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>
                                                        <br><small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $user['role'] === ROLE_ADMIN ? 'danger' : ($user['role'] === ROLE_MANAGER ? 'warning' : 'info'); ?>">
                                                    <?php echo $roles[$user['role']] ?? $user['role']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo STATUS_COLORS[$user['status']] ?? 'secondary'; ?>">
                                                    <?php echo $user_statuses[$user['status']] ?? $user['status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($user['last_login']): ?>
                                                    <small><?php echo date('d.m.Y H:i', strtotime($user['last_login'])); ?></small>
                                                <?php else: ?>
                                                    <small class="text-muted">Hiç giriş yapmamış</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small><?php echo date('d.m.Y', strtotime($user['created_at'])); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="users.php?action=edit&id=<?php echo $user['id']; ?>" 
                                                       class="btn btn-outline-warning" data-bs-toggle="tooltip" title="Düzenle">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                        <a href="users.php?action=delete&id=<?php echo $user['id']; ?>" 
                                                           class="btn btn-outline-danger" data-bs-toggle="tooltip" title="Sil"
                                                           onclick="return confirmDelete('Bu kullanıcıyı silmek istediğinizden emin misiniz?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Kullanıcı bulunamadı</h5>
                            <p class="text-muted">Arama kriterlerinizi değiştirin veya yeni kullanıcı ekleyin.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit User Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-<?php echo $action === 'add' ? 'user-plus' : 'user-edit'; ?> me-2"></i>
                        <?php echo $action === 'add' ? 'Yeni Kullanıcı Ekle' : 'Kullanıcı Düzenle'; ?>
                    </h5>
                </div>
                
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Kullanıcı Adı <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($user['username'] ?? ''); ?>" required>
                                <div class="invalid-feedback">Kullanıcı adı zorunludur.</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">E-posta <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                                <div class="invalid-feedback">Geçerli bir e-posta adresi girin.</div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="full_name" class="form-label">Tam Adı <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>" required>
                            <div class="invalid-feedback">Tam ad zorunludur.</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">Rol <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Rol Seçin</option>
                                    <?php foreach ($roles as $key => $name): ?>
                                        <option value="<?php echo $key; ?>" 
                                                <?php echo ($user['role'] ?? '') === $key ? 'selected' : ''; ?>>
                                            <?php echo $name; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Rol seçimi zorunludur.</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Durum</label>
                                <select class="form-select" id="status" name="status">
                                    <?php foreach ($user_statuses as $key => $name): ?>
                                        <option value="<?php echo $key; ?>" 
                                                <?php echo ($user['status'] ?? 'active') === $key ? 'selected' : ''; ?>>
                                            <?php echo $name; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                Şifre 
                                <?php if ($action === 'add'): ?>
                                    <span class="text-danger">*</span>
                                <?php else: ?>
                                    <small class="text-muted">(Değiştirmek için doldurun)</small>
                                <?php endif; ?>
                            </label>
                            <input type="password" class="form-control" id="password" name="password" 
                                   <?php echo $action === 'add' ? 'required' : ''; ?>>
                            <?php if ($action === 'add'): ?>
                                <div class="invalid-feedback">Şifre zorunludur.</div>
                            <?php endif; ?>
                            <div class="form-text">Minimum 6 karakter olmalıdır.</div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between">
                            <a href="users.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Geri Dön
                            </a>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                <?php echo $action === 'add' ? 'Kullanıcı Ekle' : 'Değişiklikleri Kaydet'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
// Page specific scripts
$page_scripts = '
<script>
// Form validation
(function() {
    "use strict";
    window.addEventListener("load", function() {
        var forms = document.getElementsByClassName("needs-validation");
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener("submit", function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add("was-validated");
            }, false);
        });
    }, false);
})();

// Password strength indicator
document.getElementById("password")?.addEventListener("input", function() {
    const password = this.value;
    const strength = calculatePasswordStrength(password);
    
    // You can add password strength indicator here
});

function calculatePasswordStrength(password) {
    let strength = 0;
    if (password.length >= 6) strength++;
    if (password.match(/[a-z]/)) strength++;
    if (password.match(/[A-Z]/)) strength++;
    if (password.match(/[0-9]/)) strength++;
    if (password.match(/[^a-zA-Z0-9]/)) strength++;
    return strength;
}
</script>
';

// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
