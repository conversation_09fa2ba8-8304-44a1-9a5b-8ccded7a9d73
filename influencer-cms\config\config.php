<?php
/**
 * Main Configuration File
 * Influencer CMS - Global Settings
 */

// Prevent direct access
if (!defined('CMS_ROOT')) {
    define('CMS_ROOT', dirname(__DIR__));
}

// Error Reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Europe/Istanbul');

// Session Configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Set to 1 for HTTPS
session_start();

// Application Configuration
define('APP_NAME', 'Influencer CMS');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/influencer-cms');
define('BASE_URL', 'http://localhost/influencer-cms');
define('ADMIN_EMAIL', '<EMAIL>');

// File Upload Configuration
define('UPLOAD_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('UPLOAD_PATH', CMS_ROOT . '/uploads/');
define('UPLOAD_URL', APP_URL . '/uploads/');

// Allowed file types
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']);
define('ALLOWED_FILE_TYPES', array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_DOCUMENT_TYPES));

// Pagination
define('ITEMS_PER_PAGE', 20);
define('PAGINATION_LINKS', 5);

// Security
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour

// User Roles
define('ROLE_ADMIN', 'admin');
define('ROLE_MANAGER', 'manager');
define('ROLE_EDITOR', 'editor');
define('ROLE_VIEWER', 'viewer');

// User Permissions
define('PERMISSIONS', [
    ROLE_ADMIN => [
        'users_manage', 'users_view', 'influencers_manage', 'influencers_view', 'influencers_edit',
        'campaigns_manage', 'campaigns_view', 'campaigns_edit', 'brands_manage', 'brands_view', 'brands_edit',
        'files_manage', 'files_view', 'files_upload', 'notes_manage', 'notes_view', 'notes_create',
        'todos_manage', 'todos_view', 'todos_create', 'settings_manage', 'reports_view'
    ],
    ROLE_MANAGER => [
        'influencers_manage', 'influencers_view', 'influencers_edit', 'campaigns_manage', 'campaigns_view', 'campaigns_edit',
        'brands_manage', 'brands_view', 'brands_edit', 'files_manage', 'files_view', 'files_upload',
        'notes_manage', 'notes_view', 'notes_create', 'todos_manage', 'todos_view', 'todos_create', 'reports_view'
    ],
    ROLE_EDITOR => [
        'influencers_view', 'influencers_edit', 'campaigns_view', 'campaigns_edit', 'brands_view', 'brands_edit',
        'files_view', 'files_upload', 'notes_view', 'notes_create', 'todos_view', 'todos_create'
    ],
    ROLE_VIEWER => [
        'influencers_view', 'campaigns_view', 'brands_view', 'files_view', 'notes_view', 'todos_view'
    ]
]);

// Platform Configuration
define('PLATFORMS', [
    'instagram' => [
        'name' => 'Instagram',
        'icon' => 'fab fa-instagram',
        'color' => '#E4405F',
        'url_pattern' => 'https://instagram.com/{username}'
    ],
    'tiktok' => [
        'name' => 'TikTok',
        'icon' => 'fab fa-tiktok',
        'color' => '#000000',
        'url_pattern' => 'https://tiktok.com/@{username}'
    ],
    'youtube' => [
        'name' => 'YouTube',
        'icon' => 'fab fa-youtube',
        'color' => '#FF0000',
        'url_pattern' => 'https://youtube.com/@{username}'
    ],
    'twitter' => [
        'name' => 'X (Twitter)',
        'icon' => 'fab fa-x-twitter',
        'color' => '#000000',
        'url_pattern' => 'https://x.com/{username}'
    ],
    'facebook' => [
        'name' => 'Facebook',
        'icon' => 'fab fa-facebook',
        'color' => '#1877F2',
        'url_pattern' => 'https://facebook.com/{username}'
    ],
    'twitch' => [
        'name' => 'Twitch',
        'icon' => 'fab fa-twitch',
        'color' => '#9146FF',
        'url_pattern' => 'https://twitch.tv/{username}'
    ],
    'pinterest' => [
        'name' => 'Pinterest',
        'icon' => 'fab fa-pinterest',
        'color' => '#BD081C',
        'url_pattern' => 'https://pinterest.com/{username}'
    ],
    'linkedin' => [
        'name' => 'LinkedIn',
        'icon' => 'fab fa-linkedin',
        'color' => '#0A66C2',
        'url_pattern' => 'https://linkedin.com/in/{username}'
    ]
]);

// Content Types
$content_types = [
    'story' => [
        'name' => 'Story',
        'icon' => 'fas fa-circle',
        'description' => '24 saatlik story paylaşımı'
    ],
    'post' => [
        'name' => 'Post',
        'icon' => 'fas fa-image',
        'description' => 'Kalıcı gönderi paylaşımı'
    ],
    'reels' => [
        'name' => 'Reels/Shorts',
        'icon' => 'fas fa-video',
        'description' => 'Kısa video içeriği'
    ],
    'video' => [
        'name' => 'Video',
        'icon' => 'fas fa-play',
        'description' => 'Uzun video içeriği'
    ],
    'live' => [
        'name' => 'Canlı Yayın',
        'icon' => 'fas fa-broadcast-tower',
        'description' => 'Canlı yayın'
    ],
    'package' => [
        'name' => 'Paket',
        'icon' => 'fas fa-box',
        'description' => 'Birden fazla içerik paketi'
    ]
];

// Currency Configuration
define('CURRENCIES', [
    'TRY' => [
        'name' => 'Türk Lirası',
        'symbol' => '₺',
        'code' => 'TRY'
    ],
    'USD' => [
        'name' => 'US Dollar',
        'symbol' => '$',
        'code' => 'USD'
    ],
    'EUR' => [
        'name' => 'Euro',
        'symbol' => '€',
        'code' => 'EUR'
    ]
]);

// Status Colors
define('STATUS_COLORS', [
    'active' => 'success',
    'inactive' => 'secondary',
    'suspended' => 'danger',
    'blacklisted' => 'dark',
    'pending' => 'warning',
    'in_progress' => 'info',
    'completed' => 'success',
    'cancelled' => 'danger',
    'delayed' => 'warning'
]);

// Priority Colors
define('PRIORITY_COLORS', [
    'low' => 'secondary',
    'medium' => 'primary',
    'high' => 'warning',
    'urgent' => 'danger'
]);

// Helper Functions
function hasPermission($permission) {
    if (!isset($_SESSION['user_role'])) {
        return false;
    }

    $user_role = $_SESSION['user_role'];
    $permissions = PERMISSIONS;

    // Admin has all permissions
    if ($user_role === ROLE_ADMIN) {
        return true;
    }

    return isset($permissions[$user_role]) && in_array($permission, $permissions[$user_role]);
}

function formatCurrency($amount, $currency = 'TRY') {
    $currencies = [
        'TRY' => ['symbol' => '₺'],
        'USD' => ['symbol' => '$'],
        'EUR' => ['symbol' => '€']
    ];

    if (!isset($currencies[$currency])) {
        $currency = 'TRY';
    }

    return number_format($amount, 2, ',', '.') . ' ' . $currencies[$currency]['symbol'];
}

function formatNumber($number) {
    if ($number >= 1000000) {
        return round($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return round($number / 1000, 1) . 'K';
    }
    return number_format($number);
}

function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function redirectTo($url) {
    header("Location: " . $url);
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) &&
           isset($_SESSION['user_role']) &&
           isset($_SESSION['logged_in']) &&
           $_SESSION['logged_in'] === true;
}

function requireLogin() {
    if (!isLoggedIn()) {
        redirectTo('login.php');
    }
}

function requirePermission($permission) {
    requireLogin();

    // Admin has all permissions
    if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === ROLE_ADMIN) {
        return true;
    }

    if (!hasPermission($permission)) {
        $_SESSION['error_message'] = 'Bu işlem için yetkiniz bulunmuyor.';
        redirectTo('403.php');
    }

    return true;
}

// Auto-load classes
spl_autoload_register(function ($class_name) {
    $directories = [
        CMS_ROOT . '/classes/',
        CMS_ROOT . '/models/',
        CMS_ROOT . '/controllers/'
    ];
    
    foreach ($directories as $directory) {
        $file = $directory . $class_name . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Include database configuration
require_once CMS_ROOT . '/config/database.php';

// Create database instance
$database = new Database();

?>
