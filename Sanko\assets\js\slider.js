$(document).ready(function () {

$('.products-category').slick({
  dots: false,
  infinite: true,
  speed: 500,
  slidesToShow: 3,
  slidesToScroll: 1,
  autoplay: true,
  autoplaySpeed: 5000,
  arrows: true,
  nextArrow: '<div class="slick-custom-arrow slick-custom-arrow-right"><img src="assets/images/slide_right.svg" alt=""></div>',
  prevArrow: '<div class="slick-custom-arrow slick-custom-arrow-left"><img src="assets/images/slide_left.svg" alt=""></div>',
  responsive: [
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 3,
      }
    },
    {
      breakpoint: 991,
      settings: {
        slidesToShow: 1,
      }
    },
    {
      breakpoint: 500,
      settings: {
        slidesToShow: 1,
      }
    },
    ]
});


$('.products-detail').slick({
  dots: false,
  infinite: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
  autoplay: false,
  autoplaySpeed: 5000,
  arrows: true,
  asNavFor: '.slider-nav'
  
});

$('.slider-nav').slick({
  slidesToShow: 4,
  slidesToScroll: 1,
  asNavFor: '.products-detail',
  dots: false,
  arrows: false,
  focusOnSelect: true,
  autoplay: false,
});

$('.slider-v2').slick({
  slidesToShow: 1,
  slidesToScroll: 1,
  dots: false,
  focusOnSelect: true,
  fade: true,
  cssEase: 'linear',
  speed: 500,
  arrows: true,
  nextArrow: '<div class="slick-custom-arrow slick-custom-arrow-right"><img src="assets/images/right.svg" style="width:80px;" alt=""></div>',
  prevArrow: '<div class="slick-custom-arrow slick-custom-arrow-left"><img src="assets/images/left.svg" style="width:80px;" alt=""></div>',
});

$('.news_slider').slick({
  slidesToShow: 2,
  slidesToScroll: 1, 
  dots: false,
  fade: false,
  infinite: true,
  autoplay: true,
  autoplaySpeed: 5000,
  cssEase: 'ease',
  speed: 500,
  margin:10,
  arrows: true,
  nextArrow: '<div class="slick-custom-arrow slick-custom-arrow-right"><img src="assets/images/news_right.svg" style="width:30px;" alt=""></div>',
  prevArrow: '<div class="slick-custom-arrow slick-custom-arrow-left"><img src="assets/images/news_left.svg" style="width:30px;" alt=""></div>',
  accessibility: true,
  responsive: [
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1
      }
    }
  ],
  // Erişilebilirlik düzeltmesi
  beforeChange: function(event, slick, currentSlide, nextSlide) {
    // Tüm slide'lardaki odaklanabilir elementleri devre dışı bırak
    $('.slick-slide:not(.slick-current) a, .slick-slide:not(.slick-current) button').attr('tabindex', '-1');
  },
  afterChange: function(event, slick, currentSlide) {
    // Sadece aktif slide'daki odaklanabilir elementleri etkinleştir
    $('.slick-current a, .slick-current button').attr('tabindex', '0');
  }
});

// Erişilebilirlik düzeltmesi için ek kod
$(document).ready(function() {
  // Slick başlatıldıktan sonra çalışacak
  setTimeout(function() {
    // aria-hidden kullanımını düzelt
    $('.news_slider .slick-slide').each(function() {
      if ($(this).attr('aria-hidden') === 'true') {
        $(this).removeAttr('aria-hidden');
        if ('inert' in this) {
          this.inert = true;
        }
        $(this).find('a, button').attr('tabindex', '-1');
      } else {
        if ('inert' in this) {
          this.inert = false;
        }
        $(this).find('a, button').attr('tabindex', '0');
      }
    });
  }, 500);
  
  // Slide değiştiğinde
  $('.news_slider').on('afterChange', function() {
    $('.news_slider .slick-slide').each(function() {
      if ($(this).hasClass('slick-active')) {
        $(this).removeAttr('aria-hidden');
        if ('inert' in this) {
          this.inert = false;
        }
        $(this).find('a, button').attr('tabindex', '0');
      } else {
        $(this).removeAttr('aria-hidden');
        if ('inert' in this) {
          this.inert = true;
        }
        $(this).find('a, button').attr('tabindex', '-1');
      }
    });
  });
});

$('.slider_sosyal').slick({
  dots: true,
  arrows: false,
  adaptiveHeight: true,
  fade: true,
  cssEase: 'linear',
  speed: 500
});

// Sayfa ilk açıldığında, aktif olan tab'daki slider'ı başlat
$('.slider-okuloncesi').slick({
  dots: true,
  arrows: false,
  adaptiveHeight: true,
  fade: true,
  cssEase: 'linear',
  speed: 500
});

// Diğer tablar ilk kez açılınca slider başlat
$('button[data-bs-toggle="tab"]').on('shown.bs.tab', function() {   
  var $slider = $($(this).data('bs-target')).find('.slider-ilkokul, .slider-ortaokul, .slider-kolej');

  if ($slider.length && !$slider.hasClass('slick-initialized')) {
    $slider.slick({
      dots: true,
      arrows: false,
      adaptiveHeight: true,
      fade: true,
      cssEase: 'linear',
      speed: 500
    });
  } else if ($slider.hasClass('slick-initialized')) {
    // Eğer slider zaten başlatılmışsa, yeniden konumlandır
    $slider.slick('setPosition');
  }
});

// Tab slider'ı için erişilebilirlik ayarları ile yapılandırma
$('.custom-tabs').slick({
  slidesToShow: 4,
  infinite: true,
  centerMode: true,
  variableWidth: true,
  arrows: false,
  dots: false,
  swipeToSlide: true,
  accessibility: true,
  focusOnSelect: true
});

// İlk başta aktif
$('.custom-tabs .nav-link.active').parent().addClass('slick-current');

// Slick değişince aktif class güncelle
$('.custom-tabs').on('afterChange', function(event, slick, currentSlide){
  $('.custom-tabs .nav-link').removeClass('active');
  $(slick.$slides[currentSlide]).find('.nav-link').addClass('active');
});




// Tab butonlarına tıklama işlevi
$('.custom-tabs .nav-link').on('click', function(e) {
  // Klon slide'a tıklanmasın
  if ($(this).closest('.slick-slide').hasClass('slick-cloned')) {
    e.preventDefault();
    e.stopPropagation();
    return false;
  }

  var target = $(this).data('bs-target');

  // Tab-content güncelle
  $('.tab-pane').removeClass('show active');
  $(target).addClass('show active');

  // Aktif class güncelle
  $('.custom-tabs .nav-link').removeClass('active');
  $(this).addClass('active');

  // Slider'da ilgili index'e git
  var clickedIndex = $(this).closest('.slick-slide').data('slick-index');
  $('.custom-tabs').slick('slickGoTo', clickedIndex);

  // İçerideki slider'ı düzelt
  var $innerSlider = $(target).find('.slider-okuloncesi, .slider-ilkokul, .slider-ortaokul, .slider-kolej');
  
  if ($innerSlider.hasClass('slick-initialized')) {
    $innerSlider.slick('setPosition');
  }

  // Flex veya grid bozuk gelmesin diye force repaint
  setTimeout(() => {
    $(target)[0].offsetWidth;
    window.dispatchEvent(new Event('resize'));
  }, 50);
});



});