<?php
/**
 * Simple Test Page - No Permission Required
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Only require login, no specific permission
requireLogin();

$page_title = 'Simple Test Page';
include CMS_ROOT . '/includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Test Sayfası - Başarılı!
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h4>Tebrikler! 🎉</h4>
                        <p>Bu sayfayı görebiliyorsanız, giriş sistemi çalışıyor demektir.</p>
                    </div>
                    
                    <h6>Session Bilgileri:</h6>
                    <ul>
                        <li><strong>User ID:</strong> <?php echo $_SESSION['user_id'] ?? 'Yok'; ?></li>
                        <li><strong>Username:</strong> <?php echo $_SESSION['username'] ?? 'Yok'; ?></li>
                        <li><strong>Role:</strong> <?php echo $_SESSION['user_role'] ?? 'Yok'; ?></li>
                        <li><strong>Full Name:</strong> <?php echo $_SESSION['full_name'] ?? 'Yok'; ?></li>
                        <li><strong>Logged In:</strong> <?php echo ($_SESSION['logged_in'] ?? false) ? 'Evet' : 'Hayır'; ?></li>
                    </ul>
                    
                    <h6>Permission Testi:</h6>
                    <ul>
                        <li><strong>influencers_view:</strong> <?php echo hasPermission('influencers_view') ? '✅ Var' : '❌ Yok'; ?></li>
                        <li><strong>users_manage:</strong> <?php echo hasPermission('users_manage') ? '✅ Var' : '❌ Yok'; ?></li>
                        <li><strong>campaigns_view:</strong> <?php echo hasPermission('campaigns_view') ? '✅ Var' : '❌ Yok'; ?></li>
                    </ul>
                    
                    <div class="mt-4">
                        <h6>Test Linkleri:</h6>
                        <div class="btn-group" role="group">
                            <a href="dashboard.php" class="btn btn-primary">Dashboard</a>
                            <a href="influencers.php" class="btn btn-info">Influencers</a>
                            <a href="users.php" class="btn btn-warning">Users</a>
                            <a href="logout.php" class="btn btn-danger">Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
include CMS_ROOT . '/includes/footer.php';
?>
