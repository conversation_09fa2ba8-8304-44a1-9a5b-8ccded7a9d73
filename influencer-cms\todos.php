<?php
/**
 * Todo List Management
 * Görev yönetimi sistemi
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Check permissions
requirePermission('todos_view');

// Page settings
$page_title = 'Todo List';
$action = $_GET['action'] ?? 'list';
$todo_id = $_GET['id'] ?? null;

// Todo priorities
$priorities = [
    'low' => 'Düşük',
    'medium' => 'Orta',
    'high' => 'Yüksek',
    'urgent' => 'Acil'
];

// Todo statuses
$statuses = [
    'pending' => 'Bekliyor',
    'in_progress' => 'Devam Ediyor',
    'completed' => 'Tamamlandı',
    'cancelled' => 'İptal Edildi'
];

// Todo categories
$categories = [
    'general' => 'Genel',
    'influencer' => 'Influencer',
    'campaign' => 'Kampanya',
    'admin' => 'Yönetim',
    'follow_up' => 'Takip'
];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        global $database;
        $pdo = $database->connect();
        
        if ($action === 'add' && hasPermission('todos_create')) {
            // Add new todo
            $stmt = $pdo->prepare("
                INSERT INTO todos (title, description, priority, status, due_date, assigned_to, 
                                 influencer_id, campaign_id, category, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                sanitizeInput($_POST['title']),
                sanitizeInput($_POST['description']),
                $_POST['priority'] ?? 'medium',
                $_POST['status'] ?? 'pending',
                $_POST['due_date'] ?: null,
                $_POST['assigned_to'] ?: null,
                $_POST['influencer_id'] ?: null,
                $_POST['campaign_id'] ?: null,
                $_POST['category'] ?? 'general',
                $_SESSION['user_id']
            ]);
            
            $_SESSION['success_message'] = 'Görev başarıyla eklendi.';
            redirectTo('todos.php');
            
        } elseif ($action === 'edit' && hasPermission('todos_manage')) {
            // Update todo
            $completed_at = null;
            if ($_POST['status'] === 'completed' && $_POST['old_status'] !== 'completed') {
                $completed_at = date('Y-m-d H:i:s');
            } elseif ($_POST['status'] !== 'completed') {
                $completed_at = null;
            }
            
            $stmt = $pdo->prepare("
                UPDATE todos 
                SET title = ?, description = ?, priority = ?, status = ?, due_date = ?, 
                    assigned_to = ?, influencer_id = ?, campaign_id = ?, category = ?, 
                    completed_at = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                sanitizeInput($_POST['title']),
                sanitizeInput($_POST['description']),
                $_POST['priority'],
                $_POST['status'],
                $_POST['due_date'] ?: null,
                $_POST['assigned_to'] ?: null,
                $_POST['influencer_id'] ?: null,
                $_POST['campaign_id'] ?: null,
                $_POST['category'],
                $completed_at,
                $todo_id
            ]);
            
            $_SESSION['success_message'] = 'Görev güncellendi.';
            redirectTo('todos.php');
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'İşlem sırasında bir hata oluştu: ' . $e->getMessage();
        error_log('Todo operation error: ' . $e->getMessage());
    }
}

// Handle quick status update
if ($action === 'update_status' && $todo_id && hasPermission('todos_manage')) {
    try {
        global $database;
        $pdo = $database->connect();
        
        $new_status = $_GET['status'] ?? 'pending';
        $completed_at = ($new_status === 'completed') ? date('Y-m-d H:i:s') : null;
        
        $stmt = $pdo->prepare("UPDATE todos SET status = ?, completed_at = ? WHERE id = ?");
        $stmt->execute([$new_status, $completed_at, $todo_id]);
        
        $_SESSION['success_message'] = 'Görev durumu güncellendi.';
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Durum güncellenemedi.';
        error_log('Todo status update error: ' . $e->getMessage());
    }
    
    redirectTo('todos.php');
}

// Handle delete action
if ($action === 'delete' && $todo_id && hasPermission('todos_manage')) {
    try {
        global $database;
        $pdo = $database->connect();
        
        $stmt = $pdo->prepare("DELETE FROM todos WHERE id = ?");
        $stmt->execute([$todo_id]);
        
        $_SESSION['success_message'] = 'Görev silindi.';
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Silme işlemi sırasında bir hata oluştu.';
        error_log('Todo delete error: ' . $e->getMessage());
    }
    
    redirectTo('todos.php');
}

// Get data based on action
if ($action === 'list') {
    try {
        global $database;
        $pdo = $database->connect();
        
        $search = $_GET['search'] ?? '';
        $status_filter = $_GET['status'] ?? '';
        $priority_filter = $_GET['priority'] ?? '';
        $assigned_filter = $_GET['assigned'] ?? '';
        
        $where_conditions = [];
        $params = [];
        
        if (!empty($search)) {
            $where_conditions[] = "(t.title LIKE ? OR t.description LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($status_filter)) {
            $where_conditions[] = "t.status = ?";
            $params[] = $status_filter;
        }
        
        if (!empty($priority_filter)) {
            $where_conditions[] = "t.priority = ?";
            $params[] = $priority_filter;
        }
        
        if (!empty($assigned_filter)) {
            $where_conditions[] = "t.assigned_to = ?";
            $params[] = $assigned_filter;
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        $stmt = $pdo->prepare("
            SELECT t.*, 
                   u1.full_name as created_by_name,
                   u2.full_name as assigned_to_name,
                   CONCAT(i.first_name, ' ', i.last_name) as influencer_name,
                   c.title as campaign_title
            FROM todos t 
            LEFT JOIN users u1 ON t.created_by = u1.id
            LEFT JOIN users u2 ON t.assigned_to = u2.id
            LEFT JOIN influencers i ON t.influencer_id = i.id
            LEFT JOIN campaigns c ON t.campaign_id = c.id
            $where_clause
            ORDER BY 
                CASE t.status 
                    WHEN 'completed' THEN 3
                    WHEN 'cancelled' THEN 4
                    ELSE 1
                END,
                CASE t.priority 
                    WHEN 'urgent' THEN 1
                    WHEN 'high' THEN 2
                    WHEN 'medium' THEN 3
                    WHEN 'low' THEN 4
                END,
                t.due_date ASC,
                t.created_at DESC
        ");
        $stmt->execute($params);
        $todos = $stmt->fetchAll();
        
        // Get users for filter
        $stmt = $pdo->query("SELECT id, full_name FROM users WHERE status = 'active' ORDER BY full_name");
        $users = $stmt->fetchAll();
        
    } catch (Exception $e) {
        $todos = [];
        $users = [];
        error_log('Todo list error: ' . $e->getMessage());
    }
    
} elseif ($action === 'add' || $action === 'edit') {
    try {
        global $database;
        $pdo = $database->connect();
        
        // Get users
        $stmt = $pdo->query("SELECT id, full_name FROM users WHERE status = 'active' ORDER BY full_name");
        $users = $stmt->fetchAll();
        
        // Get influencers
        $stmt = $pdo->query("SELECT id, first_name, last_name FROM influencers WHERE status = 'active' ORDER BY first_name, last_name");
        $influencers = $stmt->fetchAll();
        
        // Get campaigns
        $stmt = $pdo->query("SELECT id, title FROM campaigns WHERE status IN ('planned', 'in_progress') ORDER BY title");
        $campaigns = $stmt->fetchAll();
        
        // Get todo for editing
        if ($action === 'edit' && $todo_id) {
            $stmt = $pdo->prepare("SELECT * FROM todos WHERE id = ?");
            $stmt->execute([$todo_id]);
            $todo = $stmt->fetch();
            
            if (!$todo) {
                $_SESSION['error_message'] = 'Görev bulunamadı.';
                redirectTo('todos.php');
            }
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Veri alınırken hata oluştu.';
        error_log('Todo data error: ' . $e->getMessage());
        redirectTo('todos.php');
    }
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <!-- Todo List -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>
                        Görev Listesi (<?php echo count($todos); ?>)
                    </h5>
                    <?php if (hasPermission('todos_create')): ?>
                        <a href="todos.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Yeni Görev
                        </a>
                    <?php endif; ?>
                </div>
                
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" class="row g-3 mb-4">
                        <div class="col-md-3">
                            <input type="text" class="form-control" name="search" 
                                   value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                                   placeholder="Görev ara...">
                        </div>
                        <div class="col-md-2">
                            <select name="status" class="form-select">
                                <option value="">Tüm Durumlar</option>
                                <?php foreach ($statuses as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" <?php echo ($_GET['status'] ?? '') === $key ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select name="priority" class="form-select">
                                <option value="">Tüm Öncelikler</option>
                                <?php foreach ($priorities as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" <?php echo ($_GET['priority'] ?? '') === $key ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="assigned" class="form-select">
                                <option value="">Tüm Atananlar</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" 
                                            <?php echo ($_GET['assigned'] ?? '') == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search"></i> Filtrele
                            </button>
                        </div>
                    </form>
                    
                    <!-- Todo Cards -->
                    <?php if (!empty($todos)): ?>
                        <div class="row">
                            <?php foreach ($todos as $todo): ?>
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card border-0 shadow-sm h-100 todo-card" data-status="<?php echo $todo['status']; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <span class="badge bg-<?php echo PRIORITY_COLORS[$todo['priority']] ?? 'secondary'; ?>">
                                                    <?php echo $priorities[$todo['priority']] ?? $todo['priority']; ?>
                                                </span>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <?php if ($todo['status'] !== 'completed'): ?>
                                                            <li><a class="dropdown-item" 
                                                                   href="todos.php?action=update_status&id=<?php echo $todo['id']; ?>&status=completed">
                                                                <i class="fas fa-check me-2"></i>Tamamla
                                                            </a></li>
                                                        <?php else: ?>
                                                            <li><a class="dropdown-item" 
                                                                   href="todos.php?action=update_status&id=<?php echo $todo['id']; ?>&status=pending">
                                                                <i class="fas fa-undo me-2"></i>Yeniden Aç
                                                            </a></li>
                                                        <?php endif; ?>
                                                        <?php if (hasPermission('todos_manage')): ?>
                                                            <li><a class="dropdown-item" href="todos.php?action=edit&id=<?php echo $todo['id']; ?>">
                                                                <i class="fas fa-edit me-2"></i>Düzenle
                                                            </a></li>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><a class="dropdown-item text-danger" 
                                                                   href="todos.php?action=delete&id=<?php echo $todo['id']; ?>"
                                                                   onclick="return confirmDelete('Bu görevi silmek istediğinizden emin misiniz?')">
                                                                <i class="fas fa-trash me-2"></i>Sil
                                                            </a></li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </div>
                                            </div>
                                            
                                            <h6 class="card-title"><?php echo htmlspecialchars($todo['title']); ?></h6>
                                            
                                            <?php if ($todo['description']): ?>
                                                <p class="card-text text-muted small">
                                                    <?php echo nl2br(htmlspecialchars(substr($todo['description'], 0, 100))); ?>
                                                    <?php if (strlen($todo['description']) > 100): ?>...<?php endif; ?>
                                                </p>
                                            <?php endif; ?>
                                            
                                            <div class="mb-2">
                                                <span class="badge bg-<?php echo STATUS_COLORS[$todo['status']] ?? 'secondary'; ?>">
                                                    <?php echo $statuses[$todo['status']] ?? $todo['status']; ?>
                                                </span>
                                                <span class="badge bg-light text-dark">
                                                    <?php echo $categories[$todo['category']] ?? $todo['category']; ?>
                                                </span>
                                            </div>
                                            
                                            <?php if ($todo['due_date']): ?>
                                                <div class="mb-2">
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        <?php 
                                                        $due_date = new DateTime($todo['due_date']);
                                                        $now = new DateTime();
                                                        $diff = $now->diff($due_date);
                                                        
                                                        if ($due_date < $now && $todo['status'] !== 'completed') {
                                                            echo '<span class="text-danger">Gecikmiş (' . $diff->days . ' gün)</span>';
                                                        } else {
                                                            echo date('d.m.Y', strtotime($todo['due_date']));
                                                        }
                                                        ?>
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($todo['assigned_to_name']): ?>
                                                <div class="mb-2">
                                                    <small class="text-muted">
                                                        <i class="fas fa-user me-1"></i>
                                                        <?php echo htmlspecialchars($todo['assigned_to_name']); ?>
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($todo['influencer_name']): ?>
                                                <div class="mb-2">
                                                    <small class="text-muted">
                                                        <i class="fas fa-star me-1"></i>
                                                        <?php echo htmlspecialchars($todo['influencer_name']); ?>
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($todo['campaign_title']): ?>
                                                <div class="mb-2">
                                                    <small class="text-muted">
                                                        <i class="fas fa-bullhorn me-1"></i>
                                                        <?php echo htmlspecialchars($todo['campaign_title']); ?>
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div class="mt-auto">
                                                <small class="text-muted">
                                                    <?php echo date('d.m.Y H:i', strtotime($todo['created_at'])); ?>
                                                    <?php if ($todo['created_by_name']): ?>
                                                        - <?php echo htmlspecialchars($todo['created_by_name']); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Henüz görev eklenmemiş</h5>
                            <p class="text-muted">İlk görevinizi oluşturmak için yukarıdaki butonu kullanın.</p>
                            <?php if (hasPermission('todos_create')): ?>
                                <a href="todos.php?action=add" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>İlk Görevi Oluştur
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Todo Form will be added in next part -->
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        Görev formu bir sonraki adımda eklenecek.
    </div>
<?php endif; ?>

<?php
// Page specific scripts
$page_scripts = '
<style>
.todo-card[data-status="completed"] {
    opacity: 0.7;
}

.todo-card[data-status="completed"] .card-title {
    text-decoration: line-through;
}

.todo-card {
    transition: transform 0.2s ease;
}

.todo-card:hover {
    transform: translateY(-2px);
}
</style>
';

// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
