<?php
/**
 * Database Configuration
 * Influencer CMS - Database Settings
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'influencer_cms';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    public $pdo;

    /**
     * Database Connection
     */
    public function connect() {
        $this->pdo = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $e) {
            echo "Connection Error: " . $e->getMessage();
            die();
        }
        
        return $this->pdo;
    }

    /**
     * Get Database Configuration
     */
    public function getConfig() {
        return [
            'host' => $this->host,
            'db_name' => $this->db_name,
            'username' => $this->username,
            'password' => $this->password,
            'charset' => $this->charset
        ];
    }

    /**
     * Test Database Connection
     */
    public function testConnection() {
        try {
            $this->connect();
            return true;
        } catch(Exception $e) {
            return false;
        }
    }

    /**
     * Create Database if not exists
     */
    public function createDatabase() {
        try {
            $dsn = "mysql:host=" . $this->host . ";charset=" . $this->charset;
            $pdo = new PDO($dsn, $this->username, $this->password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $sql = "CREATE DATABASE IF NOT EXISTS `" . $this->db_name . "` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_turkish_ci";
            $pdo->exec($sql);
            
            return true;
        } catch(PDOException $e) {
            return false;
        }
    }

    /**
     * Execute SQL File
     */
    public function executeSQLFile($file_path) {
        try {
            if (!file_exists($file_path)) {
                throw new Exception("SQL file not found: " . $file_path);
            }

            $sql = file_get_contents($file_path);
            $this->connect();
            
            // Split SQL commands
            $commands = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($commands as $command) {
                if (!empty($command) && !preg_match('/^--/', $command)) {
                    $this->pdo->exec($command);
                }
            }
            
            return true;
        } catch(Exception $e) {
            error_log("SQL Execution Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if tables exist
     */
    public function tablesExist() {
        try {
            $this->connect();
            $stmt = $this->pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $required_tables = [
                'users', 'influencers', 'influencer_platforms', 
                'influencer_pricing', 'brands', 'campaigns', 
                'files', 'notes', 'tags', 'influencer_tags', 
                'todos', 'settings'
            ];
            
            foreach ($required_tables as $table) {
                if (!in_array($table, $tables)) {
                    return false;
                }
            }
            
            return true;
        } catch(Exception $e) {
            return false;
        }
    }
}

// Global database instance
$database = new Database();
?>
