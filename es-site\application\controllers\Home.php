<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Ana Sayfa Controller
 * 
 * Escort ilan sitesinin ana sayfası ve genel işlemleri
 * 
 * @package EscortSite
 * <AUTHOR> Site Developer
 * @version 1.0
 */
class Home extends CI_Controller {

    public function __construct() {
        parent::__construct();
        
        // Model'leri yükle
        $this->load->model('Ad_model');
        $this->load->model('City_model');
        $this->load->model('Category_model');
        $this->load->model('Profile_model');
        
        // Helper'ları yükle
        $this->load->helper(['url', 'form', 'html', 'text']);
        
        // Library'leri yükle
        $this->load->library(['pagination', 'user_agent']);
    }

    /**
     * Ana sayfa
     */
    public function index() {
        // Sayfa başlığı
        $data['page_title'] = 'Escort İlan Sitesi - Ana Sayfa';

        // Varsayılan değerler
        $data['featured_ads'] = [];
        $data['latest_ads'] = [];
        $data['cities'] = [];
        $data['categories'] = [];
        $data['stats'] = [
            'total_ads' => 0,
            'total_escorts' => 0,
            'total_cities' => 0
        ];

        try {
            // Öne çıkan ilanlar
            $data['featured_ads'] = $this->Ad_model->get_featured_ads(8);

            // Son eklenen ilanlar
            $data['latest_ads'] = $this->Ad_model->get_latest_ads(12);

            // Şehirler
            $data['cities'] = $this->City_model->get_active_cities();

            // Kategoriler
            $data['categories'] = $this->Category_model->get_active_categories();

            // İstatistikler
            $data['stats'] = [
                'total_ads' => $this->Ad_model->count_active_ads(),
                'total_escorts' => $this->Profile_model->count_verified_profiles(),
                'total_cities' => $this->City_model->count_active_cities()
            ];
        } catch (Exception $e) {
            // Hata durumunda log'a yaz
            log_message('error', 'Ana sayfa yüklenirken hata: ' . $e->getMessage());
        }

        // View'ı yükle
        $this->load->view('templates/header', $data);
        $this->load->view('home/index', $data);
        $this->load->view('templates/footer', $data);
    }

    /**
     * İlan arama
     */
    public function search() {
        // Arama parametreleri
        $search_params = [
            'keyword' => $this->input->get('q'),
            'city_id' => $this->input->get('city'),
            'category_id' => $this->input->get('category'),
            'min_age' => $this->input->get('min_age'),
            'max_age' => $this->input->get('max_age'),
            'min_price' => $this->input->get('min_price'),
            'max_price' => $this->input->get('max_price')
        ];
        
        // Sayfalama konfigürasyonu
        $config['base_url'] = base_url('home/search');
        $config['total_rows'] = $this->Ad_model->count_search_results($search_params);
        $config['per_page'] = 20;
        $config['page_query_string'] = TRUE;
        $config['query_string_segment'] = 'page';
        
        // Sayfalama stil ayarları
        $config['full_tag_open'] = '<nav><ul class="pagination justify-content-center">';
        $config['full_tag_close'] = '</ul></nav>';
        $config['first_link'] = 'İlk';
        $config['last_link'] = 'Son';
        $config['next_link'] = 'Sonraki';
        $config['prev_link'] = 'Önceki';
        $config['cur_tag_open'] = '<li class="page-item active"><span class="page-link">';
        $config['cur_tag_close'] = '</span></li>';
        $config['num_tag_open'] = '<li class="page-item"><a class="page-link" href="';
        $config['num_tag_close'] = '</a></li>';
        
        $this->pagination->initialize($config);
        
        // Arama sonuçları
        $page = $this->input->get('page') ? $this->input->get('page') : 0;
        $data['ads'] = $this->Ad_model->search_ads($search_params, $config['per_page'], $page);
        $data['pagination'] = $this->pagination->create_links();
        $data['total_results'] = $config['total_rows'];
        $data['search_params'] = $search_params;
        
        // Filtre verileri
        $data['cities'] = $this->City_model->get_active_cities();
        $data['categories'] = $this->Category_model->get_active_categories();
        
        // Sayfa başlığı
        $data['page_title'] = 'Arama Sonuçları - Escort İlan Sitesi';
        
        // View'ı yükle
        $this->load->view('templates/header', $data);
        $this->load->view('home/search', $data);
        $this->load->view('templates/footer');
    }

    /**
     * İlan detay sayfası
     */
    public function ad_detail($ad_id) {
        // İlan bilgilerini al
        $ad = $this->Ad_model->get_ad_with_profile($ad_id);
        
        if (!$ad || $ad->status !== 'active') {
            show_404();
        }
        
        // Görüntülenme sayısını artır
        $this->Ad_model->increment_view_count($ad_id);
        
        // İlan sahibinin diğer ilanları
        $data['other_ads'] = $this->Ad_model->get_user_other_ads($ad->user_id, $ad_id, 6);
        
        // Benzer ilanlar
        $data['similar_ads'] = $this->Ad_model->get_similar_ads($ad->city_id, $ad->category_id, $ad_id, 6);
        
        $data['ad'] = $ad;
        $data['page_title'] = $ad->title . ' - Escort İlan Sitesi';
        
        // View'ı yükle
        $this->load->view('templates/header', $data);
        $this->load->view('home/ad_detail', $data);
        $this->load->view('templates/footer');
    }

    /**
     * Şehir bazlı ilanlar
     */
    public function city($city_slug) {
        // Şehir bilgilerini al
        $city = $this->City_model->get_city_by_slug($city_slug);
        
        if (!$city) {
            show_404();
        }
        
        // Sayfalama konfigürasyonu
        $config['base_url'] = base_url('home/city/' . $city_slug);
        $config['total_rows'] = $this->Ad_model->count_ads_by_city($city->id);
        $config['per_page'] = 20;
        $config['use_page_numbers'] = TRUE;
        
        $this->pagination->initialize($config);
        
        // İlanları al
        $page = $this->uri->segment(4) ? $this->uri->segment(4) : 1;
        $offset = ($page - 1) * $config['per_page'];
        $data['ads'] = $this->Ad_model->get_ads_by_city($city->id, $config['per_page'], $offset);
        $data['pagination'] = $this->pagination->create_links();
        
        $data['city'] = $city;
        $data['page_title'] = $city->name . ' Escort İlanları - Escort İlan Sitesi';
        
        // View'ı yükle
        $this->load->view('templates/header', $data);
        $this->load->view('home/city_ads', $data);
        $this->load->view('templates/footer');
    }

    /**
     * Kategori bazlı ilanlar
     */
    public function category($category_slug) {
        // Kategori bilgilerini al
        $category = $this->Category_model->get_category_by_slug($category_slug);
        
        if (!$category) {
            show_404();
        }
        
        // Sayfalama konfigürasyonu
        $config['base_url'] = base_url('home/category/' . $category_slug);
        $config['total_rows'] = $this->Ad_model->count_ads_by_category($category->id);
        $config['per_page'] = 20;
        $config['use_page_numbers'] = TRUE;
        
        $this->pagination->initialize($config);
        
        // İlanları al
        $page = $this->uri->segment(4) ? $this->uri->segment(4) : 1;
        $offset = ($page - 1) * $config['per_page'];
        $data['ads'] = $this->Ad_model->get_ads_by_category($category->id, $config['per_page'], $offset);
        $data['pagination'] = $this->pagination->create_links();
        
        $data['category'] = $category;
        $data['page_title'] = $category->name . ' İlanları - Escort İlan Sitesi';
        
        // View'ı yükle
        $this->load->view('templates/header', $data);
        $this->load->view('home/category_ads', $data);
        $this->load->view('templates/footer');
    }

    /**
     * Hakkımızda sayfası
     */
    public function about() {
        $data['page_title'] = 'Hakkımızda - Escort İlan Sitesi';
        
        $this->load->view('templates/header', $data);
        $this->load->view('pages/about');
        $this->load->view('templates/footer');
    }

    /**
     * İletişim sayfası
     */
    public function contact() {
        $data['page_title'] = 'İletişim - Escort İlan Sitesi';
        
        if ($this->input->post()) {
            // İletişim formu işleme
            $this->form_validation->set_rules('name', 'Ad Soyad', 'required|min_length[3]|max_length[100]');
            $this->form_validation->set_rules('email', 'E-posta', 'required|valid_email');
            $this->form_validation->set_rules('subject', 'Konu', 'required|min_length[5]|max_length[200]');
            $this->form_validation->set_rules('message', 'Mesaj', 'required|min_length[10]|max_length[1000]');
            
            if ($this->form_validation->run()) {
                // E-posta gönderme işlemi burada yapılacak
                $data['success_message'] = 'Mesajınız başarıyla gönderildi. En kısa sürede size dönüş yapacağız.';
            }
        }
        
        $this->load->view('templates/header', $data);
        $this->load->view('pages/contact', $data);
        $this->load->view('templates/footer');
    }

    /**
     * Gizlilik politikası
     */
    public function privacy() {
        $data['page_title'] = 'Gizlilik Politikası - Escort İlan Sitesi';
        
        $this->load->view('templates/header', $data);
        $this->load->view('pages/privacy');
        $this->load->view('templates/footer');
    }

    /**
     * Kullanım şartları
     */
    public function terms() {
        $data['page_title'] = 'Kullanım Şartları - Escort İlan Sitesi';
        
        $this->load->view('templates/header', $data);
        $this->load->view('pages/terms');
        $this->load->view('templates/footer');
    }
}
