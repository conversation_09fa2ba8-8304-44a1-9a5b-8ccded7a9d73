<!-- Contact Header -->
<section class="bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4"><PERSON><PERSON><PERSON>ş<PERSON></h1>
                <p class="lead">Bizimle iletişime geçin, size yardımcı olmaktan mutluluk duyarız</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8 mb-5">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-envelope me-2"></i>
                            Bize Mesaj Gönderin
                        </h3>
                    </div>
                    <div class="card-body p-4">
                        <?php if (isset($success_message)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success_message; ?>
                            </div>
                        <?php endif; ?>

                        <?php echo validation_errors('<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>', '</div>'); ?>

                        <form action="<?php echo base_url('home/contact'); ?>" method="POST" id="contactForm" class="needs-validation" novalidate>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Ad Soyad <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo set_value('name'); ?>" required>
                                    <div class="invalid-feedback">
                                        Lütfen adınızı ve soyadınızı girin.
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">E-posta <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo set_value('email'); ?>" required>
                                    <div class="invalid-feedback">
                                        Lütfen geçerli bir e-posta adresi girin.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="subject" class="form-label">Konu <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="subject" name="subject" 
                                       value="<?php echo set_value('subject'); ?>" required>
                                <div class="invalid-feedback">
                                    Lütfen mesajınızın konusunu belirtin.
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Mesaj <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="message" name="message" rows="6" required><?php echo set_value('message'); ?></textarea>
                                <div class="invalid-feedback">
                                    Lütfen mesajınızı yazın.
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="privacy_agree" required>
                                    <label class="form-check-label" for="privacy_agree">
                                        <a href="<?php echo base_url('home/privacy'); ?>" target="_blank">Gizlilik Politikası</a>'nı okudum ve kabul ediyorum. <span class="text-danger">*</span>
                                    </label>
                                    <div class="invalid-feedback">
                                        Gizlilik politikasını kabul etmelisiniz.
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>
                                Mesaj Gönder
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Contact Info -->
            <div class="col-lg-4">
                <div class="card shadow-lg border-0 mb-4">
                    <div class="card-header bg-success text-white">
                        <h4 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            İletişim Bilgileri
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-envelope fa-2x text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">E-posta</h6>
                                    <p class="text-muted mb-0"><EMAIL></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-clock fa-2x text-success"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Çalışma Saatleri</h6>
                                    <p class="text-muted mb-0">7/24 Online Destek</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-reply fa-2x text-info"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Yanıt Süresi</h6>
                                    <p class="text-muted mb-0">24 saat içinde</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-shield-alt fa-2x text-warning"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Gizlilik</h6>
                                    <p class="text-muted mb-0">%100 Gizli ve Güvenli</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- FAQ Card -->
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-warning text-dark">
                        <h4 class="card-title mb-0">
                            <i class="fas fa-question-circle me-2"></i>
                            Sık Sorulan Sorular
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="faqAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq1">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                        Üyelik ücretsiz mi?
                                    </button>
                                </h2>
                                <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        Evet, temel üyelik tamamen ücretsizdir. Premium özellikler için ücretli paketlerimiz mevcuttur.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq2">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                        Bilgilerim güvende mi?
                                    </button>
                                </h2>
                                <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        Evet, tüm kişisel bilgileriniz SSL şifreleme ile korunur ve kesinlikle üçüncü taraflarla paylaşılmaz.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq3">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                        Nasıl ilan verebilirim?
                                    </button>
                                </h2>
                                <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        Üye olduktan sonra dashboard'unuzdan "İlan Ver" seçeneğini kullanarak kolayca ilan verebilirsiniz.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.contact-item {
    padding: 1rem;
    border-radius: 10px;
    transition: background-color 0.3s ease;
}

.contact-item:hover {
    background-color: #f8f9fa;
}

.contact-icon {
    width: 60px;
    text-align: center;
}

.card {
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.accordion-button:not(.collapsed) {
    background-color: #e7f3ff;
    color: #0d6efd;
}

@media (max-width: 768px) {
    .contact-icon {
        width: 50px;
    }
    
    .contact-icon i {
        font-size: 1.5rem !important;
    }
}
</style>

<script>
$(document).ready(function() {
    // Form validation
    $('#contactForm').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });
    
    // Character counter for message
    $('#message').on('input', function() {
        const maxLength = 1000;
        const currentLength = $(this).val().length;
        const remaining = maxLength - currentLength;
        
        if (!$('.char-counter').length) {
            $(this).after('<small class="char-counter text-muted"></small>');
        }
        
        $('.char-counter').text(`${currentLength}/${maxLength} karakter`);
        
        if (remaining < 50) {
            $('.char-counter').removeClass('text-muted').addClass('text-warning');
        } else {
            $('.char-counter').removeClass('text-warning').addClass('text-muted');
        }
    });
});
</script>
