-- =====================================================
-- ESCORT İLAN SİTESİ VERİTABANI ŞEMASI
-- CodeIgniter 3.x + PayTR + Canlı Mesajlaşma
-- PHP 7.1 Uyumlu - MySQL 5.x
-- =====================================================

-- Veritabanı oluştur
CREATE DATABASE IF NOT EXISTS `escort_site` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_turkish_ci;
USE `escort_site`;

-- =====================================================
-- 1. KULLANICILAR TABLOSU (users)
-- =====================================================
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `email` varchar(100) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `role` enum('escort','customer','admin') NOT NULL DEFAULT 'customer',
  `status` enum('active','inactive','banned','pending') NOT NULL DEFAULT 'pending',
  `email_verified` tinyint(1) NOT NULL DEFAULT 0,
  `email_verification_token` varchar(100) DEFAULT NULL,
  `reset_token` varchar(100) DEFAULT NULL,
  `reset_token_expires` datetime DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `last_ip` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- =====================================================
-- 2. ESCORT PROFİLLERİ TABLOSU (profiles)
-- =====================================================
CREATE TABLE `profiles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `age` int(3) NOT NULL,
  `city_id` int(11) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `whatsapp` varchar(20) DEFAULT NULL,
  `description` text,
  `services` text, -- JSON formatında hizmetler
  `price_range` varchar(50) DEFAULT NULL,
  `availability` text, -- JSON formatında müsaitlik saatleri
  `profile_photo` varchar(255) DEFAULT NULL,
  `gallery_photos` text, -- JSON formatında fotoğraf listesi
  `is_verified` tinyint(1) NOT NULL DEFAULT 0,
  `verification_documents` text, -- JSON formatında belgeler
  `view_count` int(11) NOT NULL DEFAULT 0,
  `rating` decimal(3,2) DEFAULT NULL,
  `review_count` int(11) NOT NULL DEFAULT 0,
  `is_premium` tinyint(1) NOT NULL DEFAULT 0,
  `premium_expires` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_city_id` (`city_id`),
  KEY `idx_age` (`age`),
  KEY `idx_is_verified` (`is_verified`),
  KEY `idx_is_premium` (`is_premium`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- =====================================================
-- 3. İLANLAR TABLOSU (ads)
-- =====================================================
CREATE TABLE `ads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `description` text NOT NULL,
  `city_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `photos` text, -- JSON formatında fotoğraf listesi
  `status` enum('pending','active','inactive','rejected','expired') NOT NULL DEFAULT 'pending',
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `is_urgent` tinyint(1) NOT NULL DEFAULT 0,
  `featured_expires` datetime DEFAULT NULL,
  `urgent_expires` datetime DEFAULT NULL,
  `view_count` int(11) NOT NULL DEFAULT 0,
  `contact_count` int(11) NOT NULL DEFAULT 0,
  `expires_at` datetime DEFAULT NULL,
  `admin_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_city_id` (`city_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_featured` (`is_featured`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- =====================================================
-- 4. MESAJLAŞMA TABLOSU (messages)
-- =====================================================
CREATE TABLE `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender_id` int(11) NOT NULL,
  `receiver_id` int(11) NOT NULL,
  `conversation_id` varchar(100) NOT NULL, -- sender_id-receiver_id formatında
  `message` text NOT NULL,
  `message_type` enum('text','image','file') NOT NULL DEFAULT 'text',
  `file_path` varchar(255) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `read_at` datetime DEFAULT NULL,
  `is_deleted_sender` tinyint(1) NOT NULL DEFAULT 0,
  `is_deleted_receiver` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_sender_id` (`sender_id`),
  KEY `idx_receiver_id` (`receiver_id`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`receiver_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- =====================================================
-- 5. ÖDEME GEÇMİŞİ TABLOSU (payments)
-- =====================================================
CREATE TABLE `payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `payment_type` enum('message_credit','ad_featured','ad_urgent','premium_membership') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'TRY',
  `paytr_token` varchar(255) DEFAULT NULL,
  `paytr_order_id` varchar(100) DEFAULT NULL,
  `paytr_payment_id` varchar(100) DEFAULT NULL,
  `status` enum('pending','completed','failed','cancelled','refunded') NOT NULL DEFAULT 'pending',
  `payment_method` varchar(50) DEFAULT NULL,
  `payment_date` datetime DEFAULT NULL,
  `callback_data` text, -- PayTR callback verisi
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_paytr_order_id` (`paytr_order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_type` (`payment_type`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- =====================================================
-- 6. ABONELİK VE MESAJ HAKLARI TABLOSU (subscriptions)
-- =====================================================
CREATE TABLE `subscriptions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `subscription_type` enum('message_credits','premium_membership','ad_package') NOT NULL,
  `credits_remaining` int(11) NOT NULL DEFAULT 0,
  `total_credits` int(11) NOT NULL DEFAULT 0,
  `expires_at` datetime DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `auto_renew` tinyint(1) NOT NULL DEFAULT 0,
  `payment_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_subscription_type` (`subscription_type`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_is_active` (`is_active`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`payment_id`) REFERENCES `payments` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- =====================================================
-- 7. KATEGORİLER TABLOSU (categories)
-- =====================================================
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL UNIQUE,
  `description` text DEFAULT NULL,
  `icon` varchar(100) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_slug` (`slug`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- =====================================================
-- 8. ŞEHİRLER TABLOSU (cities)
-- =====================================================
CREATE TABLE `cities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL UNIQUE,
  `plate_code` varchar(3) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_slug` (`slug`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- =====================================================
-- 9. HİZMETLER TABLOSU (services)
-- =====================================================
CREATE TABLE `services` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL UNIQUE,
  `description` text DEFAULT NULL,
  `icon` varchar(100) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_slug` (`slug`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`),
  FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- =====================================================
-- 10. ADMİN KULLANICILARI TABLOSU (admin_users)
-- =====================================================
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `email` varchar(100) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('super_admin','admin','moderator') NOT NULL DEFAULT 'moderator',
  `permissions` text, -- JSON formatında izinler
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `last_login` datetime DEFAULT NULL,
  `last_ip` varchar(45) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- =====================================================
-- DEMO VERİLERİ VE TEMEL YAPILANDIRMA
-- =====================================================

-- Şehirler ekle (Türkiye'nin büyük şehirleri)
INSERT INTO `cities` (`name`, `slug`, `plate_code`, `is_active`, `sort_order`) VALUES
('İstanbul', 'istanbul', '34', 1, 1),
('Ankara', 'ankara', '06', 1, 2),
('İzmir', 'izmir', '35', 1, 3),
('Bursa', 'bursa', '16', 1, 4),
('Antalya', 'antalya', '07', 1, 5),
('Adana', 'adana', '01', 1, 6),
('Konya', 'konya', '42', 1, 7),
('Gaziantep', 'gaziantep', '27', 1, 8),
('Mersin', 'mersin', '33', 1, 9),
('Kayseri', 'kayseri', '38', 1, 10);

-- Kategoriler ekle
INSERT INTO `categories` (`name`, `slug`, `description`, `icon`, `sort_order`, `is_active`) VALUES
('Escort', 'escort', 'Genel escort hizmetleri', 'fa-user', 1, 1),
('Masaj', 'masaj', 'Masaj ve rahatlama hizmetleri', 'fa-spa', 2, 1),
('Eşlik', 'eslik', 'Sosyal etkinlik eşlik hizmetleri', 'fa-users', 3, 1),
('VIP', 'vip', 'VIP ve özel hizmetler', 'fa-crown', 4, 1);

-- Hizmetler ekle
INSERT INTO `services` (`name`, `slug`, `description`, `category_id`, `is_active`, `sort_order`) VALUES
('Genel Escort', 'genel-escort', 'Genel escort hizmeti', 1, 1, 1),
('Masaj Hizmeti', 'masaj-hizmeti', 'Profesyonel masaj hizmeti', 2, 1, 2),
('Sosyal Eşlik', 'sosyal-eslik', 'Etkinlik ve sosyal eşlik', 3, 1, 3),
('VIP Hizmet', 'vip-hizmet', 'Özel VIP hizmetler', 4, 1, 4);

-- Varsayılan admin kullanıcısı ekle (şifre: admin123)
INSERT INTO `admin_users` (`username`, `email`, `password`, `full_name`, `role`, `is_active`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Site Yöneticisi', 'super_admin', 1);

-- Test escort kullanıcısı ekle (şifre: test123)
INSERT INTO `users` (`username`, `email`, `password`, `role`, `status`, `email_verified`) VALUES
('test_escort', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'escort', 'active', 1);

-- Test müşteri kullanıcısı ekle (şifre: test123)
INSERT INTO `users` (`username`, `email`, `password`, `role`, `status`, `email_verified`) VALUES
('test_customer', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'customer', 'active', 1);

-- Test escort profili ekle
INSERT INTO `profiles` (`user_id`, `display_name`, `age`, `city_id`, `description`, `services`, `price_range`, `is_verified`) VALUES
(1, 'Ayşe', 25, 1, 'Profesyonel escort hizmeti sunuyorum. Temiz ve güvenilir.', '["genel-escort","masaj-hizmeti"]', '500-1000 TL', 1);

-- Test ilan ekle
INSERT INTO `ads` (`user_id`, `title`, `description`, `city_id`, `category_id`, `price`, `status`) VALUES
(1, 'Profesyonel Escort Hizmeti - İstanbul', 'Kaliteli ve güvenilir escort hizmeti. Temizlik ve gizlilik önceliğimizdir.', 1, 1, 750.00, 'active');

-- =====================================================
-- İNDEKSLER VE PERFORMANS OPTİMİZASYONU
-- =====================================================

-- Mesajlaşma için composite index
CREATE INDEX `idx_conversation_created` ON `messages` (`conversation_id`, `created_at`);

-- İlan arama için composite index
CREATE INDEX `idx_ads_search` ON `ads` (`status`, `city_id`, `category_id`, `created_at`);

-- Kullanıcı profil arama için composite index
CREATE INDEX `idx_profiles_search` ON `profiles` (`city_id`, `age`, `is_verified`, `is_premium`);

-- =====================================================
-- TETİKLEYİCİLER (TRIGGERS)
-- =====================================================

-- Profil görüntülenme sayısını artır
DELIMITER $$
CREATE TRIGGER `update_profile_view_count`
AFTER INSERT ON `ads`
FOR EACH ROW
BEGIN
    UPDATE `profiles` SET `view_count` = `view_count` + 1
    WHERE `user_id` = NEW.user_id;
END$$
DELIMITER ;

-- Mesaj okunduğunda read_at güncelle
DELIMITER $$
CREATE TRIGGER `update_message_read_time`
BEFORE UPDATE ON `messages`
FOR EACH ROW
BEGIN
    IF NEW.is_read = 1 AND OLD.is_read = 0 THEN
        SET NEW.read_at = NOW();
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- VERİTABANI KURULUMU TAMAMLANDI
-- =====================================================
SELECT 'Escort İlan Sitesi veritabanı başarıyla oluşturuldu!' as 'DURUM';
