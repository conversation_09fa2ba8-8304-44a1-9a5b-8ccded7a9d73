<?php
/**
 * File Management
 * Dosya ve medya yönetimi
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Check permissions
requirePermission('files_view');

// Page settings
$page_title = 'Dosya Yönetimi';
$action = $_GET['action'] ?? 'list';
$file_id = $_GET['id'] ?? null;
$influencer_id = $_GET['influencer_id'] ?? null;

// File types
$file_types = [
    'contract' => 'Sözleşme',
    'media_kit' => 'Medya Kiti',
    'presentation' => 'Sunum',
    'image' => 'Görsel',
    'document' => 'Belge',
    'other' => 'Diğer'
];

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'upload' && hasPermission('files_upload')) {
    try {
        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('Dosya yükleme hatası.');
        }
        
        $file = $_FILES['file'];
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        // Check file type
        if (!in_array($file_extension, ALLOWED_FILE_TYPES)) {
            throw new Exception('Desteklenmeyen dosya türü.');
        }
        
        // Check file size
        if ($file['size'] > UPLOAD_MAX_SIZE) {
            throw new Exception('Dosya boyutu çok büyük. Maksimum ' . (UPLOAD_MAX_SIZE / 1024 / 1024) . 'MB olabilir.');
        }
        
        // Create upload directory
        $upload_dir = UPLOAD_PATH . 'files/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        // Generate unique filename
        $filename = uniqid() . '_' . time() . '.' . $file_extension;
        $filepath = $upload_dir . $filename;
        
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            // Save to database
            global $database;
            $pdo = $database->connect();
            
            $stmt = $pdo->prepare("
                INSERT INTO files (influencer_id, file_name, original_name, file_path, 
                                 file_type, file_size, mime_type, description, uploaded_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $_POST['influencer_id'],
                $filename,
                $file['name'],
                'files/' . $filename,
                $_POST['file_type'],
                $file['size'],
                $file['type'],
                sanitizeInput($_POST['description']),
                $_SESSION['user_id']
            ]);
            
            $_SESSION['success_message'] = 'Dosya başarıyla yüklendi.';
        } else {
            throw new Exception('Dosya yüklenemedi.');
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = $e->getMessage();
        error_log('File upload error: ' . $e->getMessage());
    }
    
    redirectTo('files.php' . ($influencer_id ? '?influencer_id=' . $influencer_id : ''));
}

// Handle delete action
if ($action === 'delete' && $file_id && hasPermission('files_manage')) {
    try {
        global $database;
        $pdo = $database->connect();
        
        // Get file info
        $stmt = $pdo->prepare("SELECT * FROM files WHERE id = ?");
        $stmt->execute([$file_id]);
        $file = $stmt->fetch();
        
        if ($file) {
            // Delete file from filesystem
            $file_path = UPLOAD_PATH . $file['file_path'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
            
            // Delete from database
            $stmt = $pdo->prepare("DELETE FROM files WHERE id = ?");
            $stmt->execute([$file_id]);
            
            $_SESSION['success_message'] = 'Dosya silindi.';
        } else {
            $_SESSION['error_message'] = 'Dosya bulunamadı.';
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Silme işlemi sırasında bir hata oluştu.';
        error_log('File delete error: ' . $e->getMessage());
    }
    
    redirectTo('files.php' . ($influencer_id ? '?influencer_id=' . $influencer_id : ''));
}

// Get data
try {
    global $database;
    $pdo = $database->connect();
    
    $search = $_GET['search'] ?? '';
    $type_filter = $_GET['type'] ?? '';
    
    $where_conditions = [];
    $params = [];
    
    if ($influencer_id) {
        $where_conditions[] = "f.influencer_id = ?";
        $params[] = $influencer_id;
        
        // Get influencer info
        $stmt = $pdo->prepare("SELECT * FROM influencers WHERE id = ?");
        $stmt->execute([$influencer_id]);
        $influencer = $stmt->fetch();
    }
    
    if (!empty($search)) {
        $where_conditions[] = "(f.original_name LIKE ? OR f.description LIKE ? OR CONCAT(i.first_name, ' ', i.last_name) LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if (!empty($type_filter)) {
        $where_conditions[] = "f.file_type = ?";
        $params[] = $type_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $stmt = $pdo->prepare("
        SELECT f.*, 
               CONCAT(i.first_name, ' ', i.last_name) as influencer_name,
               u.full_name as uploaded_by_name
        FROM files f 
        LEFT JOIN influencers i ON f.influencer_id = i.id
        LEFT JOIN users u ON f.uploaded_by = u.id
        $where_clause
        ORDER BY f.created_at DESC
    ");
    $stmt->execute($params);
    $files = $stmt->fetchAll();
    
    // Get influencers for filter
    if (!$influencer_id) {
        $stmt = $pdo->query("SELECT id, first_name, last_name FROM influencers WHERE status = 'active' ORDER BY first_name, last_name");
        $influencers = $stmt->fetchAll();
    }
    
} catch (Exception $e) {
    $files = [];
    $influencers = [];
    error_log('Files list error: ' . $e->getMessage());
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($influencer_id && isset($influencer)): ?>
    <!-- Influencer Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <?php if ($influencer['profile_photo']): ?>
                                <img src="<?php echo UPLOAD_URL . 'profiles/' . $influencer['profile_photo']; ?>" 
                                     class="rounded-circle me-3" width="60" height="60" style="object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center me-3" 
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h4 class="mb-1"><?php echo htmlspecialchars($influencer['first_name'] . ' ' . $influencer['last_name']); ?></h4>
                                <p class="mb-0 opacity-75">Dosya Yönetimi</p>
                            </div>
                        </div>
                        <div>
                            <a href="influencer_detail.php?id=<?php echo $influencer['id']; ?>" class="btn btn-light">
                                <i class="fas fa-arrow-left me-2"></i>Geri Dön
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- File List -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-folder me-2"></i>
                    Dosya Listesi (<?php echo count($files); ?>)
                </h5>
                <?php if (hasPermission('files_upload')): ?>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="fas fa-upload me-2"></i>Dosya Yükle
                    </button>
                <?php endif; ?>
            </div>
            
            <div class="card-body">
                <!-- Filters -->
                <form method="GET" class="row g-3 mb-4">
                    <?php if ($influencer_id): ?>
                        <input type="hidden" name="influencer_id" value="<?php echo $influencer_id; ?>">
                    <?php endif; ?>
                    
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" 
                               value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                               placeholder="Dosya adı, açıklama ara...">
                    </div>
                    <div class="col-md-3">
                        <select name="type" class="form-select">
                            <option value="">Tüm Türler</option>
                            <?php foreach ($file_types as $key => $name): ?>
                                <option value="<?php echo $key; ?>" <?php echo ($_GET['type'] ?? '') === $key ? 'selected' : ''; ?>>
                                    <?php echo $name; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <?php if (!$influencer_id && isset($influencers)): ?>
                            <select name="influencer_id" class="form-select">
                                <option value="">Tüm Influencer'lar</option>
                                <?php foreach ($influencers as $inf): ?>
                                    <option value="<?php echo $inf['id']; ?>" 
                                            <?php echo ($_GET['influencer_id'] ?? '') == $inf['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($inf['first_name'] . ' ' . $inf['last_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-search"></i> Filtrele
                        </button>
                    </div>
                </form>
                
                <!-- File Grid -->
                <?php if (!empty($files)): ?>
                    <div class="row">
                        <?php foreach ($files as $file): ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-start mb-3">
                                            <div class="file-icon me-3">
                                                <?php
                                                $extension = strtolower(pathinfo($file['original_name'], PATHINFO_EXTENSION));
                                                $icon_class = 'fas fa-file';
                                                $icon_color = '#6c757d';
                                                
                                                if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                                                    $icon_class = 'fas fa-image';
                                                    $icon_color = '#28a745';
                                                } elseif (in_array($extension, ['pdf'])) {
                                                    $icon_class = 'fas fa-file-pdf';
                                                    $icon_color = '#dc3545';
                                                } elseif (in_array($extension, ['doc', 'docx'])) {
                                                    $icon_class = 'fas fa-file-word';
                                                    $icon_color = '#007bff';
                                                } elseif (in_array($extension, ['xls', 'xlsx'])) {
                                                    $icon_class = 'fas fa-file-excel';
                                                    $icon_color = '#28a745';
                                                } elseif (in_array($extension, ['ppt', 'pptx'])) {
                                                    $icon_class = 'fas fa-file-powerpoint';
                                                    $icon_color = '#fd7e14';
                                                }
                                                ?>
                                                <i class="<?php echo $icon_class; ?> fa-2x" style="color: <?php echo $icon_color; ?>"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($file['original_name']); ?></h6>
                                                <small class="text-muted">
                                                    <span class="badge bg-secondary"><?php echo $file_types[$file['file_type']] ?? $file['file_type']; ?></span>
                                                </small>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="<?php echo UPLOAD_URL . $file['file_path']; ?>" target="_blank">
                                                        <i class="fas fa-eye me-2"></i>Görüntüle
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="<?php echo UPLOAD_URL . $file['file_path']; ?>" download>
                                                        <i class="fas fa-download me-2"></i>İndir
                                                    </a></li>
                                                    <?php if (hasPermission('files_manage')): ?>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" 
                                                               href="files.php?action=delete&id=<?php echo $file['id']; ?><?php echo $influencer_id ? '&influencer_id=' . $influencer_id : ''; ?>"
                                                               onclick="return confirmDelete('Bu dosyayı silmek istediğinizden emin misiniz?')">
                                                            <i class="fas fa-trash me-2"></i>Sil
                                                        </a></li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        </div>
                                        
                                        <?php if ($file['description']): ?>
                                            <p class="text-muted small mb-2"><?php echo htmlspecialchars($file['description']); ?></p>
                                        <?php endif; ?>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <?php echo number_format($file['file_size'] / 1024, 1); ?> KB
                                            </small>
                                            <small class="text-muted">
                                                <?php echo date('d.m.Y', strtotime($file['created_at'])); ?>
                                            </small>
                                        </div>
                                        
                                        <?php if (!$influencer_id): ?>
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-user me-1"></i>
                                                    <?php echo htmlspecialchars($file['influencer_name']); ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="mt-1">
                                            <small class="text-muted">
                                                <i class="fas fa-upload me-1"></i>
                                                <?php echo htmlspecialchars($file['uploaded_by_name']); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Henüz dosya yüklenmemiş</h5>
                        <p class="text-muted">İlk dosyanızı yüklemek için yukarıdaki butonu kullanın.</p>
                        <?php if (hasPermission('files_upload')): ?>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                                <i class="fas fa-upload me-2"></i>İlk Dosyayı Yükle
                            </button>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<?php if (hasPermission('files_upload')): ?>
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Dosya Yükle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data" action="files.php?action=upload">
                <div class="modal-body">
                    <?php if ($influencer_id): ?>
                        <input type="hidden" name="influencer_id" value="<?php echo $influencer_id; ?>">
                    <?php else: ?>
                        <div class="mb-3">
                            <label for="influencer_id" class="form-label">Influencer <span class="text-danger">*</span></label>
                            <select class="form-select" name="influencer_id" required>
                                <option value="">Influencer Seçin</option>
                                <?php foreach ($influencers as $inf): ?>
                                    <option value="<?php echo $inf['id']; ?>">
                                        <?php echo htmlspecialchars($inf['first_name'] . ' ' . $inf['last_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label for="file" class="form-label">Dosya <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" name="file" required>
                        <div class="form-text">
                            Desteklenen formatlar: <?php echo implode(', ', ALLOWED_FILE_TYPES); ?><br>
                            Maksimum boyut: <?php echo UPLOAD_MAX_SIZE / 1024 / 1024; ?>MB
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="file_type" class="form-label">Dosya Türü <span class="text-danger">*</span></label>
                        <select class="form-select" name="file_type" required>
                            <option value="">Tür Seçin</option>
                            <?php foreach ($file_types as $key => $name): ?>
                                <option value="<?php echo $key; ?>"><?php echo $name; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Açıklama</label>
                        <textarea class="form-control" name="description" rows="3" 
                                  placeholder="Dosya hakkında kısa açıklama..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>Yükle
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
