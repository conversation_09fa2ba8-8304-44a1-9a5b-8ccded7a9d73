/************************ Form placeholder effect ***************************/
$('.floating-placeholder[placeholder]').placeholderLabel({
  // placeholder color
  placeholderColor: "rgba(0,0,0,.5)",
  // label color
  labelColor: "rgba(0,0,0,.7)",
  // size of label
  labelSize: "inherit",
  // font style
  fontStyle: "normal",
  // uses border color
  useBorderColor: false,
  // displayed in the input
  inInput: true,
  // time to move
  timeMove: 150
});
$('.floating-placeholder').prev('label').css({
  'pointer-events': 'none',
  'font-weight': 'normal'
});

/************************ Add to any ***************************/
var a2a_config = a2a_config || {};
a2a_config.num_services = 12;
a2a_config.prioritize = ["facebook", "twitter", "google_plus", "email", "reddit", "whatsapp", "google_gmail", "evernote", "flipboard", "google_bookmarks", "outlook_com"];



/*-------------------------------------Specific Function-------------------------------------*/
function reveal1() {
  var reveals = document.querySelectorAll(".reveal1");

  for (var i = 0; i < reveals.length; i++) {
    var windowHeight = window.innerHeight;
    var elementTop = reveals[i].getBoundingClientRect().top;
    var elementVisible = 150;
    if (elementTop < windowHeight - elementVisible) {
      reveals[i].classList.add("active");
    } else {
      reveals[i].classList.remove("active");
    }
  }
}
window.addEventListener("scroll", reveal1);


$(document).ready(function() {
  $(".home-references .list li").click(function() {
    var newSrc = $(this).attr('data-src');
    var imgHolder = $(".image-holder img");

    // Yeni görseli arka planda yükleyin
    var newImage = new Image();
    newImage.src = newSrc;

    // Yeni görsel tamamen yüklendiğinde
    newImage.onload = function() {
      // Mevcut görseli gizleyin
      imgHolder.addClass('fade');

      // Geçiş efekti tamamlandığında yeni görseli güncelleyin
      imgHolder.one('transitionend', function() {
        imgHolder.attr("src", newSrc);
        imgHolder.removeClass('fade');
      });
    };
  });
});

$(document).ready(function() {
  var $tooltip = $('.tooltip');
  $('.icon').mouseenter(function() {
    var title = $(this).data('title');
    var description = $(this).data('description');
    var link = $(this).data('link');
    $tooltip.find('strong').text(title);
    $tooltip.find('p').text(description);
    $tooltip.find('a').attr('href', link);
    $tooltip.css({
      top: $(this).position().top + $(this).outerHeight() + 10,
      left: $(this).position().left + $(this).outerWidth() / 2 - $tooltip.outerWidth() / 2
    }).stop(true, true).fadeIn();
  }).mouseleave(function() {
    $tooltip.stop(true, true).fadeOut();
  });

  $tooltip.mouseenter(function() {
    $(this).stop(true, true).show();
  }).mouseleave(function() {
    $(this).stop(true, true).fadeOut();
  });
});



$(document).ready(function() {
  // Tüm <a> etiketlerine tıklama olayı ekle
  $("a").on('click', function(event) {
      // Sayfa içi link ise varsayılan davranışı önle
      if (this.hash !== "") {
          event.preventDefault();

          // Linkin hash değerini al
          var hash = this.hash;

          // $('html, body') ile animasyonlu kaydırma yap
          $('html, body').animate({
              scrollTop: $(hash).offset().top
          }, 800, function(){

              // URL'i güncelleme
              window.location.hash = hash;
      });
      }
  });
});

document.addEventListener('DOMContentLoaded', function () {
  const sections = document.querySelectorAll('.section');

  const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
          if (entry.isIntersecting) {
              entry.target.classList.add('visible');
          } else {
              entry.target.classList.remove('visible');
          }
      });
  }, {
      threshold: [0] // Div'in herhangi bir kısmı görünür olursa tetiklenir
  });

  sections.forEach(section => {
      observer.observe(section);
  });
});

function showResult(value){
  $.ajax({
      url: '/search',
      type: 'post',
      data: {'value': ''+value+''},
      success: function(data) {
          $('.result').html(data);
      }
  });
}

document.addEventListener('DOMContentLoaded', () => {
  function updateTargetDivHeight() {
      const targetDiv = document.getElementById('targetDiv');
      const sourceDivs = document.querySelectorAll('.sourceDiv');

      if (!targetDiv || !sourceDivs.length) {
          return;
      }

      let maxHeight = 0;
      sourceDivs.forEach(sourceDiv => {
          if (getComputedStyle(sourceDiv).display === 'block') {
              maxHeight = Math.max(maxHeight, sourceDiv.scrollHeight + 45); // 50 px ekle
          }
      });

      targetDiv.style.height = maxHeight ? `${maxHeight}px` : ''; // Boş bırak
  }
  // MutationObserver kullanarak sourceDiv öğelerinin stil değişimlerini izler
  const observer = new MutationObserver(updateTargetDivHeight);
  document.querySelectorAll('.sourceDiv').forEach(sourceDiv => {
      observer.observe(sourceDiv, { attributes: true, attributeFilter: ['style'] });
  });

 // Menü öğelerine tıklama işlevi ekle
  document.querySelectorAll('.nav-menu > li > a').forEach(menuItem => {
      menuItem.addEventListener('click', () => {
          const submenu = menuItem.nextElementSibling;
          if (submenu && submenu.classList.contains('sourceDiv')) {
              submenu.style.display = (getComputedStyle(submenu).display === 'none') ? 'block' : 'none';
              updateTargetDivHeight();
          }
      });
  });

  updateTargetDivHeight();
});

$('.search-icon').on('click touchstart', function(event) {
  event.preventDefault();
  $('.searchBar').fadeToggle();
  $('.aramaYap').val('').focus();
});

$('.close').on('click touchstart', function(event) {
  event.preventDefault();
  $('.searchBar').fadeToggle();
  $('.result').html('');
});

  gsap.registerPlugin(ScrollTrigger);

  const h2 = document.querySelector(".slider_animation_text h2");
  const chars = h2.textContent.split("");
  h2.textContent = "";

  chars.forEach(char => {
    const span = document.createElement("span");
    span.textContent = char;
    if (char === " ") {
      span.classList.add("space");
    }
    h2.appendChild(span);
  });

  let tl = gsap.timeline({
    scrollTrigger: {
      trigger: ".slider_animation_text",
      start: "bottom bottom",
      end: "bottom center",
      scrub: true
    }
  });

  tl.to(".slider_animation_text h2 span", {
    color: "#ffffff",
    stagger: 3.09,
    duration: 0.02,
    ease: "power2.inOut"
  });


if (window.innerWidth > 768) {
 gsap.registerPlugin(ScrollTrigger);

  gsap.to(".custom-tabs", {
    x: 200,
    ease: "none",
    scrollTrigger: {
      trigger: ".custom-tabs",
      start: "top bottom",
      end: "bottom top",
      scrub: true
    }
  });
}

gsap.registerPlugin(ScrollTrigger);

window.addEventListener('load', function() {
    gsap.set(".slider-v2 .slideimg", {
        scale: 1,
        filter: "blur(0px)",
        opacity: 1
    });
    
    let lastScrollTop = 0;
    
    ScrollTrigger.create({
        trigger: ".slider-v2",
        start: "top top",
        end: "bottom top",
        markers: false,
        onUpdate: function(self) {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollingDown = scrollTop > lastScrollTop;
            lastScrollTop = scrollTop;
            
            if (scrollTop > 200) {
                if (scrollingDown) {
                    gsap.to(".slider-v2 .slideimg", {
                        scale: 1.4,
                        filter: "blur(20px)",
                        duration: 0.5,
                    });
                } 
                else {
                    gsap.to(".slider-v2 .slideimg", {
                        scale: 1,
                        filter: "blur(0px)",
                        duration: 0.5
                    });
                }
            }
        }
    });

if (window.innerWidth > 768) {    
    gsap.to(".slider-v2 .hero-content", {
    scrollTrigger: {
        trigger: ".slider-v2",
        start: "top top",
        end: "bottom top",
        scrub: 0.7
    },
    y: -100,
});
    gsap.to(".slick-custom-arrow", {
    scrollTrigger: {
        trigger: ".slider-v2",
        start: "top top",
        end: "bottom top",
        scrub: 0.7
    },
    y: -100,
});
}

});
