<?php
/**
 * Brand Management
 * Marka yönetimi sistemi
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Check permissions
requirePermission('brands_view');

// Page settings
$page_title = 'Marka Yönetimi';
$action = $_GET['action'] ?? 'list';
$brand_id = $_GET['id'] ?? null;

// Brand statuses
$brand_statuses = [
    'active' => 'Aktif',
    'inactive' => 'Pasif',
    'suspended' => 'Askıya Alınmış'
];

// Industries
$industries = [
    'fashion' => 'Moda',
    'beauty' => 'Güzellik',
    'technology' => 'Teknoloji',
    'food' => 'Gıda',
    'travel' => 'Seyahat',
    'fitness' => 'Fitness',
    'lifestyle' => 'Yaşam Tarzı',
    'automotive' => 'Otomotiv',
    'finance' => 'Finans',
    'education' => 'Eğitim',
    'health' => 'Sağlık',
    'entertainment' => 'Eğlence',
    'other' => 'Diğer'
];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        global $database;
        $pdo = $database->connect();
        
        if ($action === 'add' && hasPermission('brands_manage')) {
            // Add new brand
            $stmt = $pdo->prepare("
                INSERT INTO brands (name, industry, website, contact_person, contact_email, 
                                  contact_phone, address, description, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                sanitizeInput($_POST['name']),
                $_POST['industry'],
                sanitizeInput($_POST['website']),
                sanitizeInput($_POST['contact_person']),
                sanitizeInput($_POST['contact_email']),
                sanitizeInput($_POST['contact_phone']),
                sanitizeInput($_POST['address']),
                sanitizeInput($_POST['description']),
                $_POST['status'] ?? 'active'
            ]);
            
            $_SESSION['success_message'] = 'Marka başarıyla eklendi.';
            redirectTo('brands.php');
            
        } elseif ($action === 'edit' && hasPermission('brands_manage')) {
            // Update brand
            $stmt = $pdo->prepare("
                UPDATE brands 
                SET name = ?, industry = ?, website = ?, contact_person = ?, contact_email = ?, 
                    contact_phone = ?, address = ?, description = ?, status = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                sanitizeInput($_POST['name']),
                $_POST['industry'],
                sanitizeInput($_POST['website']),
                sanitizeInput($_POST['contact_person']),
                sanitizeInput($_POST['contact_email']),
                sanitizeInput($_POST['contact_phone']),
                sanitizeInput($_POST['address']),
                sanitizeInput($_POST['description']),
                $_POST['status'],
                $brand_id
            ]);
            
            $_SESSION['success_message'] = 'Marka bilgileri güncellendi.';
            redirectTo('brands.php');
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'İşlem sırasında bir hata oluştu: ' . $e->getMessage();
        error_log('Brand operation error: ' . $e->getMessage());
    }
}

// Handle delete action
if ($action === 'delete' && $brand_id && hasPermission('brands_manage')) {
    try {
        global $database;
        $pdo = $database->connect();
        
        $stmt = $pdo->prepare("DELETE FROM brands WHERE id = ?");
        $stmt->execute([$brand_id]);
        
        $_SESSION['success_message'] = 'Marka silindi.';
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Silme işlemi sırasında bir hata oluştu.';
        error_log('Brand delete error: ' . $e->getMessage());
    }
    
    redirectTo('brands.php');
}

// Get data based on action
if ($action === 'list') {
    try {
        global $database;
        $pdo = $database->connect();
        
        $search = $_GET['search'] ?? '';
        $industry_filter = $_GET['industry'] ?? '';
        $status_filter = $_GET['status'] ?? '';
        
        $where_conditions = [];
        $params = [];
        
        if (!empty($search)) {
            $where_conditions[] = "(name LIKE ? OR contact_person LIKE ? OR contact_email LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($industry_filter)) {
            $where_conditions[] = "industry = ?";
            $params[] = $industry_filter;
        }
        
        if (!empty($status_filter)) {
            $where_conditions[] = "status = ?";
            $params[] = $status_filter;
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        $stmt = $pdo->prepare("
            SELECT * FROM brands 
            $where_clause
            ORDER BY created_at DESC
        ");
        $stmt->execute($params);
        $brands = $stmt->fetchAll();
        
    } catch (Exception $e) {
        $brands = [];
        error_log('Brand list error: ' . $e->getMessage());
    }
    
} elseif ($action === 'edit' && $brand_id) {
    try {
        global $database;
        $pdo = $database->connect();
        
        $stmt = $pdo->prepare("SELECT * FROM brands WHERE id = ?");
        $stmt->execute([$brand_id]);
        $brand = $stmt->fetch();
        
        if (!$brand) {
            $_SESSION['error_message'] = 'Marka bulunamadı.';
            redirectTo('brands.php');
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Marka bilgileri alınamadı.';
        redirectTo('brands.php');
    }
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <!-- Brand List -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        Marka Listesi (<?php echo count($brands); ?>)
                    </h5>
                    <?php if (hasPermission('brands_manage')): ?>
                        <a href="brands.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Yeni Marka
                        </a>
                    <?php endif; ?>
                </div>
                
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" class="row g-3 mb-4">
                        <div class="col-md-4">
                            <input type="text" class="form-control" name="search" 
                                   value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                                   placeholder="Marka adı, kişi, e-posta ara...">
                        </div>
                        <div class="col-md-3">
                            <select name="industry" class="form-select">
                                <option value="">Tüm Sektörler</option>
                                <?php foreach ($industries as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" <?php echo ($_GET['industry'] ?? '') === $key ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="">Tüm Durumlar</option>
                                <?php foreach ($brand_statuses as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" <?php echo ($_GET['status'] ?? '') === $key ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search"></i> Filtrele
                            </button>
                        </div>
                    </form>
                    
                    <!-- Brand Table -->
                    <?php if (!empty($brands)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Marka</th>
                                        <th>Sektör</th>
                                        <th>İletişim Kişisi</th>
                                        <th>E-posta</th>
                                        <th>Durum</th>
                                        <th>Kayıt Tarihi</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($brands as $brand): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($brand['logo']): ?>
                                                        <img src="<?php echo UPLOAD_URL . 'brands/' . $brand['logo']; ?>" 
                                                             class="rounded me-2" width="32" height="32" style="object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-primary rounded d-flex align-items-center justify-content-center me-2" 
                                                             style="width: 32px; height: 32px;">
                                                            <i class="fas fa-building text-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($brand['name']); ?></strong>
                                                        <?php if ($brand['website']): ?>
                                                            <br><small class="text-muted">
                                                                <a href="<?php echo htmlspecialchars($brand['website']); ?>" target="_blank" class="text-decoration-none">
                                                                    <i class="fas fa-external-link-alt me-1"></i>Website
                                                                </a>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo $industries[$brand['industry']] ?? $brand['industry']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($brand['contact_person']); ?></td>
                                            <td>
                                                <?php if ($brand['contact_email']): ?>
                                                    <a href="mailto:<?php echo htmlspecialchars($brand['contact_email']); ?>" class="text-decoration-none">
                                                        <?php echo htmlspecialchars($brand['contact_email']); ?>
                                                    </a>
                                                <?php else: ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo STATUS_COLORS[$brand['status']] ?? 'secondary'; ?>">
                                                    <?php echo $brand_statuses[$brand['status']] ?? $brand['status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small><?php echo date('d.m.Y', strtotime($brand['created_at'])); ?></small>
                                            </td>
                                            <td>
                                                <?php if (hasPermission('brands_manage')): ?>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="brands.php?action=edit&id=<?php echo $brand['id']; ?>" 
                                                           class="btn btn-outline-warning" data-bs-toggle="tooltip" title="Düzenle">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="brands.php?action=delete&id=<?php echo $brand['id']; ?>" 
                                                           class="btn btn-outline-danger" data-bs-toggle="tooltip" title="Sil"
                                                           onclick="return confirmDelete('Bu markayı silmek istediğinizden emin misiniz?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Marka bulunamadı</h5>
                            <p class="text-muted">Arama kriterlerinizi değiştirin veya yeni marka ekleyin.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Brand Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                        <?php echo $action === 'add' ? 'Yeni Marka Ekle' : 'Marka Düzenle'; ?>
                    </h5>
                </div>
                
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="name" class="form-label">Marka Adı <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($brand['name'] ?? ''); ?>" required>
                                <div class="invalid-feedback">Marka adı zorunludur.</div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="industry" class="form-label">Sektör <span class="text-danger">*</span></label>
                                <select class="form-select" id="industry" name="industry" required>
                                    <option value="">Sektör Seçin</option>
                                    <?php foreach ($industries as $key => $name): ?>
                                        <option value="<?php echo $key; ?>" 
                                                <?php echo ($brand['industry'] ?? '') === $key ? 'selected' : ''; ?>>
                                            <?php echo $name; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Sektör seçimi zorunludur.</div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="website" class="form-label">Website</label>
                            <input type="url" class="form-control" id="website" name="website" 
                                   value="<?php echo htmlspecialchars($brand['website'] ?? ''); ?>"
                                   placeholder="https://...">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="contact_person" class="form-label">İletişim Kişisi</label>
                                <input type="text" class="form-control" id="contact_person" name="contact_person" 
                                       value="<?php echo htmlspecialchars($brand['contact_person'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="contact_email" class="form-label">E-posta</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                       value="<?php echo htmlspecialchars($brand['contact_email'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="contact_phone" class="form-label">Telefon</label>
                                <input type="tel" class="form-control" id="contact_phone" name="contact_phone" 
                                       value="<?php echo htmlspecialchars($brand['contact_phone'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Adres</label>
                            <textarea class="form-control" id="address" name="address" rows="2"><?php echo htmlspecialchars($brand['address'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Açıklama</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($brand['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="mb-4">
                            <label for="status" class="form-label">Durum</label>
                            <select class="form-select" id="status" name="status">
                                <?php foreach ($brand_statuses as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" 
                                            <?php echo ($brand['status'] ?? 'active') === $key ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between">
                            <a href="brands.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Geri Dön
                            </a>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                <?php echo $action === 'add' ? 'Marka Ekle' : 'Değişiklikleri Kaydet'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
