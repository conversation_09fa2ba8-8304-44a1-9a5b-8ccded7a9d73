<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * User Model
 * 
 * Kullanıcı işlemleri için model sınıfı
 * 
 * @package EscortSite
 * <AUTHOR> Site Developer
 * @version 1.0
 */
class User_model extends CI_Model {

    protected $table = 'users';

    public function __construct() {
        parent::__construct();
    }

    /**
     * Kullanıcı girişi
     */
    public function login($email, $password) {
        $this->db->where('email', $email);
        $this->db->where('status !=', 'banned');
        $user = $this->db->get($this->table)->row();

        // Debug için log ekle
        log_message('debug', 'Login attempt for email: ' . $email);
        log_message('debug', 'User found: ' . ($user ? 'Yes' : 'No'));

        if ($user) {
            log_message('debug', 'User status: ' . $user->status);
            log_message('debug', 'Password verify: ' . (password_verify($password, $user->password) ? 'Success' : 'Failed'));

            if (password_verify($password, $user->password)) {
                return $user;
            }
        }

        return false;
    }

    /**
     * Kullanıcı oluştur
     */
    public function create_user($data) {
        if ($this->db->insert($this->table, $data)) {
            return $this->db->insert_id();
        }
        return false;
    }

    /**
     * Kullanıcıyı ID ile getir
     */
    public function get_user_by_id($id) {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }

    /**
     * Kullanıcıyı e-posta ile getir
     */
    public function get_user_by_email($email) {
        $this->db->where('email', $email);
        return $this->db->get($this->table)->row();
    }

    /**
     * Kullanıcıyı kullanıcı adı ile getir
     */
    public function get_user_by_username($username) {
        $this->db->where('username', $username);
        return $this->db->get($this->table)->row();
    }

    /**
     * E-posta doğrulama token'ı ile kullanıcı getir
     */
    public function get_user_by_verification_token($token) {
        $this->db->where('email_verification_token', $token);
        $this->db->where('email_verified', 0);
        return $this->db->get($this->table)->row();
    }

    /**
     * Şifre sıfırlama token'ı ile kullanıcı getir
     */
    public function get_user_by_reset_token($token) {
        $this->db->where('reset_token', $token);
        $this->db->where('reset_token_expires >', date('Y-m-d H:i:s'));
        return $this->db->get($this->table)->row();
    }

    /**
     * E-posta doğrula
     */
    public function verify_email($user_id) {
        $data = [
            'email_verified' => 1,
            'email_verification_token' => null,
            'status' => 'active'
        ];
        $this->db->where('id', $user_id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Şifre sıfırlama token'ı ayarla
     */
    public function set_reset_token($user_id, $token) {
        $data = [
            'reset_token' => $token,
            'reset_token_expires' => date('Y-m-d H:i:s', strtotime('+1 hour'))
        ];
        $this->db->where('id', $user_id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Şifre güncelle
     */
    public function update_password($user_id, $password) {
        $data = [
            'password' => $password,
            'reset_token' => null,
            'reset_token_expires' => null
        ];
        $this->db->where('id', $user_id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Son giriş bilgilerini güncelle
     */
    public function update_last_login($user_id) {
        $data = [
            'last_login' => date('Y-m-d H:i:s'),
            'last_ip' => $this->input->ip_address()
        ];
        $this->db->where('id', $user_id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Kullanıcı bilgilerini güncelle
     */
    public function update_user($user_id, $data) {
        $this->db->where('id', $user_id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Kullanıcı durumunu güncelle
     */
    public function update_status($user_id, $status) {
        $data = ['status' => $status];
        $this->db->where('id', $user_id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Kullanıcı sil
     */
    public function delete_user($user_id) {
        $this->db->where('id', $user_id);
        return $this->db->delete($this->table);
    }

    /**
     * Aktif kullanıcı sayısı
     */
    public function count_active_users() {
        $this->db->where('status', 'active');
        return $this->db->count_all_results($this->table);
    }

    /**
     * Escort sayısı
     */
    public function count_escorts() {
        $this->db->where('role', 'escort');
        $this->db->where('status', 'active');
        return $this->db->count_all_results($this->table);
    }

    /**
     * Müşteri sayısı
     */
    public function count_customers() {
        $this->db->where('role', 'customer');
        $this->db->where('status', 'active');
        return $this->db->count_all_results($this->table);
    }

    /**
     * Kullanıcı listesi (admin için)
     */
    public function get_users($limit = null, $offset = 0, $filters = []) {
        $this->db->select('u.*, p.display_name, p.city_id, c.name as city_name');
        $this->db->from($this->table . ' u');
        $this->db->join('profiles p', 'p.user_id = u.id', 'left');
        $this->db->join('cities c', 'c.id = p.city_id', 'left');

        // Filtreler
        if (!empty($filters['role'])) {
            $this->db->where('u.role', $filters['role']);
        }

        if (!empty($filters['status'])) {
            $this->db->where('u.status', $filters['status']);
        }

        if (!empty($filters['search'])) {
            $this->db->group_start();
            $this->db->like('u.username', $filters['search']);
            $this->db->or_like('u.email', $filters['search']);
            $this->db->or_like('p.display_name', $filters['search']);
            $this->db->group_end();
        }

        $this->db->order_by('u.created_at', 'DESC');

        if ($limit) {
            $this->db->limit($limit, $offset);
        }

        return $this->db->get()->result();
    }

    /**
     * Kullanıcı sayısı (filtrelenmiş)
     */
    public function count_users($filters = []) {
        $this->db->from($this->table . ' u');
        $this->db->join('profiles p', 'p.user_id = u.id', 'left');

        // Filtreler
        if (!empty($filters['role'])) {
            $this->db->where('u.role', $filters['role']);
        }

        if (!empty($filters['status'])) {
            $this->db->where('u.status', $filters['status']);
        }

        if (!empty($filters['search'])) {
            $this->db->group_start();
            $this->db->like('u.username', $filters['search']);
            $this->db->or_like('u.email', $filters['search']);
            $this->db->or_like('p.display_name', $filters['search']);
            $this->db->group_end();
        }

        return $this->db->count_all_results();
    }

    /**
     * E-posta benzersizlik kontrolü
     */
    public function is_email_unique($email, $exclude_id = null) {
        $this->db->where('email', $email);
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        return $this->db->count_all_results($this->table) === 0;
    }

    /**
     * Kullanıcı adı benzersizlik kontrolü
     */
    public function is_username_unique($username, $exclude_id = null) {
        $this->db->where('username', $username);
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        return $this->db->count_all_results($this->table) === 0;
    }

    /**
     * Son kayıt olan kullanıcılar
     */
    public function get_recent_users($limit = 10) {
        $this->db->select('u.*, p.display_name');
        $this->db->from($this->table . ' u');
        $this->db->join('profiles p', 'p.user_id = u.id', 'left');
        $this->db->order_by('u.created_at', 'DESC');
        $this->db->limit($limit);
        return $this->db->get()->result();
    }
}
