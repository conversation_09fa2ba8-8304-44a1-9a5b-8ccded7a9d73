<?php
/**
 * Dashboard - Ana Kontrol Paneli
 * <PERSON><PERSON><PERSON><PERSON><PERSON>, grafikler ve hızlı erişim
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Page settings
$page_title = 'Dashboard';

// Get dashboard statistics
try {
    global $database;
    $pdo = $database->connect();
    
    // Total counts
    $stats = [];
    
    // Influencers count
    $stmt = $pdo->query("SELECT COUNT(*) as total, 
                                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active 
                         FROM influencers");
    $influencer_stats = $stmt->fetch();
    $stats['influencers'] = $influencer_stats;
    
    // Campaigns count
    $stmt = $pdo->query("SELECT COUNT(*) as total,
                                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                                SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress
                         FROM campaigns");
    $campaign_stats = $stmt->fetch();
    $stats['campaigns'] = $campaign_stats;
    
    // Brands count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM brands WHERE status = 'active'");
    $brand_stats = $stmt->fetch();
    $stats['brands'] = $brand_stats;
    
    // Todos count
    $stmt = $pdo->query("SELECT COUNT(*) as total,
                                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                         FROM todos");
    $todo_stats = $stmt->fetch();
    $stats['todos'] = $todo_stats;
    
    // Recent activities
    $stmt = $pdo->query("
        SELECT 'influencer' as type, CONCAT(first_name, ' ', last_name) as title, created_at 
        FROM influencers 
        ORDER BY created_at DESC LIMIT 5
        
        UNION ALL
        
        SELECT 'campaign' as type, title, created_at 
        FROM campaigns 
        ORDER BY created_at DESC LIMIT 5
        
        ORDER BY created_at DESC LIMIT 10
    ");
    $recent_activities = $stmt->fetchAll();
    
    // Monthly campaign data for chart
    $stmt = $pdo->query("
        SELECT DATE_FORMAT(created_at, '%Y-%m') as month,
               COUNT(*) as count
        FROM campaigns 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
    ");
    $monthly_campaigns = $stmt->fetchAll();
    
    // Platform distribution
    $stmt = $pdo->query("
        SELECT platform, COUNT(*) as count
        FROM influencer_platforms 
        GROUP BY platform 
        ORDER BY count DESC
    ");
    $platform_distribution = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log('Dashboard error: ' . $e->getMessage());
    $stats = [
        'influencers' => ['total' => 0, 'active' => 0],
        'campaigns' => ['total' => 0, 'completed' => 0, 'in_progress' => 0],
        'brands' => ['total' => 0],
        'todos' => ['total' => 0, 'pending' => 0, 'completed' => 0]
    ];
    $recent_activities = [];
    $monthly_campaigns = [];
    $platform_distribution = [];
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h2 class="mb-2">Hoş geldiniz, <?php echo htmlspecialchars($_SESSION['full_name']); ?>!</h2>
                        <p class="mb-0 opacity-75">
                            Bugün <?php echo date('d F Y, l'); ?> - 
                            Sistemde <?php echo $stats['influencers']['total']; ?> influencer ve 
                            <?php echo $stats['campaigns']['total']; ?> kampanya bulunuyor.
                        </p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-icon bg-primary">
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="text-end">
                        <h3 class="mb-1"><?php echo number_format($stats['influencers']['total']); ?></h3>
                        <p class="text-muted mb-0">Toplam Influencer</p>
                        <small class="text-success">
                            <i class="fas fa-check-circle"></i>
                            <?php echo $stats['influencers']['active']; ?> aktif
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-icon bg-success">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="text-end">
                        <h3 class="mb-1"><?php echo number_format($stats['campaigns']['total']); ?></h3>
                        <p class="text-muted mb-0">Toplam Kampanya</p>
                        <small class="text-info">
                            <i class="fas fa-clock"></i>
                            <?php echo $stats['campaigns']['in_progress']; ?> devam ediyor
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-icon bg-warning">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="text-end">
                        <h3 class="mb-1"><?php echo number_format($stats['brands']['total']); ?></h3>
                        <p class="text-muted mb-0">Aktif Marka</p>
                        <small class="text-muted">
                            <i class="fas fa-handshake"></i>
                            İş ortakları
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-icon bg-info">
                        <i class="fas fa-tasks"></i>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="text-end">
                        <h3 class="mb-1"><?php echo number_format($stats['todos']['pending']); ?></h3>
                        <p class="text-muted mb-0">Bekleyen Görev</p>
                        <small class="text-success">
                            <i class="fas fa-check"></i>
                            <?php echo $stats['todos']['completed']; ?> tamamlandı
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Recent Activities -->
<div class="row">
    <!-- Campaign Chart -->
    <div class="col-xl-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Aylık Kampanya Trendi
                </h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                        Son 6 Ay
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">Son 3 Ay</a></li>
                        <li><a class="dropdown-item" href="#">Son 6 Ay</a></li>
                        <li><a class="dropdown-item" href="#">Son 1 Yıl</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <canvas id="campaignChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Platform Distribution -->
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-share-alt me-2"></i>
                    Platform Dağılımı
                </h5>
            </div>
            <div class="card-body">
                <canvas id="platformChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities and Quick Actions -->
<div class="row">
    <!-- Recent Activities -->
    <div class="col-xl-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Son Aktiviteler
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_activities)): ?>
                    <div class="timeline">
                        <?php foreach ($recent_activities as $activity): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-<?php echo $activity['type'] === 'influencer' ? 'user-plus' : 'bullhorn'; ?>"></i>
                                </div>
                                <div class="timeline-content">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($activity['title']); ?></h6>
                                    <small class="text-muted">
                                        <?php echo date('d.m.Y H:i', strtotime($activity['created_at'])); ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Henüz aktivite bulunmuyor</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Hızlı İşlemler
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="influencers.php?action=add" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        Yeni Influencer Ekle
                    </a>
                    <a href="campaigns.php?action=add" class="btn btn-success">
                        <i class="fas fa-bullhorn me-2"></i>
                        Yeni Kampanya Oluştur
                    </a>
                    <a href="brands.php?action=add" class="btn btn-warning">
                        <i class="fas fa-building me-2"></i>
                        Yeni Marka Ekle
                    </a>
                    <a href="todos.php?action=add" class="btn btn-info">
                        <i class="fas fa-plus me-2"></i>
                        Yeni Görev Ekle
                    </a>
                </div>
                
                <hr class="my-3">
                
                <h6 class="mb-3">Hızlı Linkler</h6>
                <div class="list-group list-group-flush">
                    <a href="reports.php" class="list-group-item list-group-item-action border-0 px-0">
                        <i class="fas fa-chart-bar me-2"></i>Raporları Görüntüle
                    </a>
                    <a href="files.php" class="list-group-item list-group-item-action border-0 px-0">
                        <i class="fas fa-folder me-2"></i>Dosya Yönetimi
                    </a>
                    <a href="settings.php" class="list-group-item list-group-item-action border-0 px-0">
                        <i class="fas fa-cog me-2"></i>Sistem Ayarları
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Page specific scripts
$page_scripts = '
<script>
// Campaign Chart
const campaignCtx = document.getElementById("campaignChart").getContext("2d");
const campaignChart = new Chart(campaignCtx, {
    type: "line",
    data: {
        labels: ' . json_encode(array_column($monthly_campaigns, 'month')) . ',
        datasets: [{
            label: "Kampanyalar",
            data: ' . json_encode(array_column($monthly_campaigns, 'count')) . ',
            borderColor: "#667eea",
            backgroundColor: "rgba(102, 126, 234, 0.1)",
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: "rgba(0,0,0,0.1)"
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});

// Platform Chart
const platformCtx = document.getElementById("platformChart").getContext("2d");
const platformChart = new Chart(platformCtx, {
    type: "doughnut",
    data: {
        labels: ' . json_encode(array_column($platform_distribution, 'platform')) . ',
        datasets: [{
            data: ' . json_encode(array_column($platform_distribution, 'count')) . ',
            backgroundColor: [
                "#667eea", "#764ba2", "#f093fb", "#f5576c",
                "#4facfe", "#00f2fe", "#43e97b", "#38f9d7"
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: "bottom"
            }
        }
    }
});

// Refresh dashboard data
function refreshDashboardData() {
    // This function can be called to refresh dashboard data via AJAX
    console.log("Refreshing dashboard data...");
}
</script>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 20px;
    height: 20px;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.timeline::before {
    content: "";
    position: absolute;
    left: -21px;
    top: 10px;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}
</style>
';

// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
