<?php
// Login test scripti
require_once 'application/config/database.php';

$host = $db['default']['hostname'];
$username = $db['default']['username'];
$password = $db['default']['password'];
$database = $db['default']['database'];

echo "<h1>Login Test</h1>";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Test kullanıcılarını listele
    $stmt = $pdo->query("SELECT id, username, email, password, role, status FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Veritabanındaki Test Kullanıcıları:</h2>";
    
    if (empty($users)) {
        echo "<p style='color: red;'>❌ Test kullanıcıları bulunamadı!</p>";
        echo "<p><a href='setup.php'>Setup.php'yi çalıştırın</a></p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Role</th><th>Status</th><th>Password Test</th></tr>";
        
        foreach ($users as $user) {
            $password_test = password_verify('test123', $user['password']);
            
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['username'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "<td>" . $user['status'] . "</td>";
            echo "<td style='color: " . ($password_test ? 'green' : 'red') . ";'>" . ($password_test ? '✅ Doğru' : '❌ Hatalı') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Manuel şifre testi
    echo "<h2>Manuel Şifre Testi:</h2>";
    $test_password = 'test123';
    $test_hash = '$2y$10$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW';
    
    echo "<p><strong>Test Şifre:</strong> $test_password</p>";
    echo "<p><strong>Test Hash:</strong> $test_hash</p>";
    echo "<p><strong>Sonuç:</strong> " . (password_verify($test_password, $test_hash) ? '✅ Eşleşiyor' : '❌ Eşleşmiyor') . "</p>";
    
    // Yeni hash oluştur
    $new_hash = password_hash($test_password, PASSWORD_DEFAULT);
    echo "<p><strong>Yeni Hash:</strong> $new_hash</p>";
    echo "<p><strong>Yeni Hash Test:</strong> " . (password_verify($test_password, $new_hash) ? '✅ Çalışıyor' : '❌ Çalışmıyor') . "</p>";
    
    // Şifreleri güncelle
    if (isset($_GET['update_passwords'])) {
        echo "<h2>Şifreleri Güncelleme:</h2>";
        
        $new_hash = password_hash('test123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')");
        $stmt->execute([$new_hash]);
        
        echo "<p style='color: green;'>✅ Şifreler güncellendi!</p>";
        echo "<p><a href='test_login.php'>Sayfayı yenile</a></p>";
    } else {
        echo "<p><a href='test_login.php?update_passwords=1' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Şifreleri Güncelle</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Hata: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php/auth/login'>Giriş Sayfasına Git</a></p>";
?>
