/**
 * ESCORT İLAN SİTESİ - ANA JAVASCRIPT DOSYASI
 * 
 * Bu dosya sitenin genel JavaScript fonksiyonlarını içerir
 * 
 * <AUTHOR> Site Developer
 * @version 1.0
 */

(function($) {
    'use strict';

    // Site ayarları
    const SITE_CONFIG = {
        baseUrl: window.location.origin + '/',
        ajaxTimeout: 30000,
        imageLoadDelay: 300,
        scrollOffset: 70
    };

    // DOM hazır olduğunda çalışacak fonksiyonlar
    $(document).ready(function() {
        initializeComponents();
        bindEvents();
        setupAjaxDefaults();
    });

    /**
     * Bileşenleri başlat
     */
    function initializeComponents() {
        // Tooltip'leri başlat
        initTooltips();
        
        // Lazy loading'i başlat
        initLazyLoading();
        
        // Form validasyonunu başlat
        initFormValidation();
        
        // Smooth scrolling'i başlat
        initSmoothScrolling();
        
        // Back to top butonunu başlat
        initBackToTop();
        
        // Search suggestions'ı başlat
        initSearchSuggestions();
        
        // Image gallery'yi başlat
        initImageGallery();
        
        // Auto-hide alerts
        autoHideAlerts();
        
        console.log('✅ Tüm bileşenler başarıyla başlatıldı');
    }

    /**
     * Event listener'ları bağla
     */
    function bindEvents() {
        // Navbar toggle
        $('.navbar-toggler').on('click', function() {
            $(this).toggleClass('active');
        });

        // Search form
        $('#searchForm').on('submit', handleSearchSubmit);
        
        // Contact form
        $('#contactForm').on('submit', handleContactSubmit);
        
        // Ad card hover effects
        $('.ad-card').hover(handleAdCardHover, handleAdCardLeave);
        
        // City card click
        $('.city-card').on('click', handleCityCardClick);
        
        // Price formatting
        $('.price').each(formatPrice);
        
        // External links
        $('a[href^="http"]:not([href*="' + window.location.hostname + '"])').attr({
            'target': '_blank',
            'rel': 'noopener noreferrer'
        });
    }

    /**
     * AJAX varsayılan ayarları
     */
    function setupAjaxDefaults() {
        $.ajaxSetup({
            timeout: SITE_CONFIG.ajaxTimeout,
            beforeSend: function(xhr) {
                // CSRF token ekle (CodeIgniter için)
                const token = $('meta[name="csrf-token"]').attr('content');
                if (token) {
                    xhr.setRequestHeader('X-CSRF-TOKEN', token);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Hatası:', error);
                showNotification('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
            }
        });
    }

    /**
     * Tooltip'leri başlat
     */
    function initTooltips() {
        $('[data-bs-toggle="tooltip"]').tooltip();
    }

    /**
     * Lazy loading başlat
     */
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('fade-in');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Form validasyonu başlat
     */
    function initFormValidation() {
        $('.needs-validation').on('submit', function(event) {
            if (this.checkValidity() === false) {
                event.preventDefault();
                event.stopPropagation();
            }
            $(this).addClass('was-validated');
        });
    }

    /**
     * Smooth scrolling başlat
     */
    function initSmoothScrolling() {
        $('a[href*="#"]:not([href="#"])').on('click', function() {
            if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && 
                location.hostname === this.hostname) {
                
                const target = $(this.hash);
                const targetElement = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                
                if (targetElement.length) {
                    $('html, body').animate({
                        scrollTop: targetElement.offset().top - SITE_CONFIG.scrollOffset
                    }, 800);
                    return false;
                }
            }
        });
    }

    /**
     * Back to top butonu
     */
    function initBackToTop() {
        const $backToTop = $('#backToTop');
        
        $(window).on('scroll', function() {
            if ($(this).scrollTop() > 100) {
                $backToTop.fadeIn();
            } else {
                $backToTop.fadeOut();
            }
        });

        $backToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 800);
            return false;
        });
    }

    /**
     * Arama önerileri
     */
    function initSearchSuggestions() {
        const $searchInput = $('#searchInput');
        
        if ($searchInput.length) {
            let searchTimeout;
            
            $searchInput.on('input', function() {
                const query = $(this).val().trim();
                
                clearTimeout(searchTimeout);
                
                if (query.length > 2) {
                    searchTimeout = setTimeout(() => {
                        fetchSearchSuggestions(query);
                    }, 300);
                } else {
                    hideSearchSuggestions();
                }
            });
            
            // Dışarı tıklandığında önerileri gizle
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.search-container').length) {
                    hideSearchSuggestions();
                }
            });
        }
    }

    /**
     * Resim galerisi
     */
    function initImageGallery() {
        $('.gallery-image').on('click', function() {
            const src = $(this).attr('src');
            const alt = $(this).attr('alt');
            showImageModal(src, alt);
        });
    }

    /**
     * Alert'leri otomatik gizle
     */
    function autoHideAlerts() {
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    }

    /**
     * Arama formu submit
     */
    function handleSearchSubmit(e) {
        e.preventDefault();
        
        const formData = $(this).serialize();
        const searchUrl = $(this).attr('action') + '?' + formData;
        
        window.location.href = searchUrl;
    }

    /**
     * İletişim formu submit
     */
    function handleContactSubmit(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const originalText = $submitBtn.text();
        
        // Loading state
        $submitBtn.prop('disabled', true).html('<span class="loading"></span> Gönderiliyor...');
        
        $.ajax({
            url: $form.attr('action'),
            method: 'POST',
            data: $form.serialize(),
            success: function(response) {
                showNotification('Mesajınız başarıyla gönderildi!', 'success');
                $form[0].reset();
            },
            error: function() {
                showNotification('Mesaj gönderilemedi. Lütfen tekrar deneyin.', 'error');
            },
            complete: function() {
                $submitBtn.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * İlan kartı hover
     */
    function handleAdCardHover() {
        $(this).find('.card-img-top').addClass('scale-hover');
    }

    function handleAdCardLeave() {
        $(this).find('.card-img-top').removeClass('scale-hover');
    }

    /**
     * Şehir kartı tıklama
     */
    function handleCityCardClick() {
        const href = $(this).find('a').attr('href');
        if (href) {
            window.location.href = href;
        }
    }

    /**
     * Fiyat formatla
     */
    function formatPrice() {
        const price = $(this).text().trim();
        if (price && !isNaN(price)) {
            const formatted = new Intl.NumberFormat('tr-TR', {
                style: 'currency',
                currency: 'TRY'
            }).format(price);
            $(this).text(formatted);
        }
    }

    /**
     * Arama önerilerini getir
     */
    function fetchSearchSuggestions(query) {
        $.ajax({
            url: SITE_CONFIG.baseUrl + 'api/search_suggestions',
            method: 'GET',
            data: { q: query },
            success: function(response) {
                showSearchSuggestions(response.suggestions);
            },
            error: function() {
                console.warn('Arama önerileri alınamadı');
            }
        });
    }

    /**
     * Arama önerilerini göster
     */
    function showSearchSuggestions(suggestions) {
        const $container = $('.search-suggestions');
        
        if (suggestions && suggestions.length > 0) {
            let html = '<ul class="list-unstyled">';
            suggestions.forEach(suggestion => {
                html += `<li><a href="${suggestion.url}" class="d-block p-2 text-decoration-none">${suggestion.title}</a></li>`;
            });
            html += '</ul>';
            
            $container.html(html).show();
        } else {
            hideSearchSuggestions();
        }
    }

    /**
     * Arama önerilerini gizle
     */
    function hideSearchSuggestions() {
        $('.search-suggestions').hide();
    }

    /**
     * Resim modal göster
     */
    function showImageModal(src, alt) {
        const modalHtml = `
            <div class="modal fade" id="imageModal" tabindex="-1">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${alt || 'Resim'}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <img src="${src}" class="img-fluid" alt="${alt || 'Resim'}">
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('imageModal'));
        modal.show();
        
        // Modal kapandığında DOM'dan kaldır
        $('#imageModal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    }

    /**
     * Bildirim göster
     */
    function showNotification(message, type = 'info') {
        const alertClass = `alert-${type}`;
        const iconClass = type === 'success' ? 'fa-check-circle' : 
                         type === 'error' ? 'fa-exclamation-circle' : 
                         'fa-info-circle';
        
        const notificationHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <i class="fas ${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('body').append(notificationHtml);
        
        // 5 saniye sonra otomatik kaldır
        setTimeout(() => {
            $('.alert').last().fadeOut(() => {
                $(this).remove();
            });
        }, 5000);
    }

    /**
     * Utility fonksiyonları
     */
    window.EscortSite = {
        showNotification: showNotification,
        formatPrice: formatPrice,
        showImageModal: showImageModal
    };

})(jQuery);
