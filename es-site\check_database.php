<?php
// Veritabanı kontrol scripti
require_once 'application/config/database.php';

$host = $db['default']['hostname'];
$username = $db['default']['username'];
$password = $db['default']['password'];
$database = $db['default']['database'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Veritabanı Bağlantı Testi</h2>";
    echo "<p style='color: green;'>✅ Veritabanı bağlantısı başarılı!</p>";
    
    // Tabloları kontrol et
    $tables = ['users', 'cities', 'categories', 'profiles', 'ads'];
    
    echo "<h3>Tablo Durumu:</h3>";
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p>✅ <strong>$table:</strong> $count kayıt</p>";
        } catch (Exception $e) {
            echo "<p>❌ <strong>$table:</strong> Tablo bulunamadı</p>";
        }
    }
    
    // Kullanıcıları listele
    echo "<h3>Mevcut Kullanıcılar:</h3>";
    try {
        $stmt = $pdo->query("SELECT id, username, email, role, status FROM users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($users)) {
            echo "<p>❌ Hiç kullanıcı bulunamadı!</p>";
            echo "<p><strong>Çözüm:</strong> insert_test_data.sql dosyasını phpMyAdmin'de çalıştırın.</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Kullanıcı Adı</th><th>E-posta</th><th>Rol</th><th>Durum</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td>" . $user['username'] . "</td>";
                echo "<td>" . $user['email'] . "</td>";
                echo "<td>" . $user['role'] . "</td>";
                echo "<td>" . $user['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Kullanıcılar tablosu okunamadı: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>Veritabanı Bağlantı Hatası</h2>";
    echo "<p style='color: red;'>❌ " . $e->getMessage() . "</p>";
    echo "<p><strong>Çözüm:</strong></p>";
    echo "<ol>";
    echo "<li>XAMPP'ta MySQL'in çalıştığından emin olun</li>";
    echo "<li>database_setup.sql dosyasını phpMyAdmin'de çalıştırın</li>";
    echo "<li>application/config/database.php dosyasındaki ayarları kontrol edin</li>";
    echo "</ol>";
}
?>
