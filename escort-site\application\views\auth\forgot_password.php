<!-- Forgot Password Section -->
<section class="py-5 bg-light min-vh-100 d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-warning text-dark text-center py-4">
                        <h2 class="mb-0">
                            <i class="fas fa-key me-2"></i>
                            Şifre Sıfırlama
                        </h2>
                        <p class="mb-0 mt-2">Şifrenizi sıfırlamak için e-posta adresinizi girin</p>
                    </div>
                    
                    <div class="card-body p-5">
                        <?php echo validation_errors('<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>', '</div>'); ?>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            E-posta adresinizi girin, size şifre sıfırlama linki gönderelim.
                        </div>

                        <form action="<?php echo site_url('auth/forgot_password'); ?>" method="POST" class="needs-validation" novalidate>
                            <div class="mb-4">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>E-posta Adresi
                                </label>
                                <input type="email" class="form-control form-control-lg" id="email" name="email" 
                                       value="<?php echo set_value('email'); ?>" 
                                       placeholder="<EMAIL>" required>
                                <div class="invalid-feedback">
                                    Lütfen geçerli bir e-posta adresi girin.
                                </div>
                            </div>
                            
                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-warning btn-lg text-dark">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Sıfırlama Linki Gönder
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center">
                            <a href="<?php echo site_url('auth/login'); ?>" class="text-decoration-none">
                                <i class="fas fa-arrow-left me-1"></i>Giriş Sayfasına Dön
                            </a>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-light text-center py-3">
                        <p class="mb-0">
                            Hesabınız yok mu? 
                            <a href="<?php echo site_url('auth/register'); ?>" class="text-primary fw-bold text-decoration-none">
                                Kayıt Ol
                            </a>
                        </p>
                    </div>
                </div>
                
                <!-- Help Info -->
                <div class="card mt-4 border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-question-circle me-2"></i>
                            Yardım
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><i class="fas fa-check text-success me-2"></i>E-posta adresinizi doğru yazdığınızdan emin olun</li>
                            <li><i class="fas fa-check text-success me-2"></i>Spam klasörünüzü kontrol edin</li>
                            <li><i class="fas fa-check text-success me-2"></i>Link 1 saat geçerlidir</li>
                            <li><i class="fas fa-check text-success me-2"></i>Sorun yaşıyorsanız bizimle iletişime geçin</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.min-vh-100 {
    min-height: 100vh;
}

.card {
    border-radius: 15px;
    overflow: hidden;
}

.form-control-lg {
    border-radius: 10px;
    padding: 15px;
}

.btn-lg {
    border-radius: 10px;
    padding: 15px;
    font-weight: 600;
}

@media (max-width: 768px) {
    .card-body {
        padding: 2rem !important;
    }
}
</style>

<script>
$(document).ready(function() {
    // Form validation
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });
    
    // Auto focus on email field
    $('#email').focus();
});
</script>
