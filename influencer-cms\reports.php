<?php
/**
 * Reports and Analytics
 * Raporlar ve analitik sistemi
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Check permissions
requirePermission('reports_view');

// Page settings
$page_title = 'Raporlar ve Analitik';
$report_type = $_GET['type'] ?? 'overview';
$date_range = $_GET['range'] ?? '30';
$start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
$end_date = $_GET['end_date'] ?? date('Y-m-d');

// Calculate date range - Use explicit table references to avoid ambiguity
$campaigns_date_condition = "DATE(created_at) BETWEEN '$start_date' AND '$end_date'";
$campaign_join_date_condition = "DATE(c.created_at) BETWEEN '$start_date' AND '$end_date'";

// Initialize variables to prevent undefined variable errors
$stats = [];
$campaign_status = [];
$platform_stats = [];
$monthly_trend = [];
$top_influencers = [];
$top_brands = [];
$campaigns_report = [];
$influencers_report = [];
$financial_monthly = [];
$financial_platform = [];
$financial_content = [];

try {
    global $database;
    $pdo = $database->connect();
    
    // Overview Statistics
    if ($report_type === 'overview') {
        // Total counts with error handling
        $stats = [];

        // Check if tables exist first
        $tables_exist = true;
        $required_tables = ['influencers', 'campaigns', 'brands'];

        foreach ($required_tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() == 0) {
                $tables_exist = false;
                break;
            }
        }

        if ($tables_exist) {
            // Influencers count
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM influencers WHERE status = 'active'");
            $stats['total_influencers'] = $stmt->fetch()['total'] ?? 0;

            // Campaigns count
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM campaigns WHERE $campaigns_date_condition");
            $stats['total_campaigns'] = $stmt->fetch()['total'] ?? 0;

            // Brands count
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM brands WHERE status = 'active'");
            $stats['total_brands'] = $stmt->fetch()['total'] ?? 0;

            // Revenue sum
            $stmt = $pdo->query("SELECT COALESCE(SUM(price), 0) as total FROM campaigns WHERE $campaigns_date_condition");
            $stats['total_revenue'] = $stmt->fetch()['total'] ?? 0;
        } else {
            // Default values if tables don't exist
            $stats = [
                'total_influencers' => 0,
                'total_campaigns' => 0,
                'total_brands' => 0,
                'total_revenue' => 0
            ];
        }
        
        // Campaign status breakdown (only if campaigns exist)
        if ($stats['total_campaigns'] > 0) {
            $stmt = $pdo->query("
                SELECT status, COUNT(*) as count
                FROM campaigns
                WHERE $campaigns_date_condition
                GROUP BY status
            ");
            $campaign_status = $stmt->fetchAll();
        } else {
            $campaign_status = [];
        }

        // Platform breakdown (only if campaigns exist)
        if ($stats['total_campaigns'] > 0) {
            $stmt = $pdo->query("
                SELECT platform, COUNT(*) as count, COALESCE(SUM(price), 0) as revenue
                FROM campaigns
                WHERE $campaigns_date_condition
                GROUP BY platform
                ORDER BY count DESC
            ");
            $platform_stats = $stmt->fetchAll();
        } else {
            $platform_stats = [];
        }
        
        // Monthly trend (always show, even if empty)
        $stmt = $pdo->query("
            SELECT
                DATE_FORMAT(created_at, '%Y-%m') as month,
                COUNT(*) as campaigns,
                COALESCE(SUM(price), 0) as revenue
            FROM campaigns
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month
        ");
        $monthly_trend = $stmt->fetchAll();

        // Top influencers (only if campaigns exist)
        if ($stats['total_campaigns'] > 0) {
            $stmt = $pdo->query("
                SELECT
                    i.id,
                    CONCAT(i.first_name, ' ', i.last_name) as name,
                    COUNT(c.id) as campaign_count,
                    COALESCE(SUM(c.price), 0) as total_revenue
                FROM influencers i
                LEFT JOIN campaigns c ON i.id = c.influencer_id AND $campaign_join_date_condition
                WHERE i.status = 'active'
                GROUP BY i.id
                HAVING campaign_count > 0
                ORDER BY total_revenue DESC
                LIMIT 10
            ");
            $top_influencers = $stmt->fetchAll();
        } else {
            $top_influencers = [];
        }

        // Top brands (only if campaigns exist)
        if ($stats['total_campaigns'] > 0) {
            $stmt = $pdo->query("
                SELECT
                    b.id,
                    b.name,
                    COUNT(c.id) as campaign_count,
                    COALESCE(SUM(c.price), 0) as total_spent
                FROM brands b
                LEFT JOIN campaigns c ON b.id = c.brand_id AND $campaign_join_date_condition
                WHERE b.status = 'active'
                GROUP BY b.id
                HAVING campaign_count > 0
                ORDER BY total_spent DESC
                LIMIT 10
            ");
            $top_brands = $stmt->fetchAll();
        } else {
            $top_brands = [];
        }
    }
    
    // Campaign Performance Report
    elseif ($report_type === 'campaigns') {
        // Check if campaigns table has data
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM campaigns WHERE $campaigns_date_condition");
        $campaign_count = $stmt->fetch()['count'];

        if ($campaign_count > 0) {
            $stmt = $pdo->query("
                SELECT
                    c.*,
                    COALESCE(CONCAT(i.first_name, ' ', i.last_name), 'Bilinmeyen') as influencer_name,
                    COALESCE(b.name, 'Bilinmeyen') as brand_name
                FROM campaigns c
                LEFT JOIN influencers i ON c.influencer_id = i.id
                LEFT JOIN brands b ON c.brand_id = b.id
                WHERE DATE(c.created_at) BETWEEN '$start_date' AND '$end_date'
                ORDER BY c.created_at DESC
            ");
            $campaigns_report = $stmt->fetchAll();
        } else {
            $campaigns_report = [];
        }
    }
    
    // Influencer Performance Report
    elseif ($report_type === 'influencers') {
        // Check if influencers table exists and has data
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM influencers WHERE status = 'active'");
        $influencer_count = $stmt->fetch()['count'];

        if ($influencer_count > 0) {
            $stmt = $pdo->query("
                SELECT
                    i.*,
                    COUNT(c.id) as campaign_count,
                    COALESCE(SUM(c.price), 0) as total_revenue,
                    AVG(c.rating) as avg_rating,
                    MAX(c.created_at) as last_campaign
                FROM influencers i
                LEFT JOIN campaigns c ON i.id = c.influencer_id AND $campaign_join_date_condition
                WHERE i.status = 'active'
                GROUP BY i.id
                ORDER BY total_revenue DESC
            ");
            $influencers_report = $stmt->fetchAll();
        } else {
            $influencers_report = [];
        }
    }
    
    // Financial Report
    elseif ($report_type === 'financial') {
        // Check if campaigns have financial data
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM campaigns WHERE $campaigns_date_condition AND price > 0");
        $financial_count = $stmt->fetch()['count'];

        if ($financial_count > 0) {
            // Revenue by month
            $stmt = $pdo->query("
                SELECT
                    DATE_FORMAT(created_at, '%Y-%m') as month,
                    COUNT(*) as campaigns,
                    COALESCE(SUM(price), 0) as revenue,
                    COALESCE(AVG(price), 0) as avg_price
                FROM campaigns
                WHERE $campaigns_date_condition
                GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                ORDER BY month
            ");
            $financial_monthly = $stmt->fetchAll();

            // Revenue by platform
            $stmt = $pdo->query("
                SELECT
                    platform,
                    COUNT(*) as campaigns,
                    COALESCE(SUM(price), 0) as revenue,
                    COALESCE(AVG(price), 0) as avg_price
                FROM campaigns
                WHERE $campaigns_date_condition
                GROUP BY platform
                ORDER BY revenue DESC
            ");
            $financial_platform = $stmt->fetchAll();

            // Revenue by content type
            $stmt = $pdo->query("
                SELECT
                    content_type,
                    COUNT(*) as campaigns,
                    COALESCE(SUM(price), 0) as revenue,
                    COALESCE(AVG(price), 0) as avg_price
                FROM campaigns
                WHERE $campaigns_date_condition
                GROUP BY content_type
                ORDER BY revenue DESC
            ");
            $financial_content = $stmt->fetchAll();
        } else {
            $financial_monthly = [];
            $financial_platform = [];
            $financial_content = [];
        }
    }
    
} catch (Exception $e) {
    error_log('Reports error: ' . $e->getMessage());
    $_SESSION['error_message'] = 'Rapor verileri alınırken hata oluştu: ' . $e->getMessage();

    // Set default values to prevent further errors
    $stats = [
        'total_influencers' => 0,
        'total_campaigns' => 0,
        'total_brands' => 0,
        'total_revenue' => 0
    ];
    $campaign_status = [];
    $platform_stats = [];
    $monthly_trend = [];
    $top_influencers = [];
    $top_brands = [];
    $campaigns_report = [];
    $influencers_report = [];
    $financial_monthly = [];
    $financial_platform = [];
    $financial_content = [];
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Reports Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="fas fa-chart-line me-2"></i>
                            Raporlar ve Analitik
                        </h2>
                        <p class="mb-0 opacity-75">Detaylı performans analizi ve istatistikler</p>
                    </div>
                    <div class="text-end">
                        <div class="btn-group">
                            <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#dateRangeModal">
                                <i class="fas fa-calendar me-2"></i>
                                <?php echo date('d.m.Y', strtotime($start_date)); ?> - <?php echo date('d.m.Y', strtotime($end_date)); ?>
                            </button>
                            <button type="button" class="btn btn-light" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>Yazdır
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Navigation -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <ul class="nav nav-pills nav-fill">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $report_type === 'overview' ? 'active' : ''; ?>" 
                           href="reports.php?type=overview&start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>">
                            <i class="fas fa-tachometer-alt me-2"></i>Genel Bakış
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $report_type === 'campaigns' ? 'active' : ''; ?>" 
                           href="reports.php?type=campaigns&start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>">
                            <i class="fas fa-bullhorn me-2"></i>Kampanyalar
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $report_type === 'influencers' ? 'active' : ''; ?>" 
                           href="reports.php?type=influencers&start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>">
                            <i class="fas fa-star me-2"></i>Influencer'lar
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $report_type === 'financial' ? 'active' : ''; ?>" 
                           href="reports.php?type=financial&start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>">
                            <i class="fas fa-money-bill-wave me-2"></i>Finansal
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php if ($report_type === 'overview'): ?>
    <!-- Overview Report -->
    <div class="row mb-4">
        <!-- Key Metrics -->
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['total_influencers']); ?></h3>
                    <p class="text-muted mb-0">Aktif Influencer</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-bullhorn fa-2x"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['total_campaigns']); ?></h3>
                    <p class="text-muted mb-0">Toplam Kampanya</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['total_brands']); ?></h3>
                    <p class="text-muted mb-0">Aktif Marka</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-lira-sign fa-2x"></i>
                    </div>
                    <h3 class="mb-1"><?php echo formatCurrency($stats['total_revenue'], 'TRY'); ?></h3>
                    <p class="text-muted mb-0">Toplam Gelir</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <!-- Campaign Status Chart -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Kampanya Durumları</h6>
                </div>
                <div class="card-body">
                    <canvas id="campaignStatusChart" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Platform Performance -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Platform Performansı</h6>
                </div>
                <div class="card-body">
                    <canvas id="platformChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <!-- Monthly Trend -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Aylık Trend</h6>
                </div>
                <div class="card-body">
                    <canvas id="monthlyTrendChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Top Influencers -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">En Başarılı Influencer'lar</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($top_influencers)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Influencer</th>
                                        <th>Kampanya</th>
                                        <th>Gelir</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($top_influencers as $influencer): ?>
                                        <tr>
                                            <td>
                                                <a href="influencer_detail.php?id=<?php echo $influencer['id']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($influencer['name']); ?>
                                                </a>
                                            </td>
                                            <td><?php echo $influencer['campaign_count']; ?></td>
                                            <td><?php echo formatCurrency($influencer['total_revenue'], 'TRY'); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">Bu dönemde kampanya bulunamadı.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Top Brands -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">En Aktif Markalar</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($top_brands)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Marka</th>
                                        <th>Kampanya</th>
                                        <th>Harcama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($top_brands as $brand): ?>
                                        <tr>
                                            <td>
                                                <a href="brands.php?action=edit&id=<?php echo $brand['id']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($brand['name']); ?>
                                                </a>
                                            </td>
                                            <td><?php echo $brand['campaign_count']; ?></td>
                                            <td><?php echo formatCurrency($brand['total_spent'], 'TRY'); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">Bu dönemde kampanya bulunamadı.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<?php elseif ($report_type === 'campaigns'): ?>
    <!-- Campaigns Report -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Kampanya Performans Raporu</h6>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportToCSV('campaigns')">
                        <i class="fas fa-download me-2"></i>CSV İndir
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!empty($campaigns_report)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="campaignsTable">
                                <thead>
                                    <tr>
                                        <th>Kampanya</th>
                                        <th>Influencer</th>
                                        <th>Marka</th>
                                        <th>Platform</th>
                                        <th>İçerik Türü</th>
                                        <th>Fiyat</th>
                                        <th>Durum</th>
                                        <th>Değerlendirme</th>
                                        <th>Tarih</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($campaigns_report as $campaign): ?>
                                        <tr>
                                            <td>
                                                <a href="campaigns.php?action=edit&id=<?php echo $campaign['id']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($campaign['title']); ?>
                                                </a>
                                            </td>
                                            <td>
                                                <a href="influencer_detail.php?id=<?php echo $campaign['influencer_id']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($campaign['influencer_name']); ?>
                                                </a>
                                            </td>
                                            <td><?php echo htmlspecialchars($campaign['brand_name']); ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="<?php echo PLATFORMS[$campaign['platform']]['icon'] ?? 'fas fa-globe'; ?> me-2"
                                                       style="color: <?php echo PLATFORMS[$campaign['platform']]['color'] ?? '#6c757d'; ?>"></i>
                                                    <?php echo PLATFORMS[$campaign['platform']]['name'] ?? ucfirst($campaign['platform']); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo ucfirst($campaign['content_type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    <?php echo formatCurrency($campaign['price'], $campaign['currency']); ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo STATUS_COLORS[$campaign['status']] ?? 'secondary'; ?>">
                                                    <?php echo ucfirst($campaign['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($campaign['rating']): ?>
                                                    <div class="text-warning">
                                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                                            <i class="fas fa-star<?php echo $i <= $campaign['rating'] ? '' : '-o'; ?>"></i>
                                                        <?php endfor; ?>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small><?php echo date('d.m.Y', strtotime($campaign['created_at'])); ?></small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Bu dönemde kampanya bulunamadı</h5>
                            <p class="text-muted">Farklı bir tarih aralığı seçin.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<?php elseif ($report_type === 'influencers'): ?>
    <!-- Influencers Report -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Influencer Performans Raporu</h6>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportToCSV('influencers')">
                        <i class="fas fa-download me-2"></i>CSV İndir
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!empty($influencers_report)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="influencersTable">
                                <thead>
                                    <tr>
                                        <th>Influencer</th>
                                        <th>Şehir</th>
                                        <th>Çalışma Türü</th>
                                        <th>Kampanya Sayısı</th>
                                        <th>Toplam Gelir</th>
                                        <th>Ortalama Değerlendirme</th>
                                        <th>Son Kampanya</th>
                                        <th>Durum</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($influencers_report as $influencer): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($influencer['profile_photo']): ?>
                                                        <img src="<?php echo UPLOAD_URL . 'profiles/' . $influencer['profile_photo']; ?>"
                                                             class="rounded-circle me-2" width="32" height="32" style="object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2"
                                                             style="width: 32px; height: 32px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <a href="influencer_detail.php?id=<?php echo $influencer['id']; ?>" class="text-decoration-none">
                                                            <strong><?php echo htmlspecialchars($influencer['first_name'] . ' ' . $influencer['last_name']); ?></strong>
                                                        </a>
                                                        <?php if ($influencer['email']): ?>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars($influencer['email']); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($influencer['city']); ?></td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo ucfirst($influencer['work_type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo $influencer['campaign_count'] ?: 0; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    <?php echo formatCurrency($influencer['total_revenue'] ?: 0, 'TRY'); ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <?php if ($influencer['avg_rating']): ?>
                                                    <div class="d-flex align-items-center">
                                                        <div class="text-warning me-2">
                                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                                <i class="fas fa-star<?php echo $i <= round($influencer['avg_rating']) ? '' : '-o'; ?>"></i>
                                                            <?php endfor; ?>
                                                        </div>
                                                        <small class="text-muted">(<?php echo number_format($influencer['avg_rating'], 1); ?>)</small>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($influencer['last_campaign']): ?>
                                                    <small><?php echo date('d.m.Y', strtotime($influencer['last_campaign'])); ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo STATUS_COLORS[$influencer['status']] ?? 'secondary'; ?>">
                                                    <?php echo ucfirst($influencer['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-star fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Bu dönemde aktif influencer bulunamadı</h5>
                            <p class="text-muted">Farklı bir tarih aralığı seçin.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<?php elseif ($report_type === 'financial'): ?>
    <!-- Financial Report -->
    <div class="row mb-4">
        <!-- Monthly Revenue Chart -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Aylık Gelir Trendi</h6>
                </div>
                <div class="card-body">
                    <canvas id="financialMonthlyChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Platform Revenue -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Platform Bazlı Gelir</h6>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportToCSV('platform-revenue')">
                        <i class="fas fa-download me-2"></i>CSV
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!empty($financial_platform)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Platform</th>
                                        <th>Kampanya</th>
                                        <th>Toplam Gelir</th>
                                        <th>Ortalama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($financial_platform as $platform): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="<?php echo PLATFORMS[$platform['platform']]['icon'] ?? 'fas fa-globe'; ?> me-2"
                                                       style="color: <?php echo PLATFORMS[$platform['platform']]['color'] ?? '#6c757d'; ?>"></i>
                                                    <?php echo PLATFORMS[$platform['platform']]['name'] ?? ucfirst($platform['platform']); ?>
                                                </div>
                                            </td>
                                            <td><?php echo $platform['campaigns']; ?></td>
                                            <td>
                                                <strong class="text-success">
                                                    <?php echo formatCurrency($platform['revenue'], 'TRY'); ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo formatCurrency($platform['avg_price'], 'TRY'); ?>
                                                </small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">Bu dönemde gelir bulunamadı.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Content Type Revenue -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">İçerik Türü Bazlı Gelir</h6>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportToCSV('content-revenue')">
                        <i class="fas fa-download me-2"></i>CSV
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!empty($financial_content)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>İçerik Türü</th>
                                        <th>Kampanya</th>
                                        <th>Toplam Gelir</th>
                                        <th>Ortalama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($financial_content as $content): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo ucfirst($content['content_type']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo $content['campaigns']; ?></td>
                                            <td>
                                                <strong class="text-success">
                                                    <?php echo formatCurrency($content['revenue'], 'TRY'); ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo formatCurrency($content['avg_price'], 'TRY'); ?>
                                                </small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">Bu dönemde gelir bulunamadı.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Summary -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Finansal Özet</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($financial_monthly)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Ay</th>
                                        <th>Kampanya Sayısı</th>
                                        <th>Toplam Gelir</th>
                                        <th>Ortalama Kampanya Değeri</th>
                                        <th>Büyüme Oranı</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $prev_revenue = 0;
                                    foreach ($financial_monthly as $month):
                                        $growth_rate = $prev_revenue > 0 ? (($month['revenue'] - $prev_revenue) / $prev_revenue) * 100 : 0;
                                    ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo date('F Y', strtotime($month['month'] . '-01')); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo $month['campaigns']; ?></span>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    <?php echo formatCurrency($month['revenue'], 'TRY'); ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <?php echo formatCurrency($month['avg_price'], 'TRY'); ?>
                                            </td>
                                            <td>
                                                <?php if ($prev_revenue > 0): ?>
                                                    <span class="badge bg-<?php echo $growth_rate >= 0 ? 'success' : 'danger'; ?>">
                                                        <?php echo ($growth_rate >= 0 ? '+' : '') . number_format($growth_rate, 1); ?>%
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php
                                        $prev_revenue = $month['revenue'];
                                    endforeach;
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Bu dönemde finansal veri bulunamadı</h5>
                            <p class="text-muted">Farklı bir tarih aralığı seçin.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<?php endif; ?>

<!-- Date Range Modal -->
<div class="modal fade" id="dateRangeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tarih Aralığı Seç</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="GET">
                <div class="modal-body">
                    <input type="hidden" name="type" value="<?php echo $report_type; ?>">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" name="start_date" 
                                   value="<?php echo $start_date; ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">Bitiş Tarihi</label>
                            <input type="date" class="form-control" name="end_date" 
                                   value="<?php echo $end_date; ?>" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Hızlı Seçim</label>
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="setDateRange(7)">Son 7 Gün</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setDateRange(30)">Son 30 Gün</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setDateRange(90)">Son 3 Ay</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setDateRange(365)">Son 1 Yıl</button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Uygula</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Page specific scripts
$page_scripts = '
<script>
// Chart data from PHP
const campaignStatusData = ' . json_encode($campaign_status ?? []) . ';
const platformData = ' . json_encode($platform_stats ?? []) . ';
const monthlyTrendData = ' . json_encode($monthly_trend ?? []) . ';
const financialMonthlyData = ' . json_encode($financial_monthly ?? []) . ';
const financialPlatformData = ' . json_encode($financial_platform ?? []) . ';
const financialContentData = ' . json_encode($financial_content ?? []) . ';

// Date range functions
function setDateRange(days) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);
    
    document.querySelector("input[name=\'start_date\']").value = startDate.toISOString().split("T")[0];
    document.querySelector("input[name=\'end_date\']").value = endDate.toISOString().split("T")[0];
}

// Initialize charts when page loads
document.addEventListener("DOMContentLoaded", function() {
    if (typeof Chart !== "undefined") {
        initializeCharts();
    }
});

function initializeCharts() {
    // Campaign Status Chart
    if (document.getElementById("campaignStatusChart")) {
        const ctx1 = document.getElementById("campaignStatusChart").getContext("2d");
        new Chart(ctx1, {
            type: "doughnut",
            data: {
                labels: campaignStatusData.map(item => item.status),
                datasets: [{
                    data: campaignStatusData.map(item => item.count),
                    backgroundColor: ["#28a745", "#ffc107", "#dc3545", "#6c757d", "#17a2b8"]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }
    
    // Platform Chart
    if (document.getElementById("platformChart")) {
        const ctx2 = document.getElementById("platformChart").getContext("2d");
        new Chart(ctx2, {
            type: "bar",
            data: {
                labels: platformData.map(item => item.platform),
                datasets: [{
                    label: "Kampanya Sayısı",
                    data: platformData.map(item => item.count),
                    backgroundColor: "#667eea"
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }
    
    // Monthly Trend Chart
    if (document.getElementById("monthlyTrendChart")) {
        const ctx3 = document.getElementById("monthlyTrendChart").getContext("2d");
        new Chart(ctx3, {
            type: "line",
            data: {
                labels: monthlyTrendData.map(item => item.month),
                datasets: [{
                    label: "Kampanya Sayısı",
                    data: monthlyTrendData.map(item => item.campaigns),
                    borderColor: "#667eea",
                    backgroundColor: "rgba(102, 126, 234, 0.1)",
                    tension: 0.4
                }, {
                    label: "Gelir (TL)",
                    data: monthlyTrendData.map(item => item.revenue),
                    borderColor: "#28a745",
                    backgroundColor: "rgba(40, 167, 69, 0.1)",
                    tension: 0.4,
                    yAxisID: "y1"
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: "linear",
                        display: true,
                        position: "left"
                    },
                    y1: {
                        type: "linear",
                        display: true,
                        position: "right",
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });
    }

    // Financial Monthly Chart
    if (document.getElementById("financialMonthlyChart")) {
        const ctx4 = document.getElementById("financialMonthlyChart").getContext("2d");
        new Chart(ctx4, {
            type: "line",
            data: {
                labels: financialMonthlyData.map(item => item.month),
                datasets: [{
                    label: "Gelir (TL)",
                    data: financialMonthlyData.map(item => item.revenue),
                    borderColor: "#28a745",
                    backgroundColor: "rgba(40, 167, 69, 0.1)",
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat("tr-TR", {
                                    style: "currency",
                                    currency: "TRY"
                                }).format(value);
                            }
                        }
                    }
                }
            }
        });
    }
}

// CSV Export Functions
function exportToCSV(type) {
    let data = [];
    let filename = "";

    switch(type) {
        case "campaigns":
            data = getCampaignsData();
            filename = "kampanyalar_raporu.csv";
            break;
        case "influencers":
            data = getInfluencersData();
            filename = "influencer_raporu.csv";
            break;
        case "platform-revenue":
            data = getPlatformRevenueData();
            filename = "platform_gelir_raporu.csv";
            break;
        case "content-revenue":
            data = getContentRevenueData();
            filename = "icerik_gelir_raporu.csv";
            break;
    }

    if (data.length > 0) {
        downloadCSV(data, filename);
    }
}

function getCampaignsData() {
    const table = document.getElementById("campaignsTable");
    if (!table) return [];

    const data = [];
    const headers = Array.from(table.querySelectorAll("thead th")).map(th => th.textContent.trim());
    data.push(headers);

    table.querySelectorAll("tbody tr").forEach(row => {
        const rowData = Array.from(row.querySelectorAll("td")).map(td => td.textContent.trim());
        data.push(rowData);
    });

    return data;
}

function getInfluencersData() {
    const table = document.getElementById("influencersTable");
    if (!table) return [];

    const data = [];
    const headers = Array.from(table.querySelectorAll("thead th")).map(th => th.textContent.trim());
    data.push(headers);

    table.querySelectorAll("tbody tr").forEach(row => {
        const rowData = Array.from(row.querySelectorAll("td")).map(td => td.textContent.trim());
        data.push(rowData);
    });

    return data;
}

function getPlatformRevenueData() {
    return [
        ["Platform", "Kampanya Sayısı", "Toplam Gelir", "Ortalama"],
        ...financialPlatformData.map(item => [
            item.platform,
            item.campaigns,
            item.revenue,
            item.avg_price
        ])
    ];
}

function getContentRevenueData() {
    return [
        ["İçerik Türü", "Kampanya Sayısı", "Toplam Gelir", "Ortalama"],
        ...financialContentData.map(item => [
            item.content_type,
            item.campaigns,
            item.revenue,
            item.avg_price
        ])
    ];
}

function downloadCSV(data, filename) {
    const csvContent = data.map(row =>
        row.map(cell => `"${cell}"`).join(",")
    ).join("\\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", filename);
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}
</script>
';

// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
