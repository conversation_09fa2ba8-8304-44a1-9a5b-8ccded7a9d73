<!-- Escort Dashboard -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="<?php echo site_url('dashboard'); ?>" class="list-group-item list-group-item-action active">
                            <i class="fas fa-home me-2"></i>Ana <PERSON>
                        </a>
                        <a href="<?php echo site_url('dashboard/create_ad'); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-plus me-2"></i><PERSON><PERSON>
                        </a>
                        <a href="<?php echo site_url('dashboard/my_ads'); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-list me-2"></i><PERSON><PERSON><PERSON>ım
                        </a>
                        <a href="<?php echo site_url('dashboard/profile'); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-user me-2"></i>Profil
                        </a>
                        <a href="<?php echo site_url('auth/logout'); ?>" class="list-group-item list-group-item-action text-danger">
                            <i class="fas fa-sign-out-alt me-2"></i>Çıkış Yap
                        </a>
                    </div>
                </div>
                
                <!-- Profile Summary -->
                <div class="card mt-4">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-user me-2"></i>Profil Özeti
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <?php if (isset($profile) && !empty($profile->profile_photo)): ?>
                            <img src="<?php echo base_url('uploads/profiles/' . $profile->profile_photo); ?>" 
                                 class="rounded-circle mb-3" width="80" height="80" style="object-fit: cover;">
                        <?php else: ?>
                            <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                                 style="width: 80px; height: 80px;">
                                <i class="fas fa-user fa-2x text-muted"></i>
                            </div>
                        <?php endif; ?>
                        
                        <h6><?php echo isset($profile) ? htmlspecialchars($profile->display_name) : 'Profil Tamamlanmamış'; ?></h6>
                        <p class="text-muted small mb-2">
                            <?php echo isset($profile) && $profile->city_name ? htmlspecialchars($profile->city_name) : 'Şehir belirtilmemiş'; ?>
                        </p>
                        
                        <?php if (isset($profile) && $profile->is_verified): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Doğrulanmış
                            </span>
                        <?php else: ?>
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-clock me-1"></i>Doğrulama Bekliyor
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-lg-9">
                <!-- Welcome Card -->
                <div class="card mb-4 bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h4 class="mb-2">
                                    Hoş geldiniz, <?php echo htmlspecialchars($user->username); ?>!
                                </h4>
                                <p class="mb-0">
                                    Dashboard'unuzdan ilanlarınızı yönetebilir, profilinizi güncelleyebilirsiniz.
                                </p>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-heart fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card text-center border-primary">
                            <div class="card-body">
                                <i class="fas fa-list fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary"><?php echo isset($stats['total_ads']) ? $stats['total_ads'] : 0; ?></h4>
                                <p class="text-muted mb-0">Toplam İlan</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card text-center border-success">
                            <div class="card-body">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h4 class="text-success"><?php echo isset($stats['active_ads']) ? $stats['active_ads'] : 0; ?></h4>
                                <p class="text-muted mb-0">Aktif İlan</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card text-center border-info">
                            <div class="card-body">
                                <i class="fas fa-eye fa-2x text-info mb-2"></i>
                                <h4 class="text-info"><?php echo isset($stats['total_views']) ? number_format($stats['total_views']) : 0; ?></h4>
                                <p class="text-muted mb-0">Toplam Görüntülenme</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card text-center border-warning">
                            <div class="card-body">
                                <i class="fas fa-comments fa-2x text-warning mb-2"></i>
                                <h4 class="text-warning"><?php echo isset($stats['messages']) ? $stats['messages'] : 0; ?></h4>
                                <p class="text-muted mb-0">Mesaj</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Hızlı İşlemler
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <a href="<?php echo site_url('dashboard/create_ad'); ?>" class="btn btn-success btn-lg w-100">
                                    <i class="fas fa-plus me-2"></i>Yeni İlan Oluştur
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="<?php echo site_url('dashboard/profile'); ?>" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-user-edit me-2"></i>Profili Düzenle
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Ads -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>Son İlanlarım
                        </h5>
                        <a href="<?php echo site_url('dashboard/my_ads'); ?>" class="btn btn-outline-primary btn-sm">
                            Tümünü Gör
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (isset($ads) && !empty($ads)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>İlan Başlığı</th>
                                            <th>Şehir</th>
                                            <th>Durum</th>
                                            <th>Görüntülenme</th>
                                            <th>Tarih</th>
                                            <th>İşlemler</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($ads as $ad): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo character_limiter(htmlspecialchars($ad->title), 30); ?></strong>
                                                </td>
                                                <td><?php echo htmlspecialchars($ad->city_name); ?></td>
                                                <td>
                                                    <?php if ($ad->status === 'active'): ?>
                                                        <span class="badge bg-success">Aktif</span>
                                                    <?php elseif ($ad->status === 'pending'): ?>
                                                        <span class="badge bg-warning text-dark">Onay Bekliyor</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Reddedildi</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo number_format($ad->view_count); ?></td>
                                                <td><?php echo date('d.m.Y', strtotime($ad->created_at)); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <?php if ($ad->status === 'active'): ?>
                                                            <a href="<?php echo site_url('home/ad_detail/' . $ad->id); ?>" 
                                                               class="btn btn-outline-primary" target="_blank">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="<?php echo site_url('dashboard/edit_ad/' . $ad->id); ?>" 
                                                           class="btn btn-outline-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-plus-circle fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Henüz ilan oluşturmadınız</h5>
                                <p class="text-muted">İlk ilanınızı oluşturmak için aşağıdaki butona tıklayın.</p>
                                <a href="<?php echo site_url('dashboard/create_ad'); ?>" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>İlk İlanımı Oluştur
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.list-group-item.active {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.card {
    border-radius: 15px;
    overflow: hidden;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}
</style>
