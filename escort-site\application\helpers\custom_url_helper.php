<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Custom URL Helper
 * 
 * URL oluşturma için yardımcı fonksiyonlar
 */

if (!function_exists('site_url_custom')) {
    /**
     * Site URL oluştur
     * 
     * @param string $uri
     * @return string
     */
    function site_url_custom($uri = '') {
        $CI =& get_instance();
        
        // Eğer mod_rewrite aktifse index.php'siz, değilse index.php'li URL döndür
        $base = $CI->config->item('base_url');
        $index = $CI->config->item('index_page');
        
        if (empty($uri)) {
            return $base;
        }
        
        if (!empty($index)) {
            return $base . $index . '/' . ltrim($uri, '/');
        } else {
            return $base . ltrim($uri, '/');
        }
    }
}

if (!function_exists('home_url')) {
    /**
     * Home controller URL'leri için kısayol
     * 
     * @param string $method
     * @param string $param
     * @return string
     */
    function home_url($method = '', $param = '') {
        $uri = 'home';
        
        if (!empty($method)) {
            $uri .= '/' . $method;
        }
        
        if (!empty($param)) {
            $uri .= '/' . $param;
        }
        
        return site_url_custom($uri);
    }
}

if (!function_exists('asset_url')) {
    /**
     * Asset URL oluştur
     * 
     * @param string $path
     * @return string
     */
    function asset_url($path = '') {
        $CI =& get_instance();
        $base = $CI->config->item('base_url');
        return $base . 'assets/' . ltrim($path, '/');
    }
}

if (!function_exists('upload_url')) {
    /**
     * Upload URL oluştur
     * 
     * @param string $path
     * @return string
     */
    function upload_url($path = '') {
        $CI =& get_instance();
        $base = $CI->config->item('base_url');
        return $base . 'uploads/' . ltrim($path, '/');
    }
}
