<!-- Login Section -->
<section class="py-5 bg-light min-vh-100 d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h2 class="mb-0">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Giriş Yap
                        </h2>
                        <p class="mb-0 mt-2">Hesabınıza giriş yapın</p>
                    </div>
                    
                    <div class="card-body p-5">
                        <?php echo validation_errors('<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>', '</div>'); ?>

                        <form action="<?php echo site_url('auth/login'); ?>" method="POST" class="needs-validation" novalidate>
                            <div class="mb-4">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>E-posta Adresi
                                </label>
                                <input type="email" class="form-control form-control-lg" id="email" name="email" 
                                       value="<?php echo set_value('email'); ?>" required>
                                <div class="invalid-feedback">
                                    Lütfen geçerli bir e-posta adresi girin.
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Şifre
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-lg" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Lütfen şifrenizi girin.
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="remember" name="remember" value="1">
                                    <label class="form-check-label" for="remember">
                                        Beni hatırla
                                    </label>
                                </div>
                            </div>
                            
                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Giriş Yap
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center">
                            <a href="<?php echo site_url('auth/forgot_password'); ?>" class="text-decoration-none">
                                <i class="fas fa-key me-1"></i>Şifremi Unuttum
                            </a>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-light text-center py-3">
                        <p class="mb-0">
                            Hesabınız yok mu? 
                            <a href="<?php echo site_url('auth/register'); ?>" class="text-primary fw-bold text-decoration-none">
                                Kayıt Ol
                            </a>
                        </p>
                    </div>
                </div>
                
                <!-- Demo Accounts Info -->
                <div class="card mt-4 border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Demo Hesapları
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Test Escort:</strong><br>
                                <small><EMAIL></small><br>
                                <small>test123</small>
                            </div>
                            <div class="col-md-6">
                                <strong>Test Müşteri:</strong><br>
                                <small><EMAIL></small><br>
                                <small>test123</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.min-vh-100 {
    min-height: 100vh;
}

.card {
    border-radius: 15px;
    overflow: hidden;
}

.form-control-lg {
    border-radius: 10px;
    padding: 15px;
}

.btn-lg {
    border-radius: 10px;
    padding: 15px;
    font-weight: 600;
}

.input-group .btn {
    border-radius: 0 10px 10px 0;
}

.form-check-input:checked {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

@media (max-width: 768px) {
    .card-body {
        padding: 2rem !important;
    }
}
</style>

<script>
$(document).ready(function() {
    // Password toggle
    $('#togglePassword').click(function() {
        const password = $('#password');
        const icon = $(this).find('i');
        
        if (password.attr('type') === 'password') {
            password.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            password.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Form validation
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });
    
    // Demo account quick fill
    $('.demo-fill').click(function(e) {
        e.preventDefault();
        const email = $(this).data('email');
        const password = $(this).data('password');
        
        $('#email').val(email);
        $('#password').val(password);
    });
});
</script>
