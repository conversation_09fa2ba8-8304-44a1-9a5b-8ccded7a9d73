<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Kategori Model
 * 
 * Kategori işlemleri için model sınıfı
 * 
 * @package EscortSite
 * <AUTHOR> Site Developer
 * @version 1.0
 */
class Category_model extends CI_Model {

    protected $table = 'categories';

    public function __construct() {
        parent::__construct();
    }

    /**
     * Aktif kategorileri getir
     */
    public function get_active_categories() {
        $this->db->where('is_active', 1);
        $this->db->order_by('sort_order', 'ASC');
        $this->db->order_by('name', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Kategoriyi slug ile getir
     */
    public function get_category_by_slug($slug) {
        $this->db->where('slug', $slug);
        $this->db->where('is_active', 1);
        return $this->db->get($this->table)->row();
    }

    /**
     * Kategoriyi ID ile getir
     */
    public function get_category_by_id($id) {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }

    /**
     * Tüm kategorileri getir (admin için)
     */
    public function get_all_categories() {
        $this->db->order_by('sort_order', 'ASC');
        $this->db->order_by('name', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Kategori oluştur
     */
    public function create_category($data) {
        return $this->db->insert($this->table, $data);
    }

    /**
     * Kategori güncelle
     */
    public function update_category($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Kategori sil
     */
    public function delete_category($id) {
        $this->db->where('id', $id);
        return $this->db->delete($this->table);
    }

    /**
     * Slug kontrolü
     */
    public function check_slug_exists($slug, $exclude_id = null) {
        $this->db->where('slug', $slug);
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        return $this->db->count_all_results($this->table) > 0;
    }

    /**
     * Kategori istatistikleri
     */
    public function get_category_stats($category_id) {
        // İlan sayısı
        $this->db->where('category_id', $category_id);
        $this->db->where('status', 'active');
        $ad_count = $this->db->count_all_results('ads');

        return [
            'ad_count' => $ad_count
        ];
    }
}
