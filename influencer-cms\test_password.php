<?php
/**
 * Password Test - Admin ş<PERSON> test et
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

echo "<h1>Password Test</h1>";

// Test password
$test_password = 'admin123';
$stored_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

echo "Test Password: " . $test_password . "<br>";
echo "Stored Hash: " . $stored_hash . "<br>";
echo "Password Verify: " . (password_verify($test_password, $stored_hash) ? 'TRUE' : 'FALSE') . "<br>";

// Generate new hash
$new_hash = password_hash($test_password, PASSWORD_DEFAULT);
echo "New Hash: " . $new_hash . "<br>";
echo "New Hash Verify: " . (password_verify($test_password, $new_hash) ? 'TRUE' : 'FALSE') . "<br>";

// Database test
try {
    global $database;
    $pdo = $database->connect();
    
    echo "<h2>Database User Check:</h2>";
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    $user = $stmt->fetch();
    
    if ($user) {
        echo "User found: " . $user['username'] . "<br>";
        echo "Email: " . $user['email'] . "<br>";
        echo "Role: " . $user['role'] . "<br>";
        echo "Status: " . $user['status'] . "<br>";
        echo "Password Hash: " . $user['password'] . "<br>";
        echo "Password Verify: " . (password_verify($test_password, $user['password']) ? 'TRUE' : 'FALSE') . "<br>";
        
        // Update password if needed
        if (!password_verify($test_password, $user['password'])) {
            echo "<h3>Updating password...</h3>";
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = 'admin'");
            $stmt->execute([$new_hash]);
            echo "Password updated!<br>";
        }
    } else {
        echo "User not found!<br>";
        
        // Create admin user
        echo "<h3>Creating admin user...</h3>";
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, full_name, role, status) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $new_hash, 'System Administrator', 'admin', 'active']);
        echo "Admin user created!<br>";
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='login.php'>Go to Login</a>";
?>
