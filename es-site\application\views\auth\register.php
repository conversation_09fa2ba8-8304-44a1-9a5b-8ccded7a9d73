<!-- Register Section -->
<section class="py-5 bg-light min-vh-100">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-success text-white text-center py-4">
                        <h2 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            Kayıt Ol
                        </h2>
                        <p class="mb-0 mt-2"><PERSON><PERSON> hesap olu<PERSON></p>
                    </div>
                    
                    <div class="card-body p-5">
                        <?php echo validation_errors('<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>', '</div>'); ?>

                        <form action="<?php echo site_url('auth/register'); ?>" method="POST" class="needs-validation" novalidate>
                            <!-- User Type Selection -->
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-users me-1"></i>Hesap Tipi <span class="text-danger">*</span>
                                </label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card user-type-card" data-type="escort">
                                            <div class="card-body text-center">
                                                <i class="fas fa-user fa-2x text-primary mb-2"></i>
                                                <h6>Escort</h6>
                                                <small class="text-muted">İlan verebilir, profil oluşturabilir</small>
                                                <input type="radio" name="role" value="escort" class="form-check-input d-none" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card user-type-card" data-type="customer">
                                            <div class="card-body text-center">
                                                <i class="fas fa-search fa-2x text-success mb-2"></i>
                                                <h6>Müşteri</h6>
                                                <small class="text-muted">İlan arayabilir, mesaj gönderebilir</small>
                                                <input type="radio" name="role" value="customer" class="form-check-input d-none" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-1"></i>Kullanıcı Adı <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo set_value('username'); ?>" required>
                                    <div class="invalid-feedback">
                                        Lütfen kullanıcı adınızı girin.
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>E-posta <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo set_value('email'); ?>" required>
                                    <div class="invalid-feedback">
                                        Lütfen geçerli bir e-posta adresi girin.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Şifre <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        Şifre en az 6 karakter olmalıdır.
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="password_confirm" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Şifre Tekrar <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" class="form-control" id="password_confirm" name="password_confirm" required>
                                    <div class="invalid-feedback">
                                        Şifreler eşleşmiyor.
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Age Confirmation -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="age_confirm" name="age_confirm" value="1" required>
                                    <label class="form-check-label" for="age_confirm">
                                        18 yaşından büyük olduğumu beyan ederim <span class="text-danger">*</span>
                                    </label>
                                    <div class="invalid-feedback">
                                        Bu onayı vermelisiniz.
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Terms -->
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="terms" name="terms" value="1" required>
                                    <label class="form-check-label" for="terms">
                                        <a href="<?php echo site_url('home/terms'); ?>" target="_blank">Kullanım Şartları</a>'nı ve 
                                        <a href="<?php echo site_url('home/privacy'); ?>" target="_blank">Gizlilik Politikası</a>'nı okudum ve kabul ediyorum <span class="text-danger">*</span>
                                    </label>
                                    <div class="invalid-feedback">
                                        Kullanım şartlarını kabul etmelisiniz.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-user-plus me-2"></i>
                                    Kayıt Ol
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="card-footer bg-light text-center py-3">
                        <p class="mb-0">
                            Zaten hesabınız var mı? 
                            <a href="<?php echo site_url('auth/login'); ?>" class="text-primary fw-bold text-decoration-none">
                                Giriş Yap
                            </a>
                        </p>
                    </div>
                </div>
                
                <!-- Security Info -->
                <div class="card mt-4 border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Güvenlik ve Gizlilik
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><i class="fas fa-check text-success me-2"></i>Tüm verileriniz SSL ile şifrelenir</li>
                            <li><i class="fas fa-check text-success me-2"></i>Kişisel bilgileriniz gizli tutulur</li>
                            <li><i class="fas fa-check text-success me-2"></i>E-posta doğrulama gereklidir</li>
                            <li><i class="fas fa-check text-success me-2"></i>Spam ve sahte hesaplar engellenir</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.user-type-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.user-type-card:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.user-type-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.user-type-card.selected i {
    color: #007bff !important;
}

.form-control {
    border-radius: 8px;
}

.btn-lg {
    border-radius: 8px;
    font-weight: 600;
}

.input-group .btn {
    border-radius: 0 8px 8px 0;
}

@media (max-width: 768px) {
    .card-body {
        padding: 2rem !important;
    }
    
    .user-type-card {
        margin-bottom: 1rem;
    }
}
</style>

<script>
$(document).ready(function() {
    // User type selection
    $('.user-type-card').click(function() {
        $('.user-type-card').removeClass('selected');
        $(this).addClass('selected');
        $(this).find('input[type="radio"]').prop('checked', true);
    });
    
    // Password toggle
    $('#togglePassword').click(function() {
        const password = $('#password');
        const icon = $(this).find('i');
        
        if (password.attr('type') === 'password') {
            password.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            password.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Password confirmation validation
    $('#password_confirm').on('input', function() {
        const password = $('#password').val();
        const confirm = $(this).val();
        
        if (password !== confirm) {
            this.setCustomValidity('Şifreler eşleşmiyor');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // Form validation
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });
    
    // Username availability check
    let usernameTimeout;
    $('#username').on('input', function() {
        const username = $(this).val();
        
        clearTimeout(usernameTimeout);
        
        if (username.length >= 3) {
            usernameTimeout = setTimeout(() => {
                checkUsernameAvailability(username);
            }, 500);
        }
    });
    
    function checkUsernameAvailability(username) {
        // AJAX call to check username availability
        // This will be implemented when we create the API endpoints
    }
});
</script>
