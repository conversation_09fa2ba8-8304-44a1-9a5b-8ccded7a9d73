/**
 * Influencer CMS - Main JavaScript File
 * Ana JavaScript dosyası
 */

// Global variables
window.InfluencerCMS = {
    baseUrl: 'http://localhost/influencer-cms',
    currentUser: null,
    notifications: [],
    
    // Initialize application
    init: function() {
        this.initBootstrap();
        this.initTooltips();
        this.initConfirmDialogs();
        this.initFormValidation();
        this.initSearchFeatures();
        this.initNotifications();
        this.initSidebar();
        this.initDataTables();
        this.initCharts();
        this.initXIconFallback();

        console.log('Influencer CMS initialized successfully');
    },
    
    // Initialize Bootstrap components
    initBootstrap: function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    },
    
    // Initialize tooltips
    initTooltips: function() {
        $('[data-bs-toggle="tooltip"]').tooltip();
    },
    
    // Initialize confirm dialogs
    initConfirmDialogs: function() {
        window.confirmDelete = function(message) {
            return confirm(message || 'Bu işlemi gerçekleştirmek istediğinizden emin misiniz?');
        };
        
        window.confirmAction = function(message) {
            return confirm(message || 'Bu işlemi onaylıyor musunuz?');
        };
    },
    
    // Initialize form validation
    initFormValidation: function() {
        // Bootstrap form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // Custom validation rules
        this.addCustomValidationRules();
    },
    
    // Add custom validation rules
    addCustomValidationRules: function() {
        // Turkish phone number validation
        $.validator && $.validator.addMethod("turkishPhone", function(value, element) {
            return this.optional(element) || /^(\+90|0)?[0-9]{10}$/.test(value.replace(/\s/g, ''));
        }, "Geçerli bir Türkiye telefon numarası girin.");
        
        // URL validation
        $.validator && $.validator.addMethod("url", function(value, element) {
            return this.optional(element) || /^https?:\/\/.+/.test(value);
        }, "Geçerli bir URL girin (http:// veya https:// ile başlamalı).");
    },
    
    // Initialize search features
    initSearchFeatures: function() {
        // Global search shortcut (Ctrl+K)
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                var searchInput = document.querySelector('input[name="q"], input[name="search"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });
        
        // Live search functionality
        this.initLiveSearch();
    },
    
    // Initialize live search
    initLiveSearch: function() {
        var searchInputs = document.querySelectorAll('.live-search');
        searchInputs.forEach(function(input) {
            var timeout;
            input.addEventListener('input', function() {
                clearTimeout(timeout);
                timeout = setTimeout(function() {
                    // Implement live search logic here
                    console.log('Live search:', input.value);
                }, 300);
            });
        });
    },
    
    // Initialize notifications
    initNotifications: function() {
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert:not(.alert-permanent)').fadeOut();
        }, 5000);
        
        // Check for new notifications periodically
        this.checkNotifications();
        setInterval(this.checkNotifications.bind(this), 30000); // Every 30 seconds
    },
    
    // Check for new notifications
    checkNotifications: function() {
        // Implement notification checking logic
        // This would typically make an AJAX call to check for new notifications
    },
    
    // Initialize sidebar
    initSidebar: function() {
        // Mobile sidebar toggle
        var sidebarToggle = document.querySelector('.sidebar-toggle');
        var sidebar = document.querySelector('.sidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });
        }
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
    },
    
    // Initialize DataTables
    initDataTables: function() {
        if (typeof $.fn.DataTable !== 'undefined') {
            $('.data-table').DataTable({
                responsive: true,
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/tr.json'
                },
                pageLength: 25,
                order: [[0, 'desc']]
            });
        }
    },
    
    // Initialize charts
    initCharts: function() {
        // Chart.js initialization would go here
        if (typeof Chart !== 'undefined') {
            Chart.defaults.font.family = 'Inter, sans-serif';
            Chart.defaults.color = '#6c757d';
        }
    },

    // Initialize X (Twitter) icon fallback
    initXIconFallback: function() {
        // Check if Font Awesome X-Twitter icon is available
        setTimeout(function() {
            const xIcons = document.querySelectorAll('.fa-x-twitter');
            xIcons.forEach(function(icon) {
                // Test if the icon is properly loaded
                const computedStyle = window.getComputedStyle(icon, ':before');
                const content = computedStyle.getPropertyValue('content');

                // If Font Awesome icon is not loaded properly, add fallback
                if (!content || content === 'none' || content === '""') {
                    icon.classList.add('fallback');
                    icon.style.fontFamily = 'Arial, sans-serif';
                    icon.style.fontWeight = '900';
                }
            });
        }, 100);
    },
    
    // Utility functions
    utils: {
        // Format number with thousand separators
        formatNumber: function(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        },
        
        // Format currency
        formatCurrency: function(amount, currency) {
            currency = currency || 'TRY';
            var symbols = {
                'TRY': '₺',
                'USD': '$',
                'EUR': '€'
            };
            return this.formatNumber(amount) + ' ' + (symbols[currency] || currency);
        },
        
        // Format date
        formatDate: function(date, format) {
            format = format || 'dd.mm.yyyy';
            var d = new Date(date);
            var day = ('0' + d.getDate()).slice(-2);
            var month = ('0' + (d.getMonth() + 1)).slice(-2);
            var year = d.getFullYear();
            
            return format.replace('dd', day).replace('mm', month).replace('yyyy', year);
        },
        
        // Show loading spinner
        showLoading: function(element) {
            if (typeof element === 'string') {
                element = document.querySelector(element);
            }
            if (element) {
                element.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Yükleniyor...</span></div></div>';
            }
        },
        
        // Hide loading spinner
        hideLoading: function(element) {
            if (typeof element === 'string') {
                element = document.querySelector(element);
            }
            if (element) {
                element.innerHTML = '';
            }
        },
        
        // Show toast notification
        showToast: function(message, type) {
            type = type || 'info';
            var toastHtml = `
                <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;
            
            var toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }
            
            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            var toast = new bootstrap.Toast(toastContainer.lastElementChild);
            toast.show();
        },
        
        // AJAX helper
        ajax: function(url, options) {
            options = options || {};
            options.method = options.method || 'GET';
            options.headers = options.headers || {};
            options.headers['X-Requested-With'] = 'XMLHttpRequest';
            
            return fetch(url, options)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('AJAX Error:', error);
                    this.showToast('Bir hata oluştu: ' + error.message, 'danger');
                    throw error;
                });
        }
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    InfluencerCMS.init();
});

// jQuery ready function for backward compatibility
$(document).ready(function() {
    // Additional jQuery-specific initialization
    
    // Auto-submit forms with .auto-submit class when select changes
    $('.auto-submit').on('change', 'select', function() {
        $(this).closest('form').submit();
    });
    
    // Confirm before form submission for .confirm-submit forms
    $('.confirm-submit').on('submit', function(e) {
        if (!confirm('Bu işlemi gerçekleştirmek istediğinizden emin misiniz?')) {
            e.preventDefault();
            return false;
        }
    });
    
    // Auto-resize textareas
    $('textarea.auto-resize').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Copy to clipboard functionality
    $('.copy-to-clipboard').on('click', function() {
        var text = $(this).data('text') || $(this).text();
        navigator.clipboard.writeText(text).then(function() {
            InfluencerCMS.utils.showToast('Panoya kopyalandı!', 'success');
        });
    });
});

// Export for use in other files
window.CMS = InfluencerCMS;
