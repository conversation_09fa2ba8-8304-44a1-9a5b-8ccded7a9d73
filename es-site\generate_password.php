<?php
// Şifre hash'i oluşturma scripti
$password = 'test123';
$hash = password_hash($password, PASSWORD_DEFAULT);

echo "<h2>Şifre Hash Oluşturucu</h2>";
echo "<p><strong>Şifre:</strong> " . $password . "</p>";
echo "<p><strong>Hash:</strong> " . $hash . "</p>";

// Test et
if (password_verify($password, $hash)) {
    echo "<p style='color: green;'><strong>✅ Hash doğru çalışıyor!</strong></p>";
} else {
    echo "<p style='color: red;'><strong>❌ Hash çalışmıyor!</strong></p>";
}

// SQL için hazır kod
echo "<h3>SQL için hazır kod:</h3>";
echo "<textarea style='width: 100%; height: 200px;'>";
echo "-- <PERSON> k<PERSON> (Şifre: test123)\n";
echo "INSERT INTO `users` (`username`, `email`, `password`, `role`, `status`, `email_verified`, `created_at`) VALUES\n";
echo "('test_escort', '<EMAIL>', '" . $hash . "', 'escort', 'active', 1, NOW()),\n";
echo "('test_customer', '<EMAIL>', '" . $hash . "', 'customer', 'active', 1, NOW()),\n";
echo "('admin_user', '<EMAIL>', '" . $hash . "', 'admin', 'active', 1, NOW());";
echo "</textarea>";
?>
