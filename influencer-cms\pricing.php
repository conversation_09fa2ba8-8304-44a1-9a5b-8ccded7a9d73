<?php
/**
 * Pricing Management
 * Influencer fiyatlandırma yönetimi
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Check permissions
requirePermission('influencers_view');

// Page settings
$page_title = 'Fiyatlandırma Yönetimi';
$action = $_GET['action'] ?? 'list';
$influencer_id = $_GET['influencer_id'] ?? null;
$pricing_id = $_GET['id'] ?? null;

// Content types
$content_types = [
    'story' => 'Story Paylaşımı',
    'post' => 'Post Paylaşımı', 
    'reels' => 'Reels/Video',
    'video' => 'Video İçerik',
    'live' => 'Canlı Yayın',
    'package' => 'Paket Hizmet'
];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        global $database;
        $pdo = $database->connect();
        
        if ($action === 'add' && hasPermission('influencers_manage')) {
            // Add new pricing
            $stmt = $pdo->prepare("
                INSERT INTO influencer_pricing (influencer_id, platform, content_type, price, 
                                               currency, negotiable, package_details, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $_POST['influencer_id'],
                $_POST['platform'],
                $_POST['content_type'],
                floatval($_POST['price']),
                $_POST['currency'] ?? 'TRY',
                isset($_POST['negotiable']) ? 1 : 0,
                sanitizeInput($_POST['package_details']),
                sanitizeInput($_POST['notes'])
            ]);
            
            $_SESSION['success_message'] = 'Fiyatlandırma başarıyla eklendi.';
            redirectTo('pricing.php?influencer_id=' . $_POST['influencer_id']);
            
        } elseif ($action === 'edit' && hasPermission('influencers_manage')) {
            // Update pricing
            $stmt = $pdo->prepare("
                UPDATE influencer_pricing 
                SET price = ?, currency = ?, negotiable = ?, package_details = ?, notes = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                floatval($_POST['price']),
                $_POST['currency'] ?? 'TRY',
                isset($_POST['negotiable']) ? 1 : 0,
                sanitizeInput($_POST['package_details']),
                sanitizeInput($_POST['notes']),
                $pricing_id
            ]);
            
            $_SESSION['success_message'] = 'Fiyatlandırma güncellendi.';
            redirectTo('pricing.php?influencer_id=' . $_POST['influencer_id']);
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'İşlem sırasında bir hata oluştu: ' . $e->getMessage();
        error_log('Pricing operation error: ' . $e->getMessage());
    }
}

// Handle delete action
if ($action === 'delete' && $pricing_id && hasPermission('influencers_manage')) {
    try {
        global $database;
        $pdo = $database->connect();
        
        $stmt = $pdo->prepare("DELETE FROM influencer_pricing WHERE id = ?");
        $stmt->execute([$pricing_id]);
        
        $_SESSION['success_message'] = 'Fiyatlandırma silindi.';
        redirectTo('pricing.php?influencer_id=' . $influencer_id);
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Silme işlemi sırasında bir hata oluştu.';
        error_log('Pricing delete error: ' . $e->getMessage());
    }
}

// Get data
if ($influencer_id) {
    try {
        global $database;
        $pdo = $database->connect();
        
        // Get influencer info
        $stmt = $pdo->prepare("SELECT * FROM influencers WHERE id = ?");
        $stmt->execute([$influencer_id]);
        $influencer = $stmt->fetch();
        
        if (!$influencer) {
            $_SESSION['error_message'] = 'Influencer bulunamadı.';
            redirectTo('influencers.php');
        }
        
        // Get pricing
        $stmt = $pdo->prepare("SELECT * FROM influencer_pricing WHERE influencer_id = ? ORDER BY platform, content_type");
        $stmt->execute([$influencer_id]);
        $pricing_list = $stmt->fetchAll();
        
        // Get available platforms for this influencer
        $stmt = $pdo->prepare("SELECT DISTINCT platform FROM influencer_platforms WHERE influencer_id = ?");
        $stmt->execute([$influencer_id]);
        $available_platforms = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Get pricing for editing
        if ($action === 'edit' && $pricing_id) {
            $stmt = $pdo->prepare("SELECT * FROM influencer_pricing WHERE id = ? AND influencer_id = ?");
            $stmt->execute([$pricing_id, $influencer_id]);
            $pricing = $stmt->fetch();
            
            if (!$pricing) {
                $_SESSION['error_message'] = 'Fiyatlandırma bulunamadı.';
                redirectTo('pricing.php?influencer_id=' . $influencer_id);
            }
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Veri alınırken hata oluştu.';
        error_log('Pricing data error: ' . $e->getMessage());
        redirectTo('influencers.php');
    }
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($influencer_id && isset($influencer)): ?>
    <!-- Influencer Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <?php if ($influencer['profile_photo']): ?>
                                <img src="<?php echo UPLOAD_URL . 'profiles/' . $influencer['profile_photo']; ?>" 
                                     class="rounded-circle me-3" width="60" height="60" style="object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-dark bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center me-3" 
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-user"></i>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h4 class="mb-1"><?php echo htmlspecialchars($influencer['first_name'] . ' ' . $influencer['last_name']); ?></h4>
                                <p class="mb-0 opacity-75">Fiyatlandırma Yönetimi</p>
                            </div>
                        </div>
                        <div>
                            <a href="influencer_detail.php?id=<?php echo $influencer['id']; ?>" class="btn btn-dark">
                                <i class="fas fa-arrow-left me-2"></i>Geri Dön
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($action === 'list'): ?>
        <!-- Pricing List -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            Fiyatlandırma Listesi (<?php echo count($pricing_list); ?>)
                        </h5>
                        <?php if (hasPermission('influencers_manage')): ?>
                            <a href="pricing.php?action=add&influencer_id=<?php echo $influencer_id; ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Fiyat Ekle
                            </a>
                        <?php endif; ?>
                    </div>
                    
                    <div class="card-body">
                        <?php if (!empty($pricing_list)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Platform</th>
                                            <th>İçerik Türü</th>
                                            <th>Fiyat</th>
                                            <th>Para Birimi</th>
                                            <th>Pazarlık</th>
                                            <th>Notlar</th>
                                            <th>İşlemler</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($pricing_list as $price): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <i class="<?php echo PLATFORMS[$price['platform']]['icon'] ?? 'fas fa-globe'; ?> me-2" 
                                                           style="color: <?php echo PLATFORMS[$price['platform']]['color'] ?? '#6c757d'; ?>"></i>
                                                        <?php echo PLATFORMS[$price['platform']]['name'] ?? ucfirst($price['platform']); ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary">
                                                        <?php echo $content_types[$price['content_type']] ?? $price['content_type']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong class="text-success">
                                                        <?php echo formatCurrency($price['price'], $price['currency']); ?>
                                                    </strong>
                                                </td>
                                                <td><?php echo $price['currency']; ?></td>
                                                <td>
                                                    <?php if ($price['negotiable']): ?>
                                                        <span class="badge bg-success">Evet</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Hayır</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($price['notes']): ?>
                                                        <span data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($price['notes']); ?>">
                                                            <i class="fas fa-sticky-note text-info"></i>
                                                        </span>
                                                    <?php else: ?>
                                                        -
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (hasPermission('influencers_manage')): ?>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="pricing.php?action=edit&id=<?php echo $price['id']; ?>&influencer_id=<?php echo $influencer_id; ?>" 
                                                               class="btn btn-outline-warning" data-bs-toggle="tooltip" title="Düzenle">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="pricing.php?action=delete&id=<?php echo $price['id']; ?>&influencer_id=<?php echo $influencer_id; ?>" 
                                                               class="btn btn-outline-danger" data-bs-toggle="tooltip" title="Sil"
                                                               onclick="return confirmDelete('Bu fiyatlandırmayı silmek istediğinizden emin misiniz?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Henüz fiyatlandırma eklenmemiş</h5>
                                <p class="text-muted">Bu influencer için fiyatlandırma bilgileri ekleyin.</p>
                                <?php if (hasPermission('influencers_manage')): ?>
                                    <a href="pricing.php?action=add&influencer_id=<?php echo $influencer_id; ?>" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>İlk Fiyatı Ekle
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

    <?php elseif ($action === 'add' || $action === 'edit'): ?>
        <!-- Add/Edit Pricing Form -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                            <?php echo $action === 'add' ? 'Fiyat Ekle' : 'Fiyat Düzenle'; ?>
                        </h5>
                    </div>
                    
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="influencer_id" value="<?php echo $influencer_id; ?>">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="platform" class="form-label">Platform <span class="text-danger">*</span></label>
                                    <select class="form-select" id="platform" name="platform" required <?php echo $action === 'edit' ? 'disabled' : ''; ?>>
                                        <option value="">Platform Seçin</option>
                                        <?php foreach ($available_platforms as $platform_key): ?>
                                            <option value="<?php echo $platform_key; ?>" 
                                                    <?php echo ($action === 'edit' && $pricing['platform'] === $platform_key) ? 'selected' : ''; ?>>
                                                <?php echo PLATFORMS[$platform_key]['name'] ?? ucfirst($platform_key); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <?php if ($action === 'edit'): ?>
                                        <input type="hidden" name="platform" value="<?php echo $pricing['platform']; ?>">
                                    <?php endif; ?>
                                    <div class="invalid-feedback">Platform seçimi zorunludur.</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="content_type" class="form-label">İçerik Türü <span class="text-danger">*</span></label>
                                    <select class="form-select" id="content_type" name="content_type" required <?php echo $action === 'edit' ? 'disabled' : ''; ?>>
                                        <option value="">İçerik Türü Seçin</option>
                                        <?php foreach ($content_types as $key => $name): ?>
                                            <option value="<?php echo $key; ?>" 
                                                    <?php echo ($action === 'edit' && $pricing['content_type'] === $key) ? 'selected' : ''; ?>>
                                                <?php echo $name; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <?php if ($action === 'edit'): ?>
                                        <input type="hidden" name="content_type" value="<?php echo $pricing['content_type']; ?>">
                                    <?php endif; ?>
                                    <div class="invalid-feedback">İçerik türü seçimi zorunludur.</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="price" class="form-label">Fiyat <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="price" name="price" 
                                           value="<?php echo $pricing['price'] ?? ''; ?>" 
                                           min="0" step="0.01" required>
                                    <div class="invalid-feedback">Fiyat zorunludur.</div>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="currency" class="form-label">Para Birimi</label>
                                    <select class="form-select" id="currency" name="currency">
                                        <?php foreach (CURRENCIES as $code => $currency): ?>
                                            <option value="<?php echo $code; ?>" 
                                                    <?php echo ($pricing['currency'] ?? 'TRY') === $code ? 'selected' : ''; ?>>
                                                <?php echo $code; ?> (<?php echo $currency['symbol']; ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="package_details" class="form-label">Paket Detayları</label>
                                <textarea class="form-control" id="package_details" name="package_details" rows="3"
                                          placeholder="Paket içeriği, süre, özel koşullar..."><?php echo htmlspecialchars($pricing['package_details'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">Notlar</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2"
                                          placeholder="Özel notlar, koşullar..."><?php echo htmlspecialchars($pricing['notes'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="negotiable" name="negotiable" value="1"
                                           <?php echo ($pricing['negotiable'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="negotiable">
                                        Pazarlığa açık
                                    </label>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="d-flex justify-content-between">
                                <a href="pricing.php?influencer_id=<?php echo $influencer_id; ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Geri Dön
                                </a>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    <?php echo $action === 'add' ? 'Fiyat Ekle' : 'Değişiklikleri Kaydet'; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

<?php else: ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        Geçersiz influencer ID'si.
    </div>
<?php endif; ?>

<?php
// Page specific scripts
$page_scripts = '
<script>
// Form validation
(function() {
    "use strict";
    window.addEventListener("load", function() {
        var forms = document.getElementsByClassName("needs-validation");
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener("submit", function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add("was-validated");
            }, false);
        });
    }, false);
})();

// Suggested pricing based on platform and content type
const suggestedPrices = {
    "instagram": {
        "story": 1500,
        "post": 3000,
        "reels": 5000
    },
    "tiktok": {
        "video": 2500,
        "live": 4000
    },
    "youtube": {
        "video": 8000,
        "live": 6000
    }
};

function updateSuggestedPrice() {
    const platform = document.getElementById("platform").value;
    const contentType = document.getElementById("content_type").value;
    const priceField = document.getElementById("price");
    
    if (platform && contentType && suggestedPrices[platform] && suggestedPrices[platform][contentType]) {
        if (!priceField.value) {
            priceField.value = suggestedPrices[platform][contentType];
        }
    }
}

document.getElementById("platform")?.addEventListener("change", updateSuggestedPrice);
document.getElementById("content_type")?.addEventListener("change", updateSuggestedPrice);
</script>
';

// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
