<!-- Hero Section -->
<section class="hero-section bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center min-vh-50">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    Türkiye'nin <PERSON> Güvenilir
                    <span class="text-warning">Escort İlan</span> Sitesi
                </h1>
                <p class="lead mb-4">
                    Kaliteli, doğrulanmış ve güvenilir escort ilanları. Gizlilik ve güvenlik önceliğimizdir.
                </p>
                
                <!-- Quick Search -->
                <div class="card bg-white text-dark p-4 shadow-lg">
                    <form action="<?php echo site_url('home/search'); ?>" method="GET" class="row g-3">
                        <div class="col-md-4">
                            <select name="city" class="form-select">
                                <option value="">Şehir Seçin</option>
                                <?php if (isset($cities) && !empty($cities)): ?>
                                    <?php foreach ($cities as $city): ?>
                                        <option value="<?php echo $city->id; ?>"><?php echo $city->name; ?></option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select name="category" class="form-select">
                                <option value="">Kategori Seçin</option>
                                <?php if (isset($categories) && !empty($categories)): ?>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category->id; ?>"><?php echo $category->name; ?></option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>Ara
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <div class="hero-stats">
                    <div class="row">
                        <div class="col-4">
                            <div class="stat-item">
                                <h3 class="display-6 fw-bold text-warning"><?php echo isset($stats['total_ads']) ? number_format($stats['total_ads']) : '0'; ?></h3>
                                <p class="mb-0">Aktif İlan</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h3 class="display-6 fw-bold text-warning"><?php echo isset($stats['total_escorts']) ? number_format($stats['total_escorts']) : '0'; ?></h3>
                                <p class="mb-0">Doğrulanmış Escort</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h3 class="display-6 fw-bold text-warning"><?php echo isset($stats['total_cities']) ? number_format($stats['total_cities']) : '0'; ?></h3>
                                <p class="mb-0">Şehir</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Ads Section -->
<?php if (isset($featured_ads) && !empty($featured_ads)): ?>
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="display-5 fw-bold mb-3">
                    <i class="fas fa-star text-warning me-2"></i>
                    Öne Çıkan İlanlar
                </h2>
                <p class="lead text-muted">En kaliteli ve öne çıkan escort ilanları</p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($featured_ads as $ad): ?>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card ad-card h-100 shadow-sm">
                        <div class="position-relative">
                            <?php if (!empty($ad->profile_photo)): ?>
                                <img src="<?php echo base_url('uploads/profiles/' . $ad->profile_photo); ?>" 
                                     class="card-img-top" alt="<?php echo htmlspecialchars($ad->display_name); ?>" 
                                     style="height: 250px; object-fit: cover;">
                            <?php else: ?>
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                    <i class="fas fa-user fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="position-absolute top-0 start-0 m-2">
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star me-1"></i>Öne Çıkan
                                </span>
                            </div>
                            
                            <?php if ($ad->price): ?>
                                <div class="position-absolute top-0 end-0 m-2">
                                    <span class="badge bg-success">
                                        <?php echo number_format($ad->price); ?> ₺
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-body">
                            <h5 class="card-title"><?php echo htmlspecialchars($ad->display_name); ?></h5>
                            <p class="card-text text-muted small">
                                <i class="fas fa-map-marker-alt me-1"></i><?php echo $ad->city_name; ?>
                                <span class="ms-2">
                                    <i class="fas fa-birthday-cake me-1"></i><?php echo $ad->age; ?> yaş
                                </span>
                            </p>
                            <p class="card-text"><?php echo character_limiter(strip_tags($ad->description), 80); ?></p>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-eye me-1"></i><?php echo number_format($ad->view_count); ?> görüntülenme
                                </small>
                                <a href="<?php echo site_url('home/ad_detail/' . $ad->id); ?>" class="btn btn-primary btn-sm">
                                    Detay Gör
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?php echo site_url('home/search'); ?>" class="btn btn-outline-primary btn-lg">
                Tüm İlanları Gör <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Latest Ads Section -->
<?php if (isset($latest_ads) && !empty($latest_ads)): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="display-5 fw-bold mb-3">
                    <i class="fas fa-clock text-primary me-2"></i>
                    Son Eklenen İlanlar
                </h2>
                <p class="lead text-muted">En yeni escort ilanları</p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($latest_ads as $ad): ?>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card ad-card h-100 shadow-sm">
                        <div class="position-relative">
                            <?php if (!empty($ad->profile_photo)): ?>
                                <img src="<?php echo base_url('uploads/profiles/' . $ad->profile_photo); ?>" 
                                     class="card-img-top" alt="<?php echo htmlspecialchars($ad->display_name); ?>" 
                                     style="height: 250px; object-fit: cover;">
                            <?php else: ?>
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                    <i class="fas fa-user fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($ad->price): ?>
                                <div class="position-absolute top-0 end-0 m-2">
                                    <span class="badge bg-success">
                                        <?php echo number_format($ad->price); ?> ₺
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-body">
                            <h5 class="card-title"><?php echo htmlspecialchars($ad->display_name); ?></h5>
                            <p class="card-text text-muted small">
                                <i class="fas fa-map-marker-alt me-1"></i><?php echo $ad->city_name; ?>
                                <span class="ms-2">
                                    <i class="fas fa-birthday-cake me-1"></i><?php echo $ad->age; ?> yaş
                                </span>
                            </p>
                            <p class="card-text"><?php echo character_limiter(strip_tags($ad->description), 80); ?></p>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i><?php echo timespan(strtotime($ad->created_at), time(), 1); ?> önce
                                </small>
                                <a href="<?php echo site_url('home/ad_detail/' . $ad->id); ?>" class="btn btn-primary btn-sm">
                                    Detay Gör
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Cities Section -->
<?php if (isset($cities) && !empty($cities)): ?>
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="display-5 fw-bold mb-3">
                    <i class="fas fa-map-marker-alt text-danger me-2"></i>
                    Popüler Şehirler
                </h2>
                <p class="lead text-muted">En çok ilan bulunan şehirler</p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach (array_slice($cities, 0, 8) as $city): ?>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <a href="<?php echo site_url('home/city/' . $city->slug); ?>" class="text-decoration-none">
                        <div class="card city-card h-100 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-map-marker-alt fa-2x text-primary mb-3"></i>
                                <h5 class="card-title"><?php echo $city->name; ?></h5>
                                <p class="card-text text-muted">İlanları Görüntüle</p>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Features Section -->
<section class="py-5 bg-dark text-white">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="display-5 fw-bold mb-3">Neden Bizi Tercih Etmelisiniz?</h2>
                <p class="lead">Güvenli, gizli ve kaliteli hizmet garantisi</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-shield-alt fa-3x text-success"></i>
                    </div>
                    <h4>Güvenli Platform</h4>
                    <p class="text-muted">Tüm verileriniz SSL ile korunur. Güvenliğiniz bizim önceliğimizdir.</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-user-check fa-3x text-primary"></i>
                    </div>
                    <h4>Doğrulanmış Profiller</h4>
                    <p class="text-muted">Tüm escort profilleri manuel olarak kontrol edilir ve doğrulanır.</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-eye-slash fa-3x text-warning"></i>
                    </div>
                    <h4>Gizlilik Garantisi</h4>
                    <p class="text-muted">Kişisel bilgileriniz kesinlikle gizli tutulur ve paylaşılmaz.</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-comments fa-3x text-info"></i>
                    </div>
                    <h4>Canlı Mesajlaşma</h4>
                    <p class="text-muted">Güvenli mesajlaşma sistemi ile direkt iletişim kurabilirsiniz.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 60vh;
}

.min-vh-50 {
    min-height: 50vh;
}

.ad-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
}

.ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.city-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
}

.city-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1) !important;
}

.feature-icon {
    transition: transform 0.3s ease;
}

.feature-icon:hover {
    transform: scale(1.1);
}

.stat-item {
    padding: 1rem;
    border-radius: 10px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    margin: 0.5rem;
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 70vh;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .display-5 {
        font-size: 1.5rem;
    }
}
</style>
