<?php
/**
 * ESCORT İLAN SİTESİ - VERİTABANI KURULUM SCRIPTI
 * 
 * Bu script veritabanını otomatik olarak oluşturur ve demo verileri ekler.
 * Sadece ilk kurulumda çalıştırılmalıdır.
 * 
 * <AUTHOR> Site Developer
 * @version 1.0
 * @date 2025-06-30
 */

// Hata raporlamayı aç
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Güvenlik kontrolü - sadece localhost'tan erişim
if (!in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1', 'localhost'])) {
    die('Bu script sadece localhost\'tan çalıştırılabilir!');
}

// Veritabanı bağlantı bilgileri
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'escort_site',
    'charset' => 'utf8mb4'
];

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Escort İlan Sitesi - Veritabanı Kurulumu</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.8;
        }
        .content {
            padding: 30px;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .step h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .config-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .config-info strong {
            color: #495057;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #dee2e6;
        }
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 15px 0;
            overflow: hidden;
        }
        .progress-bar {
            background: #007bff;
            height: 100%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Escort İlan Sitesi</h1>
            <p>Veritabanı Kurulum Sihirbazı</p>
        </div>
        
        <div class="content">
            <?php
            if (!isset($_POST['install'])) {
                // Kurulum formu göster
                ?>
                <div class="step">
                    <h3>📋 Kurulum Öncesi Kontroller</h3>
                    <p>Veritabanı kurulumuna başlamadan önce aşağıdaki bilgileri kontrol edin:</p>
                    
                    <div class="config-info">
                        <strong>Veritabanı Bağlantı Bilgileri:</strong><br>
                        Host: <?php echo $db_config['host']; ?><br>
                        Kullanıcı: <?php echo $db_config['username']; ?><br>
                        Şifre: <?php echo empty($db_config['password']) ? '(Boş)' : '***'; ?><br>
                        Veritabanı: <?php echo $db_config['database']; ?>
                    </div>
                </div>

                <div class="step warning">
                    <h3>⚠️ Önemli Uyarılar</h3>
                    <ul>
                        <li>Bu script sadece <strong>ilk kurulumda</strong> çalıştırılmalıdır</li>
                        <li>Mevcut veritabanı varsa <strong>silinecektir</strong></li>
                        <li>MySQL/MariaDB sunucusunun çalıştığından emin olun</li>
                        <li>Kurulum sonrası bu dosyayı <strong>silin</strong></li>
                    </ul>
                </div>

                <div class="step">
                    <h3>📦 Kurulacak Özellikler</h3>
                    <ul>
                        <li>✅ Kullanıcı yönetim sistemi (Escort/Müşteri/Admin)</li>
                        <li>✅ Escort profil yönetimi</li>
                        <li>✅ İlan yayın sistemi</li>
                        <li>✅ Canlı mesajlaşma altyapısı</li>
                        <li>✅ PayTR ödeme entegrasyonu hazırlığı</li>
                        <li>✅ Admin panel altyapısı</li>
                        <li>✅ Demo veriler ve test hesapları</li>
                    </ul>
                </div>

                <div class="step">
                    <h3>🔑 Test Hesapları</h3>
                    <div class="config-info">
                        <strong>Admin Panel:</strong><br>
                        Kullanıcı: admin<br>
                        Şifre: admin123<br><br>
                        
                        <strong>Test Escort:</strong><br>
                        E-posta: <EMAIL><br>
                        Şifre: test123<br><br>
                        
                        <strong>Test Müşteri:</strong><br>
                        E-posta: <EMAIL><br>
                        Şifre: test123
                    </div>
                </div>

                <form method="post" style="text-align: center; margin-top: 30px;">
                    <button type="submit" name="install" class="btn btn-success">
                        🚀 Kurulumu Başlat
                    </button>
                </form>
                <?php
            } else {
                // Kurulum işlemini başlat
                ?>
                <div class="step">
                    <h3>⚙️ Kurulum İşlemi</h3>
                    <div class="progress">
                        <div class="progress-bar" style="width: 0%" id="progressBar"></div>
                    </div>
                    <div id="installLog"></div>
                </div>

                <script>
                function updateProgress(percent, message) {
                    document.getElementById('progressBar').style.width = percent + '%';
                    document.getElementById('installLog').innerHTML += '<p>' + message + '</p>';
                }

                updateProgress(10, '🔄 Veritabanı bağlantısı kontrol ediliyor...');
                </script>

                <?php
                try {
                    // MySQL bağlantısı oluştur
                    $dsn = "mysql:host={$db_config['host']};charset={$db_config['charset']}";
                    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    echo "<script>updateProgress(20, '✅ Veritabanı sunucusuna bağlantı başarılı');</script>";
                    flush();

                    // SQL dosyasını oku
                    $sql_file = __DIR__ . '/database_setup.sql';
                    if (!file_exists($sql_file)) {
                        throw new Exception('database_setup.sql dosyası bulunamadı!');
                    }

                    $sql_content = file_get_contents($sql_file);
                    echo "<script>updateProgress(30, '✅ SQL dosyası okundu');</script>";
                    flush();

                    // SQL komutlarını ayır ve çalıştır
                    $sql_commands = explode(';', $sql_content);
                    $total_commands = count($sql_commands);
                    $executed = 0;

                    foreach ($sql_commands as $command) {
                        $command = trim($command);
                        if (!empty($command) && !preg_match('/^--/', $command)) {
                            try {
                                $pdo->exec($command);
                                $executed++;
                                $progress = 30 + (($executed / $total_commands) * 60);
                                echo "<script>updateProgress($progress, '⚙️ SQL komutu çalıştırıldı: " . substr($command, 0, 50) . "...');</script>";
                                flush();
                            } catch (PDOException $e) {
                                // Bazı komutlar hata verebilir (örn: DROP IF EXISTS), bunları görmezden gel
                                if (strpos($e->getMessage(), 'already exists') === false) {
                                    echo "<script>updateProgress($progress, '⚠️ Uyarı: " . htmlspecialchars($e->getMessage()) . "');</script>";
                                }
                            }
                        }
                    }

                    echo "<script>updateProgress(95, '✅ Tüm SQL komutları çalıştırıldı');</script>";
                    flush();

                    // Kurulum tamamlandı
                    echo "<script>updateProgress(100, '🎉 Kurulum başarıyla tamamlandı!');</script>";
                    
                    ?>
                    <div class="step success">
                        <h3>🎉 Kurulum Başarılı!</h3>
                        <p>Escort İlan Sitesi veritabanı başarıyla oluşturuldu ve demo veriler eklendi.</p>
                        
                        <div class="config-info">
                            <strong>Sonraki Adımlar:</strong><br>
                            1. Bu dosyayı (install_database.php) güvenlik için silin<br>
                            2. CodeIgniter konfigürasyon dosyalarını düzenleyin<br>
                            3. Admin paneline giriş yaparak ayarları kontrol edin<br>
                            4. PayTR entegrasyonu için API bilgilerini ekleyin
                        </div>

                        <a href="index.php" class="btn btn-success">Ana Sayfaya Git</a>
                        <a href="admin/" class="btn">Admin Paneli</a>
                    </div>
                    <?php

                } catch (Exception $e) {
                    ?>
                    <div class="step error">
                        <h3>❌ Kurulum Hatası</h3>
                        <p><strong>Hata:</strong> <?php echo htmlspecialchars($e->getMessage()); ?></p>
                        <p>Lütfen veritabanı bağlantı bilgilerini kontrol edin ve tekrar deneyin.</p>
                        
                        <a href="?" class="btn">Tekrar Dene</a>
                    </div>
                    <script>
                    updateProgress(0, '❌ Kurulum başarısız: <?php echo htmlspecialchars($e->getMessage()); ?>');
                    </script>
                    <?php
                }
            }
            ?>
        </div>
    </div>
</body>
</html>
