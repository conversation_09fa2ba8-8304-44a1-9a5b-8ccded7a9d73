<?php
/**
 * Fix Brands Table - Eksik sütunları ekle
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

echo "<h1>Brands Tablosu Düzeltme</h1>";

try {
    global $database;
    $pdo = $database->connect();
    
    echo "<h2>Mevcut Brands Tablosu Yapısı:</h2>";
    $stmt = $pdo->query("DESCRIBE brands");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check if address column exists
    $column_exists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'address') {
            $column_exists = true;
            break;
        }
    }
    
    if (!$column_exists) {
        echo "<h2>Address sütunu eksik! Ekleniyor...</h2>";
        
        // Add missing columns
        $alterQueries = [
            "ALTER TABLE brands ADD COLUMN address TEXT AFTER contact_phone",
            "ALTER TABLE brands ADD COLUMN description TEXT AFTER address",
            "ALTER TABLE brands ADD COLUMN logo VARCHAR(255) AFTER description"
        ];
        
        foreach ($alterQueries as $query) {
            try {
                $pdo->exec($query);
                echo "✅ Executed: " . $query . "<br>";
            } catch (Exception $e) {
                echo "⚠️ Warning: " . $e->getMessage() . " (Query: " . $query . ")<br>";
            }
        }
        
        echo "<h2>Güncellenmiş Brands Tablosu Yapısı:</h2>";
        $stmt = $pdo->query("DESCRIBE brands");
        $columns = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h2>✅ Brands tablosu başarıyla güncellendi!</h2>";
        
    } else {
        echo "<h2>✅ Address sütunu zaten mevcut!</h2>";
    }
    
    // Test insert
    echo "<h2>Test Verisi Ekleme:</h2>";
    try {
        $stmt = $pdo->prepare("
            INSERT INTO brands (name, industry, website, contact_person, contact_email, contact_phone, address, description, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE name = VALUES(name)
        ");
        
        $stmt->execute([
            'Test Marka',
            'technology',
            'https://test.com',
            'Test Kişi',
            '<EMAIL>',
            '+90 ************',
            'Test Adres, İstanbul',
            'Test açıklama',
            'active'
        ]);
        
        echo "✅ Test verisi başarıyla eklendi!<br>";
        
    } catch (Exception $e) {
        echo "❌ Test verisi eklenirken hata: " . $e->getMessage() . "<br>";
    }
    
    // Show current brands
    echo "<h2>Mevcut Markalar:</h2>";
    $stmt = $pdo->query("SELECT * FROM brands ORDER BY id DESC LIMIT 5");
    $brands = $stmt->fetchAll();
    
    if (!empty($brands)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Industry</th><th>Contact Person</th><th>Status</th></tr>";
        foreach ($brands as $brand) {
            echo "<tr>";
            echo "<td>" . $brand['id'] . "</td>";
            echo "<td>" . htmlspecialchars($brand['name']) . "</td>";
            echo "<td>" . $brand['industry'] . "</td>";
            echo "<td>" . htmlspecialchars($brand['contact_person']) . "</td>";
            echo "<td>" . $brand['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "Henüz marka eklenmemiş.<br>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Hata:</h2>";
    echo $e->getMessage();
}

echo "<br><br>";
echo "<a href='brands.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Brands Sayfasına Git</a>";
echo " ";
echo "<a href='brands.php?action=add' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Yeni Marka Ekle</a>";
?>
