<!-- Search Header -->
<section class="bg-light py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-3">
                    <i class="fas fa-search me-2"></i>
                    <PERSON><PERSON>
                </h1>
                
                <!-- Search Form -->
                <div class="card shadow-sm">
                    <div class="card-body">
                        <form action="<?php echo base_url('home/search'); ?>" method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="keyword" class="form-label">Ana<PERSON><PERSON></label>
                                <input type="text" class="form-control" id="keyword" name="q" 
                                       value="<?php echo isset($search_params['keyword']) ? htmlspecialchars($search_params['keyword']) : ''; ?>" 
                                       placeholder="Arama yapın...">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="city" class="form-label">Şehir</label>
                                <select name="city" id="city" class="form-select">
                                    <option value="">Tüm Şehirler</option>
                                    <?php if (isset($cities) && !empty($cities)): ?>
                                        <?php foreach ($cities as $city): ?>
                                            <option value="<?php echo $city->id; ?>" 
                                                    <?php echo (isset($search_params['city_id']) && $search_params['city_id'] == $city->id) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($city->name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="category" class="form-label">Kategori</label>
                                <select name="category" id="category" class="form-select">
                                    <option value="">Tüm Kategoriler</option>
                                    <?php if (isset($categories) && !empty($categories)): ?>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo $category->id; ?>" 
                                                    <?php echo (isset($search_params['category_id']) && $search_params['category_id'] == $category->id) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($category->name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="min_age" class="form-label">Min Yaş</label>
                                <select name="min_age" id="min_age" class="form-select">
                                    <option value="">Yaş Seçin</option>
                                    <?php for ($i = 18; $i <= 50; $i++): ?>
                                        <option value="<?php echo $i; ?>" 
                                                <?php echo (isset($search_params['min_age']) && $search_params['min_age'] == $i) ? 'selected' : ''; ?>>
                                            <?php echo $i; ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="max_age" class="form-label">Max Yaş</label>
                                <select name="max_age" id="max_age" class="form-select">
                                    <option value="">Yaş Seçin</option>
                                    <?php for ($i = 18; $i <= 50; $i++): ?>
                                        <option value="<?php echo $i; ?>" 
                                                <?php echo (isset($search_params['max_age']) && $search_params['max_age'] == $i) ? 'selected' : ''; ?>>
                                            <?php echo $i; ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Search Results -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <!-- Results Info -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="h4 mb-0">
                        Arama Sonuçları 
                        <?php if (isset($total_results)): ?>
                            <span class="badge bg-primary"><?php echo number_format($total_results); ?> sonuç</span>
                        <?php endif; ?>
                    </h2>
                    
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="view_type" id="grid_view" autocomplete="off" checked>
                        <label class="btn btn-outline-secondary" for="grid_view">
                            <i class="fas fa-th"></i>
                        </label>
                        
                        <input type="radio" class="btn-check" name="view_type" id="list_view" autocomplete="off">
                        <label class="btn btn-outline-secondary" for="list_view">
                            <i class="fas fa-list"></i>
                        </label>
                    </div>
                </div>
                
                <!-- Results Grid -->
                <?php if (isset($ads) && !empty($ads)): ?>
                    <div class="row" id="results_grid">
                        <?php foreach ($ads as $ad): ?>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                <div class="card ad-card h-100 shadow-sm">
                                    <div class="position-relative">
                                        <?php if (!empty($ad->profile_photo)): ?>
                                            <img src="<?php echo base_url('uploads/profiles/' . $ad->profile_photo); ?>" 
                                                 class="card-img-top" alt="<?php echo htmlspecialchars($ad->display_name); ?>" 
                                                 style="height: 250px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                                <i class="fas fa-user fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (isset($ad->is_featured) && $ad->is_featured): ?>
                                            <div class="position-absolute top-0 start-0 m-2">
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-star me-1"></i>Öne Çıkan
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (isset($ad->price) && $ad->price): ?>
                                            <div class="position-absolute top-0 end-0 m-2">
                                                <span class="badge bg-success">
                                                    <?php echo number_format($ad->price); ?> ₺
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo htmlspecialchars($ad->display_name); ?></h5>
                                        <p class="card-text text-muted small">
                                            <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($ad->city_name); ?>
                                            <?php if (isset($ad->age)): ?>
                                                <span class="ms-2">
                                                    <i class="fas fa-birthday-cake me-1"></i><?php echo $ad->age; ?> yaş
                                                </span>
                                            <?php endif; ?>
                                        </p>
                                        <p class="card-text"><?php echo character_limiter(strip_tags($ad->description), 80); ?></p>
                                    </div>
                                    
                                    <div class="card-footer bg-transparent">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-eye me-1"></i><?php echo number_format($ad->view_count); ?> görüntülenme
                                            </small>
                                            <a href="<?php echo base_url('home/ad_detail/' . $ad->id); ?>" class="btn btn-primary btn-sm">
                                                Detay Gör
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if (isset($pagination) && !empty($pagination)): ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-center">
                                    <?php echo $pagination; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <!-- No Results -->
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h3 class="text-muted">Arama kriterlerinize uygun ilan bulunamadı</h3>
                        <p class="text-muted">Lütfen arama kriterlerinizi değiştirerek tekrar deneyin.</p>
                        <a href="<?php echo base_url('home/search'); ?>" class="btn btn-primary">
                            <i class="fas fa-redo me-2"></i>Yeni Arama
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<style>
.ad-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.btn-check:checked + .btn-outline-secondary {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

@media (max-width: 768px) {
    .ad-card .card-img-top {
        height: 200px;
    }
}
</style>

<script>
$(document).ready(function() {
    // View type toggle
    $('input[name="view_type"]').change(function() {
        if ($(this).attr('id') === 'list_view') {
            $('#results_grid').removeClass('row').addClass('list-view');
            $('#results_grid .col-lg-3').removeClass('col-lg-3 col-md-4 col-sm-6').addClass('col-12');
        } else {
            $('#results_grid').removeClass('list-view').addClass('row');
            $('#results_grid .col-12').removeClass('col-12').addClass('col-lg-3 col-md-4 col-sm-6');
        }
    });
});
</script>
