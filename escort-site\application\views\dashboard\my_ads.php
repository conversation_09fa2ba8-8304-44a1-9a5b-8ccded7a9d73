<!-- My Ads Section -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="<?php echo site_url('dashboard'); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-home me-2"></i>Ana <PERSON>
                        </a>
                        <a href="<?php echo site_url('dashboard/create_ad'); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-plus me-2"></i><PERSON><PERSON>
                        </a>
                        <a href="<?php echo site_url('dashboard/my_ads'); ?>" class="list-group-item list-group-item-action active">
                            <i class="fas fa-list me-2"></i><PERSON><PERSON><PERSON>ım
                        </a>
                        <a href="<?php echo site_url('dashboard/profile'); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-user me-2"></i>Profil
                        </a>
                        <a href="<?php echo site_url('auth/logout'); ?>" class="list-group-item list-group-item-action text-danger">
                            <i class="fas fa-sign-out-alt me-2"></i>Çıkış Yap
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-lg-9">
                <div class="card shadow-sm">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            İlanlarım
                        </h4>
                        <a href="<?php echo site_url('dashboard/create_ad'); ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-plus me-1"></i>Yeni İlan
                        </a>
                    </div>
                    
                    <div class="card-body">
                        <?php if (isset($ads) && !empty($ads)): ?>
                            <!-- Stats Summary -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white text-center">
                                        <div class="card-body">
                                            <h5><?php echo count($ads); ?></h5>
                                            <small>Toplam İlan</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white text-center">
                                        <div class="card-body">
                                            <h5><?php echo count(array_filter($ads, function($ad) { return $ad->status === 'active'; })); ?></h5>
                                            <small>Aktif İlan</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-dark text-center">
                                        <div class="card-body">
                                            <h5><?php echo count(array_filter($ads, function($ad) { return $ad->status === 'pending'; })); ?></h5>
                                            <small>Onay Bekleyen</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white text-center">
                                        <div class="card-body">
                                            <h5><?php echo array_sum(array_map(function($ad) { return $ad->view_count; }, $ads)); ?></h5>
                                            <small>Toplam Görüntülenme</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Ads Table -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>İlan Başlığı</th>
                                            <th>Şehir</th>
                                            <th>Kategori</th>
                                            <th>Durum</th>
                                            <th>Görüntülenme</th>
                                            <th>Fiyat</th>
                                            <th>Tarih</th>
                                            <th>İşlemler</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($ads as $ad): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo character_limiter(htmlspecialchars($ad->title), 40); ?></strong>
                                                    <?php if ($ad->is_featured): ?>
                                                        <span class="badge bg-warning text-dark ms-1">
                                                            <i class="fas fa-star"></i>
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($ad->city_name); ?></td>
                                                <td><?php echo htmlspecialchars($ad->category_name); ?></td>
                                                <td>
                                                    <?php if ($ad->status === 'active'): ?>
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-check me-1"></i>Aktif
                                                        </span>
                                                    <?php elseif ($ad->status === 'pending'): ?>
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="fas fa-clock me-1"></i>Onay Bekliyor
                                                        </span>
                                                    <?php elseif ($ad->status === 'rejected'): ?>
                                                        <span class="badge bg-danger">
                                                            <i class="fas fa-times me-1"></i>Reddedildi
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">
                                                            <i class="fas fa-pause me-1"></i>Pasif
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">
                                                        <i class="fas fa-eye me-1"></i><?php echo number_format($ad->view_count); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($ad->price): ?>
                                                        <span class="text-success fw-bold">
                                                            <?php echo number_format($ad->price); ?> ₺
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <?php echo date('d.m.Y', strtotime($ad->created_at)); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <?php if ($ad->status === 'active'): ?>
                                                            <a href="<?php echo site_url('home/ad_detail/' . $ad->id); ?>" 
                                                               class="btn btn-outline-primary" target="_blank" 
                                                               data-bs-toggle="tooltip" title="İlanı Görüntüle">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        
                                                        <a href="<?php echo site_url('dashboard/edit_ad/' . $ad->id); ?>" 
                                                           class="btn btn-outline-warning"
                                                           data-bs-toggle="tooltip" title="İlanı Düzenle">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        
                                                        <button type="button" class="btn btn-outline-danger" 
                                                                onclick="deleteAd(<?php echo $ad->id; ?>)"
                                                                data-bs-toggle="tooltip" title="İlanı Sil">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                        <?php else: ?>
                            <!-- No Ads -->
                            <div class="text-center py-5">
                                <i class="fas fa-plus-circle fa-4x text-muted mb-4"></i>
                                <h4 class="text-muted mb-3">Henüz ilan oluşturmadınız</h4>
                                <p class="text-muted mb-4">
                                    İlk ilanınızı oluşturmak için aşağıdaki butona tıklayın ve potansiyel müşterilerinize ulaşmaya başlayın.
                                </p>
                                <a href="<?php echo site_url('dashboard/create_ad'); ?>" class="btn btn-success btn-lg">
                                    <i class="fas fa-plus me-2"></i>İlk İlanımı Oluştur
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Help Card -->
                <div class="card mt-4 border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            İpuçları
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><i class="fas fa-check text-success me-2"></i>İlanlarınızı düzenli olarak güncelleyin</li>
                            <li><i class="fas fa-check text-success me-2"></i>Kaliteli fotoğraflar kullanın</li>
                            <li><i class="fas fa-check text-success me-2"></i>Açıklamalarınızı detaylandırın</li>
                            <li><i class="fas fa-check text-success me-2"></i>İletişim bilgilerinizi güncel tutun</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">İlan Silme Onayı</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bu ilanı silmek istediğinizden emin misiniz?</p>
                <p class="text-danger"><strong>Bu işlem geri alınamaz!</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Evet, Sil</button>
            </div>
        </div>
    </div>
</div>

<style>
.list-group-item.active {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.card {
    border-radius: 15px;
    overflow: hidden;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.table th {
    border-top: none;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.2rem 0.4rem;
    }
}
</style>

<script>
let adToDelete = null;

function deleteAd(adId) {
    adToDelete = adId;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Confirm delete
    $('#confirmDelete').click(function() {
        if (adToDelete) {
            window.location.href = '<?php echo site_url("dashboard/delete_ad/"); ?>' + adToDelete;
        }
    });
    
    // Auto refresh every 5 minutes to check status updates
    setInterval(function() {
        // Check if there are pending ads
        const pendingAds = $('.badge:contains("Onay Bekliyor")').length;
        if (pendingAds > 0) {
            // Optionally refresh the page or make an AJAX call to update statuses
            console.log('Checking for status updates...');
        }
    }, 300000); // 5 minutes
});
</script>
