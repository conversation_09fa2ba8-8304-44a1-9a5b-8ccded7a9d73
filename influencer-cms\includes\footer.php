<?php
/**
 * Footer Template
 * Sayfa alt bilgisi ve JavaScript dosyaları
 */

if (!defined('CMS_ROOT')) {
    die('Direct access not allowed');
}
?>

        </div> <!-- End container-fluid -->
    </div> <!-- End main-content -->

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Sidebar Toggle
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Tooltip initialization
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Confirm delete actions
        function confirmDelete(message = 'Bu öğeyi silmek istediğinizden emin misiniz?') {
            return confirm(message);
        }
        
        // Format numbers
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }
        
        // Format currency
        function formatCurrency(amount, currency = 'TRY') {
            const symbols = {
                'TRY': '₺',
                'USD': '$',
                'EUR': '€'
            };
            
            return new Intl.NumberFormat('tr-TR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount) + ' ' + (symbols[currency] || '₺');
        }
        
        // AJAX helper function
        function ajaxRequest(url, data, method = 'POST') {
            return fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: method !== 'GET' ? JSON.stringify(data) : null
            })
            .then(response => response.json())
            .catch(error => {
                console.error('AJAX Error:', error);
                throw error;
            });
        }
        
        // Show loading spinner
        function showLoading(element) {
            const originalContent = element.innerHTML;
            element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Yükleniyor...';
            element.disabled = true;
            
            return function hideLoading() {
                element.innerHTML = originalContent;
                element.disabled = false;
            };
        }
        
        // Show toast notification
        function showToast(message, type = 'success') {
            const toastContainer = document.getElementById('toast-container') || createToastContainer();
            
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', function() {
                toast.remove();
            });
        }
        
        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
            return container;
        }
        
        // Auto-refresh data every 5 minutes
        setInterval(function() {
            if (typeof refreshDashboardData === 'function') {
                refreshDashboardData();
            }
        }, 300000); // 5 minutes
        
        // Handle form submissions with AJAX
        document.addEventListener('DOMContentLoaded', function() {
            const ajaxForms = document.querySelectorAll('.ajax-form');
            
            ajaxForms.forEach(function(form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const formData = new FormData(form);
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const hideLoading = showLoading(submitBtn);
                    
                    fetch(form.action, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        
                        if (data.success) {
                            showToast(data.message || 'İşlem başarılı', 'success');
                            if (data.redirect) {
                                setTimeout(() => window.location.href = data.redirect, 1000);
                            }
                        } else {
                            showToast(data.message || 'Bir hata oluştu', 'danger');
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        showToast('Bir hata oluştu', 'danger');
                        console.error('Form submission error:', error);
                    });
                });
            });
        });
        
        // Search functionality
        function initializeSearch(inputId, tableId) {
            const searchInput = document.getElementById(inputId);
            const table = document.getElementById(tableId);
            
            if (searchInput && table) {
                searchInput.addEventListener('keyup', function() {
                    const filter = this.value.toLowerCase();
                    const rows = table.getElementsByTagName('tr');
                    
                    for (let i = 1; i < rows.length; i++) {
                        const row = rows[i];
                        const cells = row.getElementsByTagName('td');
                        let found = false;
                        
                        for (let j = 0; j < cells.length; j++) {
                            if (cells[j].textContent.toLowerCase().indexOf(filter) > -1) {
                                found = true;
                                break;
                            }
                        }
                        
                        row.style.display = found ? '' : 'none';
                    }
                });
            }
        }
    </script>
    
    <!-- Page specific JavaScript -->
    <?php if (isset($page_scripts)): ?>
        <?php echo $page_scripts; ?>
    <?php endif; ?>
    
    <!-- Footer -->
    <footer class="text-center py-3 mt-5">
        <div class="container-fluid">
            <small class="text-muted">
                © <?php echo date('Y'); ?> <?php echo APP_NAME; ?> v<?php echo APP_VERSION; ?> - 
                Tüm hakları saklıdır.
            </small>
        </div>
    </footer>

</body>
</html>
