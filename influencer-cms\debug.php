<?php
/**
 * Debug Page - Session ve Permission Kontrolü
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

echo "<h1>Debug Bilgileri</h1>";

echo "<h2>Session Bilgileri:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>Kullanıcı Giriş Durumu:</h2>";
echo "isLoggedIn(): " . (isLoggedIn() ? 'TRUE' : 'FALSE') . "<br>";

if (isset($_SESSION['user_role'])) {
    echo "User Role: " . $_SESSION['user_role'] . "<br>";
    echo "Role Admin: " . ROLE_ADMIN . "<br>";
    echo "Is Admin: " . ($_SESSION['user_role'] === ROLE_ADMIN ? 'TRUE' : 'FALSE') . "<br>";
}

echo "<h2>Permission Testi:</h2>";
$test_permissions = ['influencers_view', 'users_manage', 'campaigns_view'];
foreach ($test_permissions as $perm) {
    echo "hasPermission('$perm'): " . (hasPermission($perm) ? 'TRUE' : 'FALSE') . "<br>";
}

echo "<h2>Constants:</h2>";
echo "ROLE_ADMIN: " . ROLE_ADMIN . "<br>";
echo "ROLE_MANAGER: " . ROLE_MANAGER . "<br>";
echo "ROLE_EDITOR: " . ROLE_EDITOR . "<br>";
echo "ROLE_VIEWER: " . ROLE_VIEWER . "<br>";

echo "<h2>PERMISSIONS Array:</h2>";
echo "<pre>";
print_r(PERMISSIONS);
echo "</pre>";

echo "<h2>Database Connection:</h2>";
try {
    global $database;
    $pdo = $database->connect();
    echo "Database connection: SUCCESS<br>";
    
    // Test user query
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    $user = $stmt->fetch();
    
    echo "<h3>Admin User Data:</h3>";
    echo "<pre>";
    print_r($user);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "Database connection: ERROR - " . $e->getMessage() . "<br>";
}

echo "<h2>Test Links:</h2>";
echo '<a href="login.php">Login</a><br>';
echo '<a href="dashboard.php">Dashboard</a><br>';
echo '<a href="influencers.php">Influencers</a><br>';
echo '<a href="logout.php">Logout</a><br>';
?>
