    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="mb-3">
                        <i class="fas fa-heart text-danger me-2"></i>
                        EscortSite
                    </h5>
                    <p class="text-muted">
                        Türkiye'nin en güvenilir escort ilan sitesi. Kaliteli ve doğrulanmış escort ilanları ile güvenli bir platform sunuyoruz.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-telegram"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Hızlı Linkler</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?php echo base_url(); ?>" class="text-muted text-decoration-none">Ana Sayfa</a></li>
                        <li><a href="<?php echo site_url('home/search'); ?>" class="text-muted text-decoration-none">İlan Ara</a></li>
                        <li><a href="<?php echo site_url('home/about'); ?>" class="text-muted text-decoration-none">Hakkımızda</a></li>
                        <li><a href="<?php echo site_url('home/contact'); ?>" class="text-muted text-decoration-none">İletişim</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Kategoriler</h6>
                    <ul class="list-unstyled">
                        <?php if (isset($categories) && !empty($categories)): ?>
                            <?php foreach (array_slice($categories, 0, 4) as $category): ?>
                                <li><a href="<?php echo base_url('home/category/' . $category->slug); ?>" class="text-muted text-decoration-none"><?php echo htmlspecialchars($category->name); ?></a></li>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <li><a href="<?php echo base_url('home/category/escort'); ?>" class="text-muted text-decoration-none">Escort</a></li>
                            <li><a href="<?php echo base_url('home/category/masaj'); ?>" class="text-muted text-decoration-none">Masaj</a></li>
                            <li><a href="<?php echo base_url('home/category/eslik'); ?>" class="text-muted text-decoration-none">Eşlik</a></li>
                            <li><a href="<?php echo base_url('home/category/vip'); ?>" class="text-muted text-decoration-none">VIP</a></li>
                        <?php endif; ?>
                    </ul>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Popüler Şehirler</h6>
                    <ul class="list-unstyled">
                        <?php if (isset($cities) && !empty($cities)): ?>
                            <?php foreach (array_slice($cities, 0, 4) as $city): ?>
                                <li><a href="<?php echo base_url('home/city/' . $city->slug); ?>" class="text-muted text-decoration-none"><?php echo htmlspecialchars($city->name); ?></a></li>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <li><a href="<?php echo base_url('home/city/istanbul'); ?>" class="text-muted text-decoration-none">İstanbul</a></li>
                            <li><a href="<?php echo base_url('home/city/ankara'); ?>" class="text-muted text-decoration-none">Ankara</a></li>
                            <li><a href="<?php echo base_url('home/city/izmir'); ?>" class="text-muted text-decoration-none">İzmir</a></li>
                            <li><a href="<?php echo base_url('home/city/antalya'); ?>" class="text-muted text-decoration-none">Antalya</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Yasal</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?php echo base_url('home/terms'); ?>" class="text-muted text-decoration-none">Kullanım Şartları</a></li>
                        <li><a href="<?php echo base_url('home/privacy'); ?>" class="text-muted text-decoration-none">Gizlilik Politikası</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Çerez Politikası</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">KVKK</a></li>
                    </ul>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        &copy; <?php echo date('Y'); ?> EscortSite. Tüm hakları saklıdır.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">
                        <i class="fas fa-shield-alt me-1"></i>
                        Güvenli ve Gizli Platform
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="btn btn-primary btn-floating" id="backToTop" style="display: none; position: fixed; bottom: 20px; right: 20px; z-index: 1000; border-radius: 50%; width: 50px; height: 50px;">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo base_url('assets/js/main.js'); ?>"></script>

    <script>
        // Back to top button
        $(window).scroll(function() {
            if ($(this).scrollTop() > 100) {
                $('#backToTop').fadeIn();
            } else {
                $('#backToTop').fadeOut();
            }
        });

        $('#backToTop').click(function() {
            $('html, body').animate({scrollTop: 0}, 800);
            return false;
        });

        // Tooltip initialization
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Image lazy loading
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Form validation enhancement
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // Search suggestions (if search input exists)
        if ($('#searchInput').length) {
            $('#searchInput').on('input', function() {
                var query = $(this).val();
                if (query.length > 2) {
                    // AJAX search suggestions implementation
                    // This will be implemented in the next phase
                }
            });
        }

        // Mobile menu enhancements
        $('.navbar-toggler').click(function() {
            $(this).toggleClass('active');
        });

        // Smooth scrolling for anchor links
        $('a[href*="#"]:not([href="#"])').click(function() {
            if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
                var target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 70
                    }, 1000);
                    return false;
                }
            }
        });

        // Price formatting
        $('.price').each(function() {
            var price = $(this).text();
            if (price && !isNaN(price)) {
                $(this).text(new Intl.NumberFormat('tr-TR', {
                    style: 'currency',
                    currency: 'TRY'
                }).format(price));
            }
        });

        // Age verification for external links
        $('a[href^="http"]:not([href*="' + window.location.hostname + '"])').attr('target', '_blank').attr('rel', 'noopener noreferrer');

        // Console warning for developers
        console.log('%c⚠️ DİKKAT!', 'color: red; font-size: 20px; font-weight: bold;');
        console.log('%cBu site yetişkin içeriği barındırmaktadır. 18 yaşından küçükseniz lütfen siteyi terk edin.', 'color: red; font-size: 14px;');
        console.log('%cBu konsolu kullanarak zararlı kod çalıştırmayın. Güvenliğiniz için dikkatli olun.', 'color: orange; font-size: 12px;');
    </script>

    <!-- Google Analytics (Production'da aktif edilecek) -->
    <?php if (ENVIRONMENT === 'production'): ?>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
    </script>
    <?php endif; ?>

</body>
</html>
