<!-- Ad Detail Content -->
<section class="py-4">
    <div class="container">
        <?php if (isset($ad) && $ad): ?>
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8 mb-4">
                    <!-- Ad Images -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-body p-0">
                            <div id="adCarousel" class="carousel slide" data-bs-ride="carousel">
                                <div class="carousel-inner">
                                    <?php 
                                    $photos = [];
                                    if (!empty($ad->profile_photo)) {
                                        $photos[] = $ad->profile_photo;
                                    }
                                    if (!empty($ad->gallery_photos)) {
                                        $gallery = json_decode($ad->gallery_photos, true);
                                        if (is_array($gallery)) {
                                            $photos = array_merge($photos, $gallery);
                                        }
                                    }
                                    
                                    if (empty($photos)) {
                                        $photos = ['default-avatar.jpg'];
                                    }
                                    ?>
                                    
                                    <?php foreach ($photos as $index => $photo): ?>
                                        <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>">
                                            <img src="<?php echo base_url('uploads/profiles/' . $photo); ?>" 
                                                 class="d-block w-100" alt="<?php echo htmlspecialchars($ad->display_name); ?>"
                                                 style="height: 400px; object-fit: cover;">
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                
                                <?php if (count($photos) > 1): ?>
                                    <button class="carousel-control-prev" type="button" data-bs-target="#adCarousel" data-bs-slide="prev">
                                        <span class="carousel-control-prev-icon"></span>
                                    </button>
                                    <button class="carousel-control-next" type="button" data-bs-target="#adCarousel" data-bs-slide="next">
                                        <span class="carousel-control-next-icon"></span>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Ad Info -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h1 class="h3 mb-0"><?php echo htmlspecialchars($ad->title); ?></h1>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h5 class="text-primary"><?php echo htmlspecialchars($ad->display_name); ?></h5>
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-map-marker-alt me-2"></i><?php echo htmlspecialchars($ad->city_name); ?>
                                    </p>
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-birthday-cake me-2"></i><?php echo $ad->age; ?> yaş
                                    </p>
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-tag me-2"></i><?php echo htmlspecialchars($ad->category_name); ?>
                                    </p>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <?php if ($ad->price): ?>
                                        <div class="price-badge">
                                            <span class="badge bg-success fs-5 p-3">
                                                <?php echo number_format($ad->price); ?> ₺
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            <i class="fas fa-eye me-1"></i><?php echo number_format($ad->view_count); ?> görüntülenme
                                        </small>
                                    </div>
                                    
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i><?php echo date('d.m.Y', strtotime($ad->created_at)); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <h5>Açıklama</h5>
                            <p class="lead"><?php echo nl2br(htmlspecialchars($ad->description)); ?></p>
                            
                            <?php if (!empty($ad->services)): ?>
                                <hr>
                                <h5>Hizmetler</h5>
                                <?php 
                                $services = json_decode($ad->services, true);
                                if (is_array($services)):
                                ?>
                                    <div class="row">
                                        <?php foreach ($services as $service): ?>
                                            <div class="col-md-6 mb-2">
                                                <span class="badge bg-light text-dark">
                                                    <i class="fas fa-check me-1"></i><?php echo htmlspecialchars($service); ?>
                                                </span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Contact Card -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-comments me-2"></i>İletişim
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <?php if ($this->session->userdata('user_id')): ?>
                                <button class="btn btn-primary btn-lg w-100 mb-3" onclick="startMessage()">
                                    <i class="fas fa-envelope me-2"></i>Mesaj Gönder
                                </button>
                                <button class="btn btn-outline-danger w-100" onclick="reportAd()">
                                    <i class="fas fa-flag me-2"></i>Şikayet Et
                                </button>
                            <?php else: ?>
                                <p class="text-muted mb-3">İletişim kurmak için giriş yapmalısınız</p>
                                <a href="<?php echo base_url('auth/login'); ?>" class="btn btn-primary btn-lg w-100 mb-2">
                                    <i class="fas fa-sign-in-alt me-2"></i>Giriş Yap
                                </a>
                                <a href="<?php echo base_url('auth/register'); ?>" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-user-plus me-2"></i>Kayıt Ol
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Safety Tips -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-shield-alt me-2"></i>Güvenlik İpuçları
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Kişisel bilgilerinizi koruyun
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Güvenli buluşma yerlerini tercih edin
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Şüpheli davranışları bildirin
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Ön ödeme yapmayın
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Share -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-share-alt me-2"></i>Paylaş
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary" onclick="shareAd('facebook')">
                                    <i class="fab fa-facebook me-2"></i>Facebook
                                </button>
                                <button class="btn btn-outline-info" onclick="shareAd('twitter')">
                                    <i class="fab fa-twitter me-2"></i>Twitter
                                </button>
                                <button class="btn btn-outline-success" onclick="shareAd('whatsapp')">
                                    <i class="fab fa-whatsapp me-2"></i>WhatsApp
                                </button>
                                <button class="btn btn-outline-secondary" onclick="copyLink()">
                                    <i class="fas fa-link me-2"></i>Linki Kopyala
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Other Ads -->
            <?php if (isset($other_ads) && !empty($other_ads)): ?>
                <section class="mt-5">
                    <h3 class="mb-4">Bu Kullanıcının Diğer İlanları</h3>
                    <div class="row">
                        <?php foreach ($other_ads as $other_ad): ?>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                <div class="card ad-card h-100 shadow-sm">
                                    <div class="position-relative">
                                        <?php if (!empty($other_ad->profile_photo)): ?>
                                            <img src="<?php echo base_url('uploads/profiles/' . $other_ad->profile_photo); ?>" 
                                                 class="card-img-top" alt="<?php echo htmlspecialchars($other_ad->display_name); ?>" 
                                                 style="height: 200px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                                <i class="fas fa-user fa-2x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-body">
                                        <h6 class="card-title"><?php echo htmlspecialchars($other_ad->display_name); ?></h6>
                                        <p class="card-text small text-muted">
                                            <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($other_ad->city_name); ?>
                                        </p>
                                    </div>
                                    <div class="card-footer bg-transparent">
                                        <a href="<?php echo base_url('home/ad_detail/' . $other_ad->id); ?>" class="btn btn-primary btn-sm w-100">
                                            Detay Gör
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </section>
            <?php endif; ?>
            
            <!-- Similar Ads -->
            <?php if (isset($similar_ads) && !empty($similar_ads)): ?>
                <section class="mt-5">
                    <h3 class="mb-4">Benzer İlanlar</h3>
                    <div class="row">
                        <?php foreach ($similar_ads as $similar_ad): ?>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                <div class="card ad-card h-100 shadow-sm">
                                    <div class="position-relative">
                                        <?php if (!empty($similar_ad->profile_photo)): ?>
                                            <img src="<?php echo base_url('uploads/profiles/' . $similar_ad->profile_photo); ?>" 
                                                 class="card-img-top" alt="<?php echo htmlspecialchars($similar_ad->display_name); ?>" 
                                                 style="height: 200px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                                <i class="fas fa-user fa-2x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-body">
                                        <h6 class="card-title"><?php echo htmlspecialchars($similar_ad->display_name); ?></h6>
                                        <p class="card-text small text-muted">
                                            <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($similar_ad->city_name); ?>
                                        </p>
                                    </div>
                                    <div class="card-footer bg-transparent">
                                        <a href="<?php echo base_url('home/ad_detail/' . $similar_ad->id); ?>" class="btn btn-primary btn-sm w-100">
                                            Detay Gör
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </section>
            <?php endif; ?>
            
        <?php else: ?>
            <!-- Ad Not Found -->
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h3>İlan Bulunamadı</h3>
                <p class="text-muted">Aradığınız ilan mevcut değil veya kaldırılmış olabilir.</p>
                <a href="<?php echo base_url(); ?>" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i>Ana Sayfaya Dön
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>

<script>
function startMessage() {
    // Mesaj başlatma fonksiyonu
    alert('Mesajlaşma özelliği yakında aktif olacak!');
}

function reportAd() {
    if (confirm('Bu ilanı şikayet etmek istediğinizden emin misiniz?')) {
        alert('Şikayetiniz alındı. En kısa sürede değerlendirilecektir.');
    }
}

function shareAd(platform) {
    const url = window.location.href;
    const title = '<?php echo isset($ad) ? addslashes($ad->title) : ""; ?>';
    
    let shareUrl = '';
    
    switch(platform) {
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            break;
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
            break;
        case 'whatsapp':
            shareUrl = `https://wa.me/?text=${encodeURIComponent(title + ' ' + url)}`;
            break;
    }
    
    if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
    }
}

function copyLink() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        alert('Link kopyalandı!');
    });
}
</script>

<style>
.ad-card {
    transition: transform 0.3s ease;
}

.ad-card:hover {
    transform: translateY(-5px);
}

.price-badge {
    display: inline-block;
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
}

@media (max-width: 768px) {
    .carousel-control-prev,
    .carousel-control-next {
        width: 10%;
    }
}
</style>
