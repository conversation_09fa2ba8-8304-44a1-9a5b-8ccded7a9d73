<?php
// Test mod_rewrite
echo "<h1>Mod_rewrite Test</h1>";

if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✅ mod_rewrite is ENABLED</p>";
    } else {
        echo "<p style='color: red;'>❌ mod_rewrite is DISABLED</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ Cannot detect mod_rewrite status</p>";
}

echo "<h2>Test Links:</h2>";
echo "<ul>";
echo "<li><a href='" . base_url() . "'>Ana <PERSON> (base_url)</a></li>";
echo "<li><a href='" . base_url() . "index.php/home/<USER>/istanbul'>İstanbul (with index.php)</a></li>";
echo "<li><a href='" . base_url() . "home/city/istanbul'>İstanbul (without index.php)</a></li>";
echo "</ul>";

function base_url($uri = '') {
    $base = 'http://localhost/escort-site/';
    return $base . $uri;
}
?>
