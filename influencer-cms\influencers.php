<?php
/**
 * Influencer Management
 * Influencer CRUD işlemleri ve listeleme
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Check permissions
requirePermission('influencers_view');

// Page settings
$page_title = 'Influencer Yönetimi';
$action = $_GET['action'] ?? 'list';
$influencer_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        global $database;
        $pdo = $database->connect();
        
        if ($action === 'add' && hasPermission('influencers_manage')) {
            // Add new influencer
            $stmt = $pdo->prepare("
                INSERT INTO influencers (first_name, last_name, email, phone, address, birth_date, 
                                       gender, city, country, work_type, bio, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                sanitizeInput($_POST['first_name']),
                sanitizeInput($_POST['last_name']),
                sanitizeInput($_POST['email']),
                sanitizeInput($_POST['phone']),
                sanitizeInput($_POST['address']),
                $_POST['birth_date'] ?: null,
                $_POST['gender'] ?: null,
                sanitizeInput($_POST['city']),
                sanitizeInput($_POST['country']) ?: 'Türkiye',
                $_POST['work_type'] ?: 'freelance',
                sanitizeInput($_POST['bio']),
                $_SESSION['user_id']
            ]);
            
            $new_id = $pdo->lastInsertId();
            
            // Handle profile photo upload
            if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] === UPLOAD_ERR_OK) {
                $upload_result = handleFileUpload($_FILES['profile_photo'], 'profiles');
                if ($upload_result['success']) {
                    $stmt = $pdo->prepare("UPDATE influencers SET profile_photo = ? WHERE id = ?");
                    $stmt->execute([$upload_result['filename'], $new_id]);
                }
            }
            
            $_SESSION['success_message'] = 'Influencer başarıyla eklendi.';
            redirectTo('influencers.php');
            
        } elseif ($action === 'edit' && hasPermission('influencers_manage')) {
            // Update influencer
            $stmt = $pdo->prepare("
                UPDATE influencers 
                SET first_name = ?, last_name = ?, email = ?, phone = ?, address = ?, 
                    birth_date = ?, gender = ?, city = ?, country = ?, work_type = ?, bio = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                sanitizeInput($_POST['first_name']),
                sanitizeInput($_POST['last_name']),
                sanitizeInput($_POST['email']),
                sanitizeInput($_POST['phone']),
                sanitizeInput($_POST['address']),
                $_POST['birth_date'] ?: null,
                $_POST['gender'] ?: null,
                sanitizeInput($_POST['city']),
                sanitizeInput($_POST['country']) ?: 'Türkiye',
                $_POST['work_type'] ?: 'freelance',
                sanitizeInput($_POST['bio']),
                $influencer_id
            ]);
            
            // Handle profile photo upload
            if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] === UPLOAD_ERR_OK) {
                $upload_result = handleFileUpload($_FILES['profile_photo'], 'profiles');
                if ($upload_result['success']) {
                    $stmt = $pdo->prepare("UPDATE influencers SET profile_photo = ? WHERE id = ?");
                    $stmt->execute([$upload_result['filename'], $influencer_id]);
                }
            }
            
            $_SESSION['success_message'] = 'Influencer bilgileri güncellendi.';
            redirectTo('influencers.php');
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'İşlem sırasında bir hata oluştu: ' . $e->getMessage();
        error_log('Influencer operation error: ' . $e->getMessage());
    }
}

// Handle delete action
if ($action === 'delete' && $influencer_id && hasPermission('influencers_manage')) {
    try {
        global $database;
        $pdo = $database->connect();
        
        $stmt = $pdo->prepare("DELETE FROM influencers WHERE id = ?");
        $stmt->execute([$influencer_id]);
        
        $_SESSION['success_message'] = 'Influencer silindi.';
        redirectTo('influencers.php');
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Silme işlemi sırasında bir hata oluştu.';
        error_log('Influencer delete error: ' . $e->getMessage());
    }
}

// Get data based on action
if ($action === 'list') {
    // List all influencers
    try {
        global $database;
        $pdo = $database->connect();
        
        $search = $_GET['search'] ?? '';
        $status_filter = $_GET['status'] ?? '';
        $city_filter = $_GET['city'] ?? '';
        
        $where_conditions = [];
        $params = [];
        
        if (!empty($search)) {
            $where_conditions[] = "(first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($status_filter)) {
            $where_conditions[] = "status = ?";
            $params[] = $status_filter;
        }
        
        if (!empty($city_filter)) {
            $where_conditions[] = "city = ?";
            $params[] = $city_filter;
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        $stmt = $pdo->prepare("
            SELECT i.*, 
                   (SELECT COUNT(*) FROM campaigns WHERE influencer_id = i.id) as campaign_count,
                   (SELECT COUNT(*) FROM influencer_platforms WHERE influencer_id = i.id) as platform_count
            FROM influencers i 
            $where_clause
            ORDER BY i.created_at DESC
        ");
        $stmt->execute($params);
        $influencers = $stmt->fetchAll();
        
        // Get unique cities for filter
        $stmt = $pdo->query("SELECT DISTINCT city FROM influencers WHERE city IS NOT NULL ORDER BY city");
        $cities = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
    } catch (Exception $e) {
        $influencers = [];
        $cities = [];
        error_log('Influencer list error: ' . $e->getMessage());
    }
    
} elseif ($action === 'edit' && $influencer_id) {
    // Get influencer for editing
    try {
        global $database;
        $pdo = $database->connect();
        
        $stmt = $pdo->prepare("SELECT * FROM influencers WHERE id = ?");
        $stmt->execute([$influencer_id]);
        $influencer = $stmt->fetch();
        
        if (!$influencer) {
            $_SESSION['error_message'] = 'Influencer bulunamadı.';
            redirectTo('influencers.php');
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Influencer bilgileri alınamadı.';
        redirectTo('influencers.php');
    }
}

// File upload handler
function handleFileUpload($file, $subfolder = '') {
    $upload_dir = UPLOAD_PATH . ($subfolder ? $subfolder . '/' : '');
    
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $allowed_types = ALLOWED_IMAGE_TYPES;
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'Geçersiz dosya türü.'];
    }
    
    if ($file['size'] > UPLOAD_MAX_SIZE) {
        return ['success' => false, 'message' => 'Dosya boyutu çok büyük.'];
    }
    
    $filename = uniqid() . '_' . time() . '.' . $file_extension;
    $filepath = $upload_dir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename];
    } else {
        return ['success' => false, 'message' => 'Dosya yüklenemedi.'];
    }
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <!-- Influencer List -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        Influencer Listesi (<?php echo count($influencers); ?>)
                    </h5>
                    <?php if (hasPermission('influencers_manage')): ?>
                        <a href="influencers.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Yeni Influencer
                        </a>
                    <?php endif; ?>
                </div>
                
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" class="row g-3 mb-4">
                        <div class="col-md-4">
                            <input type="text" class="form-control" name="search" 
                                   value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                                   placeholder="İsim, e-posta ile ara...">
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="">Tüm Durumlar</option>
                                <option value="active" <?php echo ($_GET['status'] ?? '') === 'active' ? 'selected' : ''; ?>>Aktif</option>
                                <option value="inactive" <?php echo ($_GET['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>Pasif</option>
                                <option value="blacklisted" <?php echo ($_GET['status'] ?? '') === 'blacklisted' ? 'selected' : ''; ?>>Kara Liste</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="city" class="form-select">
                                <option value="">Tüm Şehirler</option>
                                <?php foreach ($cities as $city): ?>
                                    <option value="<?php echo htmlspecialchars($city); ?>" 
                                            <?php echo ($_GET['city'] ?? '') === $city ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($city); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search"></i> Filtrele
                            </button>
                        </div>
                    </form>
                    
                    <!-- Influencer Table -->
                    <?php if (!empty($influencers)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Profil</th>
                                        <th>İsim</th>
                                        <th>İletişim</th>
                                        <th>Şehir</th>
                                        <th>Çalışma Tipi</th>
                                        <th>Platform</th>
                                        <th>Kampanya</th>
                                        <th>Durum</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($influencers as $inf): ?>
                                        <tr>
                                            <td>
                                                <?php if ($inf['profile_photo']): ?>
                                                    <img src="<?php echo UPLOAD_URL . 'profiles/' . $inf['profile_photo']; ?>" 
                                                         class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($inf['first_name'] . ' ' . $inf['last_name']); ?></strong>
                                                <?php if ($inf['birth_date']): ?>
                                                    <br><small class="text-muted">
                                                        <?php echo date_diff(date_create($inf['birth_date']), date_create('today'))->y; ?> yaş
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($inf['email']): ?>
                                                    <div><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($inf['email']); ?></div>
                                                <?php endif; ?>
                                                <?php if ($inf['phone']): ?>
                                                    <div><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($inf['phone']); ?></div>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($inf['city'] ?? '-'); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $inf['work_type'] === 'exclusive' ? 'danger' : ($inf['work_type'] === 'contract' ? 'warning' : 'info'); ?>">
                                                    <?php 
                                                    $work_types = ['freelance' => 'Freelance', 'contract' => 'Sözleşmeli', 'exclusive' => 'Özel'];
                                                    echo $work_types[$inf['work_type']] ?? $inf['work_type']; 
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo $inf['platform_count']; ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo $inf['campaign_count']; ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo STATUS_COLORS[$inf['status']] ?? 'secondary'; ?>">
                                                    <?php
                                                    $statuses = ['active' => 'Aktif', 'inactive' => 'Pasif', 'blacklisted' => 'Kara Liste'];
                                                    echo $statuses[$inf['status']] ?? $inf['status'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="influencer_detail.php?id=<?php echo $inf['id']; ?>" 
                                                       class="btn btn-outline-info" data-bs-toggle="tooltip" title="Detay">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if (hasPermission('influencers_manage')): ?>
                                                        <a href="influencers.php?action=edit&id=<?php echo $inf['id']; ?>" 
                                                           class="btn btn-outline-warning" data-bs-toggle="tooltip" title="Düzenle">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="influencers.php?action=delete&id=<?php echo $inf['id']; ?>" 
                                                           class="btn btn-outline-danger" data-bs-toggle="tooltip" title="Sil"
                                                           onclick="return confirmDelete('Bu influencer\'ı silmek istediğinizden emin misiniz?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Henüz influencer eklenmemiş</h5>
                            <p class="text-muted">İlk influencer'ınızı eklemek için yukarıdaki butonu kullanın.</p>
                            <?php if (hasPermission('influencers_manage')): ?>
                                <a href="influencers.php?action=add" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>İlk Influencer'ı Ekle
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Influencer Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                        <?php echo $action === 'add' ? 'Yeni Influencer Ekle' : 'Influencer Düzenle'; ?>
                    </h5>
                </div>

                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                        <div class="row">
                            <!-- Profile Photo -->
                            <div class="col-md-3 mb-4">
                                <div class="text-center">
                                    <div class="profile-photo-preview mb-3">
                                        <?php if ($action === 'edit' && !empty($influencer['profile_photo'])): ?>
                                            <img src="<?php echo UPLOAD_URL . 'profiles/' . $influencer['profile_photo']; ?>"
                                                 class="rounded-circle" width="150" height="150" style="object-fit: cover;" id="photoPreview">
                                        <?php else: ?>
                                            <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto"
                                                 style="width: 150px; height: 150px;" id="photoPreview">
                                                <i class="fas fa-user fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="mb-3">
                                        <label for="profile_photo" class="form-label">Profil Fotoğrafı</label>
                                        <input type="file" class="form-control" id="profile_photo" name="profile_photo"
                                               accept="image/*" onchange="previewPhoto(this)">
                                        <div class="form-text">JPG, PNG, GIF formatları desteklenir. Max 10MB.</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Personal Information -->
                            <div class="col-md-9">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="first_name" class="form-label">Ad <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="first_name" name="first_name"
                                               value="<?php echo htmlspecialchars($influencer['first_name'] ?? ''); ?>" required>
                                        <div class="invalid-feedback">Ad alanı zorunludur.</div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="last_name" class="form-label">Soyad <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="last_name" name="last_name"
                                               value="<?php echo htmlspecialchars($influencer['last_name'] ?? ''); ?>" required>
                                        <div class="invalid-feedback">Soyad alanı zorunludur.</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">E-posta</label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="<?php echo htmlspecialchars($influencer['email'] ?? ''); ?>">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Telefon</label>
                                        <input type="tel" class="form-control" id="phone" name="phone"
                                               value="<?php echo htmlspecialchars($influencer['phone'] ?? ''); ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="birth_date" class="form-label">Doğum Tarihi</label>
                                        <input type="date" class="form-control" id="birth_date" name="birth_date"
                                               value="<?php echo $influencer['birth_date'] ?? ''; ?>">
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="gender" class="form-label">Cinsiyet</label>
                                        <select class="form-select" id="gender" name="gender">
                                            <option value="">Seçiniz</option>
                                            <option value="male" <?php echo ($influencer['gender'] ?? '') === 'male' ? 'selected' : ''; ?>>Erkek</option>
                                            <option value="female" <?php echo ($influencer['gender'] ?? '') === 'female' ? 'selected' : ''; ?>>Kadın</option>
                                            <option value="other" <?php echo ($influencer['gender'] ?? '') === 'other' ? 'selected' : ''; ?>>Diğer</option>
                                        </select>
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="work_type" class="form-label">Çalışma Tipi</label>
                                        <select class="form-select" id="work_type" name="work_type">
                                            <option value="freelance" <?php echo ($influencer['work_type'] ?? 'freelance') === 'freelance' ? 'selected' : ''; ?>>Freelance</option>
                                            <option value="contract" <?php echo ($influencer['work_type'] ?? '') === 'contract' ? 'selected' : ''; ?>>Sözleşmeli</option>
                                            <option value="exclusive" <?php echo ($influencer['work_type'] ?? '') === 'exclusive' ? 'selected' : ''; ?>>Özel</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="city" class="form-label">Şehir</label>
                                        <input type="text" class="form-control" id="city" name="city"
                                               value="<?php echo htmlspecialchars($influencer['city'] ?? ''); ?>"
                                               list="cityList">
                                        <datalist id="cityList">
                                            <option value="İstanbul">
                                            <option value="Ankara">
                                            <option value="İzmir">
                                            <option value="Bursa">
                                            <option value="Antalya">
                                            <option value="Adana">
                                            <option value="Konya">
                                            <option value="Gaziantep">
                                        </datalist>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="country" class="form-label">Ülke</label>
                                        <input type="text" class="form-control" id="country" name="country"
                                               value="<?php echo htmlspecialchars($influencer['country'] ?? 'Türkiye'); ?>">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">Adres</label>
                                    <textarea class="form-control" id="address" name="address" rows="2"><?php echo htmlspecialchars($influencer['address'] ?? ''); ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="bio" class="form-label">Biyografi</label>
                                    <textarea class="form-control" id="bio" name="bio" rows="4"
                                              placeholder="Influencer hakkında kısa bilgi..."><?php echo htmlspecialchars($influencer['bio'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between">
                            <a href="influencers.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Geri Dön
                            </a>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                <?php echo $action === 'add' ? 'Influencer Ekle' : 'Değişiklikleri Kaydet'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
// Page specific scripts
$page_scripts = '
<script>
// Photo preview
function previewPhoto(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            const preview = document.getElementById("photoPreview");
            preview.innerHTML = `<img src="${e.target.result}" class="rounded-circle" width="150" height="150" style="object-fit: cover;">`;
        };

        reader.readAsDataURL(input.files[0]);
    }
}

// Form validation
(function() {
    "use strict";
    window.addEventListener("load", function() {
        var forms = document.getElementsByClassName("needs-validation");
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener("submit", function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add("was-validated");
            }, false);
        });
    }, false);
})();

// Auto-format phone number
document.getElementById("phone")?.addEventListener("input", function(e) {
    let value = e.target.value.replace(/\D/g, "");
    if (value.length > 0) {
        if (value.length <= 3) {
            value = value;
        } else if (value.length <= 6) {
            value = value.slice(0, 3) + " " + value.slice(3);
        } else if (value.length <= 8) {
            value = value.slice(0, 3) + " " + value.slice(3, 6) + " " + value.slice(6);
        } else {
            value = value.slice(0, 3) + " " + value.slice(3, 6) + " " + value.slice(6, 8) + " " + value.slice(8, 10);
        }
    }
    e.target.value = value;
});

// City autocomplete enhancement
const cityInput = document.getElementById("city");
if (cityInput) {
    const turkishCities = [
        "Adana", "Adıyaman", "Afyonkarahisar", "Ağrı", "Amasya", "Ankara", "Antalya", "Artvin",
        "Aydın", "Balıkesir", "Bilecik", "Bingöl", "Bitlis", "Bolu", "Burdur", "Bursa",
        "Çanakkale", "Çankırı", "Çorum", "Denizli", "Diyarbakır", "Edirne", "Elazığ", "Erzincan",
        "Erzurum", "Eskişehir", "Gaziantep", "Giresun", "Gümüşhane", "Hakkari", "Hatay", "Isparta",
        "Mersin", "İstanbul", "İzmir", "Kars", "Kastamonu", "Kayseri", "Kırklareli", "Kırşehir",
        "Kocaeli", "Konya", "Kütahya", "Malatya", "Manisa", "Kahramanmaraş", "Mardin", "Muğla",
        "Muş", "Nevşehir", "Niğde", "Ordu", "Rize", "Sakarya", "Samsun", "Siirt", "Sinop",
        "Sivas", "Tekirdağ", "Tokat", "Trabzon", "Tunceli", "Şanlıurfa", "Uşak", "Van",
        "Yozgat", "Zonguldak", "Aksaray", "Bayburt", "Karaman", "Kırıkkale", "Batman", "Şırnak",
        "Bartın", "Ardahan", "Iğdır", "Yalova", "Karabük", "Kilis", "Osmaniye", "Düzce"
    ];

    cityInput.addEventListener("input", function() {
        const datalist = document.getElementById("cityList");
        datalist.innerHTML = "";

        const value = this.value.toLowerCase();
        const filtered = turkishCities.filter(city =>
            city.toLowerCase().includes(value)
        ).slice(0, 10);

        filtered.forEach(city => {
            const option = document.createElement("option");
            option.value = city;
            datalist.appendChild(option);
        });
    });
}
</script>

<style>
.profile-photo-preview {
    position: relative;
}

.profile-photo-preview:hover::after {
    content: "Değiştirmek için tıklayın";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    white-space: nowrap;
}

.was-validated .form-control:valid {
    border-color: #28a745;
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
}
</style>
';

// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
