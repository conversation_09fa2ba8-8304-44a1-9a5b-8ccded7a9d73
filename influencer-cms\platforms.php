<?php
/**
 * Platform Management
 * Influencer platform bilgileri yönetimi
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Check permissions
requirePermission('influencers_view');

// Page settings
$page_title = 'Platform Yönetimi';
$action = $_GET['action'] ?? 'list';
$influencer_id = $_GET['influencer_id'] ?? null;
$platform_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        global $database;
        $pdo = $database->connect();
        
        if ($action === 'add' && hasPermission('influencers_manage')) {
            // Add new platform
            $stmt = $pdo->prepare("
                INSERT INTO influencer_platforms (influencer_id, platform, username, followers_count, 
                                                 avg_likes, avg_comments, story_views, engagement_rate, 
                                                 profile_url, verified) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $profile_url = $_POST['profile_url'];
            if (empty($profile_url) && !empty($_POST['username'])) {
                $platform_config = PLATFORMS[$_POST['platform']] ?? null;
                if ($platform_config) {
                    $profile_url = str_replace('{username}', $_POST['username'], $platform_config['url_pattern']);
                }
            }
            
            $stmt->execute([
                $_POST['influencer_id'],
                $_POST['platform'],
                sanitizeInput($_POST['username']),
                intval($_POST['followers_count'] ?? 0),
                intval($_POST['avg_likes'] ?? 0),
                intval($_POST['avg_comments'] ?? 0),
                intval($_POST['story_views'] ?? 0),
                floatval($_POST['engagement_rate'] ?? 0),
                $profile_url,
                isset($_POST['verified']) ? 1 : 0
            ]);
            
            $_SESSION['success_message'] = 'Platform başarıyla eklendi.';
            redirectTo('influencer_detail.php?id=' . $_POST['influencer_id'] . '#platforms');
            
        } elseif ($action === 'edit' && hasPermission('influencers_manage')) {
            // Update platform
            $stmt = $pdo->prepare("
                UPDATE influencer_platforms 
                SET username = ?, followers_count = ?, avg_likes = ?, avg_comments = ?, 
                    story_views = ?, engagement_rate = ?, profile_url = ?, verified = ?, 
                    last_updated = NOW()
                WHERE id = ?
            ");
            
            $profile_url = $_POST['profile_url'];
            if (empty($profile_url) && !empty($_POST['username'])) {
                $platform_config = PLATFORMS[$_POST['platform']] ?? null;
                if ($platform_config) {
                    $profile_url = str_replace('{username}', $_POST['username'], $platform_config['url_pattern']);
                }
            }
            
            $stmt->execute([
                sanitizeInput($_POST['username']),
                intval($_POST['followers_count'] ?? 0),
                intval($_POST['avg_likes'] ?? 0),
                intval($_POST['avg_comments'] ?? 0),
                intval($_POST['story_views'] ?? 0),
                floatval($_POST['engagement_rate'] ?? 0),
                $profile_url,
                isset($_POST['verified']) ? 1 : 0,
                $platform_id
            ]);
            
            $_SESSION['success_message'] = 'Platform bilgileri güncellendi.';
            redirectTo('platforms.php?influencer_id=' . $_POST['influencer_id']);
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'İşlem sırasında bir hata oluştu: ' . $e->getMessage();
        error_log('Platform operation error: ' . $e->getMessage());
    }
}

// Handle delete action
if ($action === 'delete' && $platform_id && hasPermission('influencers_manage')) {
    try {
        global $database;
        $pdo = $database->connect();
        
        $stmt = $pdo->prepare("DELETE FROM influencer_platforms WHERE id = ?");
        $stmt->execute([$platform_id]);
        
        $_SESSION['success_message'] = 'Platform silindi.';
        redirectTo('platforms.php?influencer_id=' . $influencer_id);
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Silme işlemi sırasında bir hata oluştu.';
        error_log('Platform delete error: ' . $e->getMessage());
    }
}

// Get data based on action
if ($influencer_id) {
    try {
        global $database;
        $pdo = $database->connect();
        
        // Get influencer info
        $stmt = $pdo->prepare("SELECT * FROM influencers WHERE id = ?");
        $stmt->execute([$influencer_id]);
        $influencer = $stmt->fetch();
        
        if (!$influencer) {
            $_SESSION['error_message'] = 'Influencer bulunamadı.';
            redirectTo('influencers.php');
        }
        
        // Get platforms
        $stmt = $pdo->prepare("SELECT * FROM influencer_platforms WHERE influencer_id = ? ORDER BY platform");
        $stmt->execute([$influencer_id]);
        $platforms = $stmt->fetchAll();
        
        // Get platform for editing
        if ($action === 'edit' && $platform_id) {
            $stmt = $pdo->prepare("SELECT * FROM influencer_platforms WHERE id = ? AND influencer_id = ?");
            $stmt->execute([$platform_id, $influencer_id]);
            $platform = $stmt->fetch();
            
            if (!$platform) {
                $_SESSION['error_message'] = 'Platform bulunamadı.';
                redirectTo('platforms.php?influencer_id=' . $influencer_id);
            }
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Veri alınırken hata oluştu.';
        error_log('Platform data error: ' . $e->getMessage());
        redirectTo('influencers.php');
    }
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($influencer_id && isset($influencer)): ?>
    <!-- Influencer Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <?php if ($influencer['profile_photo']): ?>
                                <img src="<?php echo UPLOAD_URL . 'profiles/' . $influencer['profile_photo']; ?>" 
                                     class="rounded-circle me-3" width="60" height="60" style="object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center me-3" 
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h4 class="mb-1"><?php echo htmlspecialchars($influencer['first_name'] . ' ' . $influencer['last_name']); ?></h4>
                                <p class="mb-0 opacity-75">Platform Yönetimi</p>
                            </div>
                        </div>
                        <div>
                            <a href="influencer_detail.php?id=<?php echo $influencer['id']; ?>" class="btn btn-light">
                                <i class="fas fa-arrow-left me-2"></i>Geri Dön
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($action === 'list'): ?>
        <!-- Platform List -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-share-alt me-2"></i>
                            Sosyal Medya Platformları (<?php echo count($platforms); ?>)
                        </h5>
                        <?php if (hasPermission('influencers_manage')): ?>
                            <a href="platforms.php?action=add&influencer_id=<?php echo $influencer_id; ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Platform Ekle
                            </a>
                        <?php endif; ?>
                    </div>
                    
                    <div class="card-body">
                        <?php if (!empty($platforms)): ?>
                            <div class="row">
                                <?php foreach ($platforms as $plat): ?>
                                    <div class="col-md-6 col-lg-4 mb-4">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-body">
                                                <div class="d-flex align-items-center mb-3">
                                                    <i class="<?php echo PLATFORMS[$plat['platform']]['icon'] ?? 'fas fa-globe'; ?> fa-2x me-3" 
                                                       style="color: <?php echo PLATFORMS[$plat['platform']]['color'] ?? '#6c757d'; ?>"></i>
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-0"><?php echo PLATFORMS[$plat['platform']]['name'] ?? ucfirst($plat['platform']); ?></h6>
                                                        <small class="text-muted">@<?php echo htmlspecialchars($plat['username']); ?></small>
                                                    </div>
                                                    <?php if ($plat['verified']): ?>
                                                        <i class="fas fa-check-circle text-primary"></i>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <div class="row text-center mb-3">
                                                    <div class="col-4">
                                                        <div class="border-end">
                                                            <h6 class="mb-0"><?php echo formatNumber($plat['followers_count']); ?></h6>
                                                            <small class="text-muted">Takipçi</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="border-end">
                                                            <h6 class="mb-0"><?php echo formatNumber($plat['avg_likes']); ?></h6>
                                                            <small class="text-muted">Beğeni</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <h6 class="mb-0"><?php echo number_format($plat['engagement_rate'], 2); ?>%</h6>
                                                        <small class="text-muted">Etkileşim</small>
                                                    </div>
                                                </div>
                                                
                                                <div class="d-flex gap-2">
                                                    <?php if ($plat['profile_url']): ?>
                                                        <a href="<?php echo htmlspecialchars($plat['profile_url']); ?>" 
                                                           target="_blank" class="btn btn-outline-primary btn-sm flex-grow-1">
                                                            <i class="fas fa-external-link-alt me-1"></i>Görüntüle
                                                        </a>
                                                    <?php endif; ?>
                                                    
                                                    <?php if (hasPermission('influencers_manage')): ?>
                                                        <a href="platforms.php?action=edit&id=<?php echo $plat['id']; ?>&influencer_id=<?php echo $influencer_id; ?>" 
                                                           class="btn btn-outline-warning btn-sm">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="platforms.php?action=delete&id=<?php echo $plat['id']; ?>&influencer_id=<?php echo $influencer_id; ?>" 
                                                           class="btn btn-outline-danger btn-sm"
                                                           onclick="return confirmDelete('Bu platformu silmek istediğinizden emin misiniz?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <div class="mt-2">
                                                    <small class="text-muted">
                                                        Son güncelleme: <?php echo date('d.m.Y', strtotime($plat['last_updated'])); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-share-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Henüz platform eklenmemiş</h5>
                                <p class="text-muted">Bu influencer için sosyal medya platformları ekleyin.</p>
                                <?php if (hasPermission('influencers_manage')): ?>
                                    <a href="platforms.php?action=add&influencer_id=<?php echo $influencer_id; ?>" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>İlk Platformu Ekle
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

    <?php elseif ($action === 'add' || $action === 'edit'): ?>
        <!-- Add/Edit Platform Form -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                            <?php echo $action === 'add' ? 'Platform Ekle' : 'Platform Düzenle'; ?>
                        </h5>
                    </div>
                    
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="influencer_id" value="<?php echo $influencer_id; ?>">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="platform" class="form-label">Platform <span class="text-danger">*</span></label>
                                    <select class="form-select" id="platform" name="platform" required <?php echo $action === 'edit' ? 'disabled' : ''; ?>>
                                        <option value="">Platform Seçin</option>
                                        <?php foreach (PLATFORMS as $key => $config): ?>
                                            <option value="<?php echo $key; ?>" 
                                                    <?php echo ($action === 'edit' && $platform['platform'] === $key) ? 'selected' : ''; ?>>
                                                <?php echo $config['name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <?php if ($action === 'edit'): ?>
                                        <input type="hidden" name="platform" value="<?php echo $platform['platform']; ?>">
                                    <?php endif; ?>
                                    <div class="invalid-feedback">Platform seçimi zorunludur.</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">Kullanıcı Adı <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">@</span>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="<?php echo htmlspecialchars($platform['username'] ?? ''); ?>" required>
                                    </div>
                                    <div class="invalid-feedback">Kullanıcı adı zorunludur.</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="followers_count" class="form-label">Takipçi Sayısı</label>
                                    <input type="number" class="form-control" id="followers_count" name="followers_count" 
                                           value="<?php echo $platform['followers_count'] ?? ''; ?>" min="0">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="avg_likes" class="form-label">Ortalama Beğeni</label>
                                    <input type="number" class="form-control" id="avg_likes" name="avg_likes" 
                                           value="<?php echo $platform['avg_likes'] ?? ''; ?>" min="0">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="engagement_rate" class="form-label">Etkileşim Oranı (%)</label>
                                    <input type="number" class="form-control" id="engagement_rate" name="engagement_rate" 
                                           value="<?php echo $platform['engagement_rate'] ?? ''; ?>" min="0" max="100" step="0.01">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="avg_comments" class="form-label">Ortalama Yorum</label>
                                    <input type="number" class="form-control" id="avg_comments" name="avg_comments" 
                                           value="<?php echo $platform['avg_comments'] ?? ''; ?>" min="0">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="story_views" class="form-label">Story Görüntülenme</label>
                                    <input type="number" class="form-control" id="story_views" name="story_views" 
                                           value="<?php echo $platform['story_views'] ?? ''; ?>" min="0">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="profile_url" class="form-label">Profil URL'si</label>
                                <input type="url" class="form-control" id="profile_url" name="profile_url" 
                                       value="<?php echo htmlspecialchars($platform['profile_url'] ?? ''); ?>"
                                       placeholder="https://...">
                                <div class="form-text">Boş bırakılırsa otomatik oluşturulur.</div>
                            </div>
                            
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="verified" name="verified" value="1"
                                           <?php echo ($platform['verified'] ?? false) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="verified">
                                        Doğrulanmış hesap
                                    </label>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="d-flex justify-content-between">
                                <a href="platforms.php?influencer_id=<?php echo $influencer_id; ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Geri Dön
                                </a>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    <?php echo $action === 'add' ? 'Platform Ekle' : 'Değişiklikleri Kaydet'; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

<?php else: ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        Geçersiz influencer ID'si.
    </div>
<?php endif; ?>

<?php
// Page specific scripts
$page_scripts = '
<script>
// Form validation
(function() {
    "use strict";
    window.addEventListener("load", function() {
        var forms = document.getElementsByClassName("needs-validation");
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener("submit", function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add("was-validated");
            }, false);
        });
    }, false);
})();

// Auto-generate profile URL
document.getElementById("username")?.addEventListener("input", function() {
    const platform = document.getElementById("platform").value;
    const username = this.value;
    const profileUrlField = document.getElementById("profile_url");
    
    if (platform && username && !profileUrlField.value) {
        const platforms = ' . json_encode(PLATFORMS) . ';
        if (platforms[platform] && platforms[platform].url_pattern) {
            const url = platforms[platform].url_pattern.replace("{username}", username);
            profileUrlField.value = url;
        }
    }
});

document.getElementById("platform")?.addEventListener("change", function() {
    const username = document.getElementById("username").value;
    const profileUrlField = document.getElementById("profile_url");
    
    if (this.value && username && !profileUrlField.value) {
        const platforms = ' . json_encode(PLATFORMS) . ';
        if (platforms[this.value] && platforms[this.value].url_pattern) {
            const url = platforms[this.value].url_pattern.replace("{username}", username);
            profileUrlField.value = url;
        }
    }
});

// Calculate engagement rate
function calculateEngagementRate() {
    const followers = parseInt(document.getElementById("followers_count").value) || 0;
    const likes = parseInt(document.getElementById("avg_likes").value) || 0;
    const comments = parseInt(document.getElementById("avg_comments").value) || 0;
    
    if (followers > 0) {
        const engagement = ((likes + comments) / followers) * 100;
        document.getElementById("engagement_rate").value = engagement.toFixed(2);
    }
}

document.getElementById("followers_count")?.addEventListener("input", calculateEngagementRate);
document.getElementById("avg_likes")?.addEventListener("input", calculateEngagementRate);
document.getElementById("avg_comments")?.addEventListener("input", calculateEngagementRate);
</script>
';

// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
