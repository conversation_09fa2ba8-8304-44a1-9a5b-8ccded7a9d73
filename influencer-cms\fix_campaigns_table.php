<?php
/**
 * Fix Campaigns Table - Eksik sütunları ekle
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

echo "<h1>Campaigns Tablosu Düzeltme</h1>";

try {
    global $database;
    $pdo = $database->connect();
    
    echo "<h2>Mevcut Campaigns Tablosu Yapısı:</h2>";
    $stmt = $pdo->query("DESCRIBE campaigns");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check required columns
    $required_columns = [
        'content_url' => 'VARCHAR(500)',
        'performance_notes' => 'TEXT',
        'rating' => 'INT(1)'
    ];
    
    $existing_columns = array_column($columns, 'Field');
    $missing_columns = [];
    
    foreach ($required_columns as $col_name => $col_type) {
        if (!in_array($col_name, $existing_columns)) {
            $missing_columns[$col_name] = $col_type;
        }
    }
    
    if (!empty($missing_columns)) {
        echo "<h2>Eksik sütunlar bulundu! Ekleniyor...</h2>";
        
        foreach ($missing_columns as $col_name => $col_type) {
            try {
                $query = "ALTER TABLE campaigns ADD COLUMN $col_name $col_type";
                $pdo->exec($query);
                echo "✅ Eklendi: $col_name ($col_type)<br>";
            } catch (Exception $e) {
                echo "⚠️ Hata: " . $e->getMessage() . "<br>";
            }
        }
        
        echo "<h2>Güncellenmiş Campaigns Tablosu Yapısı:</h2>";
        $stmt = $pdo->query("DESCRIBE campaigns");
        $columns = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h2>✅ Campaigns tablosu başarıyla güncellendi!</h2>";
        
    } else {
        echo "<h2>✅ Tüm gerekli sütunlar mevcut!</h2>";
    }
    
    // Test data
    echo "<h2>Test Kampanyası Ekleme:</h2>";
    
    // First check if we have brands and influencers
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM brands");
    $brand_count = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM influencers");
    $influencer_count = $stmt->fetch()['count'];
    
    if ($brand_count == 0) {
        echo "⚠️ Önce marka eklemeniz gerekiyor.<br>";
        echo "<a href='brands.php?action=add' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Marka Ekle</a><br><br>";
    }
    
    if ($influencer_count == 0) {
        echo "⚠️ Önce influencer eklemeniz gerekiyor.<br>";
        echo "<a href='influencers.php?action=add' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Influencer Ekle</a><br><br>";
    }
    
    if ($brand_count > 0 && $influencer_count > 0) {
        // Get first brand and influencer
        $stmt = $pdo->query("SELECT id FROM brands LIMIT 1");
        $brand_id = $stmt->fetch()['id'];
        
        $stmt = $pdo->query("SELECT id FROM influencers LIMIT 1");
        $influencer_id = $stmt->fetch()['id'];
        
        try {
            $stmt = $pdo->prepare("
                INSERT INTO campaigns (influencer_id, brand_id, title, description, platform, 
                                     content_type, price, currency, status, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE title = VALUES(title)
            ");
            
            $stmt->execute([
                $influencer_id,
                $brand_id,
                'Test Kampanyası',
                'Bu bir test kampanyasıdır.',
                'instagram',
                'post',
                3000,
                'TRY',
                'planned',
                $_SESSION['user_id'] ?? 1
            ]);
            
            echo "✅ Test kampanyası başarıyla eklendi!<br>";
            
        } catch (Exception $e) {
            echo "❌ Test kampanyası eklenirken hata: " . $e->getMessage() . "<br>";
        }
    }
    
    // Show current campaigns
    echo "<h2>Mevcut Kampanyalar:</h2>";
    $stmt = $pdo->query("
        SELECT c.*, 
               CONCAT(i.first_name, ' ', i.last_name) as influencer_name,
               b.name as brand_name
        FROM campaigns c 
        LEFT JOIN influencers i ON c.influencer_id = i.id
        LEFT JOIN brands b ON c.brand_id = b.id
        ORDER BY c.id DESC LIMIT 5
    ");
    $campaigns = $stmt->fetchAll();
    
    if (!empty($campaigns)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Influencer</th><th>Brand</th><th>Platform</th><th>Price</th><th>Status</th></tr>";
        foreach ($campaigns as $campaign) {
            echo "<tr>";
            echo "<td>" . $campaign['id'] . "</td>";
            echo "<td>" . htmlspecialchars($campaign['title']) . "</td>";
            echo "<td>" . htmlspecialchars($campaign['influencer_name']) . "</td>";
            echo "<td>" . htmlspecialchars($campaign['brand_name']) . "</td>";
            echo "<td>" . $campaign['platform'] . "</td>";
            echo "<td>" . $campaign['price'] . " " . $campaign['currency'] . "</td>";
            echo "<td>" . $campaign['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "Henüz kampanya eklenmemiş.<br>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Hata:</h2>";
    echo $e->getMessage();
}

echo "<br><br>";
echo "<a href='campaigns.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Kampanyalar Sayfasına Git</a>";
echo " ";
echo "<a href='campaigns.php?action=add' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Yeni Kampanya Oluştur</a>";
?>
