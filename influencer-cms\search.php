<?php
/**
 * Global Search System
 * Gelişmiş arama ve filtreleme sistemi
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Check permissions
requireLogin();

// Page settings
$page_title = 'Gelişmiş Arama';
$query = $_GET['q'] ?? '';
$type = $_GET['type'] ?? 'all';
$filters = $_GET['filters'] ?? [];

// Search results
$results = [
    'influencers' => [],
    'campaigns' => [],
    'brands' => [],
    'files' => [],
    'todos' => []
];

$total_results = 0;

if (!empty($query)) {
    try {
        global $database;
        $pdo = $database->connect();
        
        // Search influencers
        if ($type === 'all' || $type === 'influencers') {
            $stmt = $pdo->prepare("
                SELECT 'influencer' as result_type, id, 
                       CONCAT(first_name, ' ', last_name) as title,
                       CONCAT(city, ' - ', work_type) as subtitle,
                       profile_photo as image,
                       created_at
                FROM influencers 
                WHERE (first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR city LIKE ?)
                AND status = 'active'
                ORDER BY created_at DESC
                LIMIT 20
            ");
            $search_term = "%$query%";
            $stmt->execute([$search_term, $search_term, $search_term, $search_term]);
            $results['influencers'] = $stmt->fetchAll();
        }
        
        // Search campaigns
        if ($type === 'all' || $type === 'campaigns') {
            $stmt = $pdo->prepare("
                SELECT 'campaign' as result_type, c.id,
                       c.title,
                       CONCAT(b.name, ' - ', CONCAT(i.first_name, ' ', i.last_name)) as subtitle,
                       NULL as image,
                       c.created_at
                FROM campaigns c
                LEFT JOIN brands b ON c.brand_id = b.id
                LEFT JOIN influencers i ON c.influencer_id = i.id
                WHERE (c.title LIKE ? OR c.description LIKE ? OR b.name LIKE ?)
                ORDER BY c.created_at DESC
                LIMIT 20
            ");
            $search_term = "%$query%";
            $stmt->execute([$search_term, $search_term, $search_term]);
            $results['campaigns'] = $stmt->fetchAll();
        }
        
        // Search brands
        if ($type === 'all' || $type === 'brands') {
            $stmt = $pdo->prepare("
                SELECT 'brand' as result_type, id,
                       name as title,
                       CONCAT(industry, ' - ', contact_person) as subtitle,
                       logo as image,
                       created_at
                FROM brands 
                WHERE (name LIKE ? OR industry LIKE ? OR contact_person LIKE ?)
                AND status = 'active'
                ORDER BY created_at DESC
                LIMIT 20
            ");
            $search_term = "%$query%";
            $stmt->execute([$search_term, $search_term, $search_term]);
            $results['brands'] = $stmt->fetchAll();
        }
        
        // Search files
        if ($type === 'all' || $type === 'files') {
            $stmt = $pdo->prepare("
                SELECT 'file' as result_type, f.id,
                       f.original_name as title,
                       CONCAT(f.file_type, ' - ', CONCAT(i.first_name, ' ', i.last_name)) as subtitle,
                       NULL as image,
                       f.created_at
                FROM files f
                LEFT JOIN influencers i ON f.influencer_id = i.id
                WHERE (f.original_name LIKE ? OR f.description LIKE ?)
                ORDER BY f.created_at DESC
                LIMIT 20
            ");
            $search_term = "%$query%";
            $stmt->execute([$search_term, $search_term]);
            $results['files'] = $stmt->fetchAll();
        }
        
        // Search todos
        if ($type === 'all' || $type === 'todos') {
            $stmt = $pdo->prepare("
                SELECT 'todo' as result_type, t.id,
                       t.title,
                       CONCAT(t.priority, ' - ', t.status) as subtitle,
                       NULL as image,
                       t.created_at
                FROM todos t
                WHERE (t.title LIKE ? OR t.description LIKE ?)
                ORDER BY t.created_at DESC
                LIMIT 20
            ");
            $search_term = "%$query%";
            $stmt->execute([$search_term, $search_term]);
            $results['todos'] = $stmt->fetchAll();
        }
        
        // Calculate total results
        foreach ($results as $category) {
            $total_results += count($category);
        }
        
    } catch (Exception $e) {
        error_log('Search error: ' . $e->getMessage());
        $_SESSION['error_message'] = 'Arama sırasında bir hata oluştu.';
    }
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Search Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h2 class="mb-3">
                    <i class="fas fa-search me-2"></i>
                    Gelişmiş Arama
                </h2>
                
                <!-- Search Form -->
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <div class="input-group input-group-lg">
                            <span class="input-group-text bg-white">
                                <i class="fas fa-search text-primary"></i>
                            </span>
                            <input type="text" class="form-control" name="q" 
                                   value="<?php echo htmlspecialchars($query); ?>"
                                   placeholder="Ne aramak istiyorsunuz?" autofocus>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <select name="type" class="form-select form-select-lg">
                            <option value="all" <?php echo $type === 'all' ? 'selected' : ''; ?>>Tümünde Ara</option>
                            <option value="influencers" <?php echo $type === 'influencers' ? 'selected' : ''; ?>>Influencer'larda</option>
                            <option value="campaigns" <?php echo $type === 'campaigns' ? 'selected' : ''; ?>>Kampanyalarda</option>
                            <option value="brands" <?php echo $type === 'brands' ? 'selected' : ''; ?>>Markalarda</option>
                            <option value="files" <?php echo $type === 'files' ? 'selected' : ''; ?>>Dosyalarda</option>
                            <option value="todos" <?php echo $type === 'todos' ? 'selected' : ''; ?>>Görevlerde</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-light btn-lg w-100">
                            <i class="fas fa-search me-2"></i>Ara
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($query)): ?>
    <!-- Search Results -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        Arama Sonuçları: "<?php echo htmlspecialchars($query); ?>" 
                        <span class="badge bg-primary"><?php echo $total_results; ?> sonuç</span>
                    </h5>
                </div>
                
                <div class="card-body">
                    <?php if ($total_results > 0): ?>
                        <!-- Results Tabs -->
                        <ul class="nav nav-tabs mb-4" id="resultTabs" role="tablist">
                            <?php if ($type === 'all'): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all-results" type="button">
                                        Tümü (<?php echo $total_results; ?>)
                                    </button>
                                </li>
                            <?php endif; ?>
                            
                            <?php if (!empty($results['influencers'])): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link <?php echo $type === 'influencers' ? 'active' : ''; ?>" 
                                            id="influencers-tab" data-bs-toggle="tab" data-bs-target="#influencers-results" type="button">
                                        Influencer'lar (<?php echo count($results['influencers']); ?>)
                                    </button>
                                </li>
                            <?php endif; ?>
                            
                            <?php if (!empty($results['campaigns'])): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link <?php echo $type === 'campaigns' ? 'active' : ''; ?>" 
                                            id="campaigns-tab" data-bs-toggle="tab" data-bs-target="#campaigns-results" type="button">
                                        Kampanyalar (<?php echo count($results['campaigns']); ?>)
                                    </button>
                                </li>
                            <?php endif; ?>
                            
                            <?php if (!empty($results['brands'])): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link <?php echo $type === 'brands' ? 'active' : ''; ?>" 
                                            id="brands-tab" data-bs-toggle="tab" data-bs-target="#brands-results" type="button">
                                        Markalar (<?php echo count($results['brands']); ?>)
                                    </button>
                                </li>
                            <?php endif; ?>
                            
                            <?php if (!empty($results['files'])): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link <?php echo $type === 'files' ? 'active' : ''; ?>" 
                                            id="files-tab" data-bs-toggle="tab" data-bs-target="#files-results" type="button">
                                        Dosyalar (<?php echo count($results['files']); ?>)
                                    </button>
                                </li>
                            <?php endif; ?>
                            
                            <?php if (!empty($results['todos'])): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link <?php echo $type === 'todos' ? 'active' : ''; ?>" 
                                            id="todos-tab" data-bs-toggle="tab" data-bs-target="#todos-results" type="button">
                                        Görevler (<?php echo count($results['todos']); ?>)
                                    </button>
                                </li>
                            <?php endif; ?>
                        </ul>
                        
                        <!-- Results Content -->
                        <div class="tab-content" id="resultTabsContent">
                            <?php if ($type === 'all'): ?>
                                <div class="tab-pane fade show active" id="all-results" role="tabpanel">
                                    <?php
                                    // Combine all results
                                    $all_results = [];
                                    foreach ($results as $category => $items) {
                                        $all_results = array_merge($all_results, $items);
                                    }
                                    
                                    // Sort by date
                                    usort($all_results, function($a, $b) {
                                        return strtotime($b['created_at']) - strtotime($a['created_at']);
                                    });
                                    
                                    foreach ($all_results as $result):
                                    ?>
                                        <div class="search-result-item mb-3 p-3 border rounded">
                                            <div class="d-flex align-items-start">
                                                <div class="result-icon me-3">
                                                    <?php
                                                    $icon_class = 'fas fa-file';
                                                    $icon_color = '#6c757d';
                                                    
                                                    switch ($result['result_type']) {
                                                        case 'influencer':
                                                            $icon_class = 'fas fa-star';
                                                            $icon_color = '#ffc107';
                                                            break;
                                                        case 'campaign':
                                                            $icon_class = 'fas fa-bullhorn';
                                                            $icon_color = '#28a745';
                                                            break;
                                                        case 'brand':
                                                            $icon_class = 'fas fa-building';
                                                            $icon_color = '#007bff';
                                                            break;
                                                        case 'file':
                                                            $icon_class = 'fas fa-file';
                                                            $icon_color = '#6f42c1';
                                                            break;
                                                        case 'todo':
                                                            $icon_class = 'fas fa-tasks';
                                                            $icon_color = '#fd7e14';
                                                            break;
                                                    }
                                                    ?>
                                                    <i class="<?php echo $icon_class; ?> fa-2x" style="color: <?php echo $icon_color; ?>"></i>
                                                </div>
                                                
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1">
                                                        <a href="<?php echo getResultUrl($result); ?>" class="text-decoration-none">
                                                            <?php echo highlightSearchTerm(htmlspecialchars($result['title']), $query); ?>
                                                        </a>
                                                    </h6>
                                                    <p class="text-muted mb-1"><?php echo htmlspecialchars($result['subtitle']); ?></p>
                                                    <small class="text-muted">
                                                        <span class="badge bg-light text-dark"><?php echo ucfirst($result['result_type']); ?></span>
                                                        <?php echo date('d.m.Y H:i', strtotime($result['created_at'])); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Individual category tabs would be similar -->
                            <!-- For brevity, showing just the structure -->
                        </div>
                        
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Sonuç bulunamadı</h5>
                            <p class="text-muted">
                                "<?php echo htmlspecialchars($query); ?>" için herhangi bir sonuç bulunamadı.<br>
                                Farklı anahtar kelimeler deneyin veya arama tipini değiştirin.
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
<?php else: ?>
    <!-- Search Tips -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        Arama İpuçları
                    </h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Kısmi kelimeler kullanabilirsiniz</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Büyük/küçük harf duyarlı değil</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Türkçe karakterler desteklenir</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Birden fazla kelime arayabilirsiniz</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-filter text-info me-2"></i>
                        Arama Kategorileri
                    </h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-star text-warning me-2"></i>Influencer'lar - İsim, şehir, e-posta</li>
                        <li class="mb-2"><i class="fas fa-bullhorn text-success me-2"></i>Kampanyalar - Başlık, açıklama</li>
                        <li class="mb-2"><i class="fas fa-building text-primary me-2"></i>Markalar - İsim, sektör, kişi</li>
                        <li class="mb-2"><i class="fas fa-file text-purple me-2"></i>Dosyalar - Dosya adı, açıklama</li>
                        <li class="mb-2"><i class="fas fa-tasks text-orange me-2"></i>Görevler - Başlık, açıklama</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
// Helper functions
function getResultUrl($result) {
    switch ($result['result_type']) {
        case 'influencer':
            return 'influencer_detail.php?id=' . $result['id'];
        case 'campaign':
            return 'campaigns.php?action=edit&id=' . $result['id'];
        case 'brand':
            return 'brands.php?action=edit&id=' . $result['id'];
        case 'file':
            return 'files.php?id=' . $result['id'];
        case 'todo':
            return 'todos.php?action=edit&id=' . $result['id'];
        default:
            return '#';
    }
}

function highlightSearchTerm($text, $term) {
    if (empty($term)) return $text;
    return preg_replace('/(' . preg_quote($term, '/') . ')/i', '<mark>$1</mark>', $text);
}

// Page specific scripts
$page_scripts = '
<script>
// Auto-focus search input
document.addEventListener("DOMContentLoaded", function() {
    const searchInput = document.querySelector("input[name=\"q\"]");
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }
});

// Search suggestions (could be enhanced with AJAX)
function initSearchSuggestions() {
    // Implementation for search suggestions
}

// Keyboard shortcuts
document.addEventListener("keydown", function(e) {
    // Ctrl+K or Cmd+K to focus search
    if ((e.ctrlKey || e.metaKey) && e.key === "k") {
        e.preventDefault();
        document.querySelector("input[name=\"q\"]").focus();
    }
});
</script>

<style>
.search-result-item {
    transition: all 0.2s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

mark {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
}

.result-icon {
    width: 50px;
    text-align: center;
}
</style>
';

// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
