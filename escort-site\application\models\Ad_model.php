<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * İlan Model
 * 
 * İlan işlemleri için model sınıfı
 * 
 * @package EscortSite
 * <AUTHOR> Site Developer
 * @version 1.0
 */
class Ad_model extends CI_Model {

    protected $table = 'ads';

    public function __construct() {
        parent::__construct();
    }

    /**
     * Öne çıkan ilanları getir
     */
    public function get_featured_ads($limit = 10) {
        $this->db->select('a.*, p.display_name, p.age, p.profile_photo, c.name as city_name, cat.name as category_name');
        $this->db->from($this->table . ' a');
        $this->db->join('profiles p', 'p.user_id = a.user_id', 'left');
        $this->db->join('cities c', 'c.id = a.city_id', 'left');
        $this->db->join('categories cat', 'cat.id = a.category_id', 'left');
        $this->db->where('a.status', 'active');
        $this->db->where('a.is_featured', 1);
        $this->db->where('(a.featured_expires IS NULL OR a.featured_expires > NOW())');
        $this->db->order_by('a.created_at', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }

    /**
     * Son eklenen ilanları getir
     */
    public function get_latest_ads($limit = 12) {
        $this->db->select('a.*, p.display_name, p.age, p.profile_photo, c.name as city_name, cat.name as category_name');
        $this->db->from($this->table . ' a');
        $this->db->join('profiles p', 'p.user_id = a.user_id', 'left');
        $this->db->join('cities c', 'c.id = a.city_id', 'left');
        $this->db->join('categories cat', 'cat.id = a.category_id', 'left');
        $this->db->where('a.status', 'active');
        $this->db->order_by('a.created_at', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }

    /**
     * Aktif ilan sayısını getir
     */
    public function count_active_ads() {
        $this->db->where('status', 'active');
        return $this->db->count_all_results($this->table);
    }

    /**
     * İlan arama
     */
    public function search_ads($params, $limit = 20, $offset = 0) {
        $this->db->select('a.*, p.display_name, p.age, p.profile_photo, c.name as city_name, cat.name as category_name');
        $this->db->from($this->table . ' a');
        $this->db->join('profiles p', 'p.user_id = a.user_id', 'left');
        $this->db->join('cities c', 'c.id = a.city_id', 'left');
        $this->db->join('categories cat', 'cat.id = a.category_id', 'left');
        $this->db->where('a.status', 'active');

        // Arama filtreleri
        if (!empty($params['keyword'])) {
            $this->db->group_start();
            $this->db->like('a.title', $params['keyword']);
            $this->db->or_like('a.description', $params['keyword']);
            $this->db->or_like('p.display_name', $params['keyword']);
            $this->db->group_end();
        }

        if (!empty($params['city_id'])) {
            $this->db->where('a.city_id', $params['city_id']);
        }

        if (!empty($params['category_id'])) {
            $this->db->where('a.category_id', $params['category_id']);
        }

        if (!empty($params['min_age'])) {
            $this->db->where('p.age >=', $params['min_age']);
        }

        if (!empty($params['max_age'])) {
            $this->db->where('p.age <=', $params['max_age']);
        }

        if (!empty($params['min_price'])) {
            $this->db->where('a.price >=', $params['min_price']);
        }

        if (!empty($params['max_price'])) {
            $this->db->where('a.price <=', $params['max_price']);
        }

        $this->db->order_by('a.is_featured', 'DESC');
        $this->db->order_by('a.created_at', 'DESC');
        $this->db->limit($limit, $offset);

        return $this->db->get()->result();
    }

    /**
     * Arama sonuç sayısını getir
     */
    public function count_search_results($params) {
        $this->db->from($this->table . ' a');
        $this->db->join('profiles p', 'p.user_id = a.user_id', 'left');
        $this->db->where('a.status', 'active');

        // Arama filtreleri (search_ads ile aynı)
        if (!empty($params['keyword'])) {
            $this->db->group_start();
            $this->db->like('a.title', $params['keyword']);
            $this->db->or_like('a.description', $params['keyword']);
            $this->db->or_like('p.display_name', $params['keyword']);
            $this->db->group_end();
        }

        if (!empty($params['city_id'])) {
            $this->db->where('a.city_id', $params['city_id']);
        }

        if (!empty($params['category_id'])) {
            $this->db->where('a.category_id', $params['category_id']);
        }

        if (!empty($params['min_age'])) {
            $this->db->where('p.age >=', $params['min_age']);
        }

        if (!empty($params['max_age'])) {
            $this->db->where('p.age <=', $params['max_age']);
        }

        if (!empty($params['min_price'])) {
            $this->db->where('a.price >=', $params['min_price']);
        }

        if (!empty($params['max_price'])) {
            $this->db->where('a.price <=', $params['max_price']);
        }

        return $this->db->count_all_results();
    }

    /**
     * İlan detayını profil bilgileriyle getir
     */
    public function get_ad_with_profile($ad_id) {
        $this->db->select('a.*, p.*, u.username, c.name as city_name, cat.name as category_name');
        $this->db->from($this->table . ' a');
        $this->db->join('profiles p', 'p.user_id = a.user_id', 'left');
        $this->db->join('users u', 'u.id = a.user_id', 'left');
        $this->db->join('cities c', 'c.id = a.city_id', 'left');
        $this->db->join('categories cat', 'cat.id = a.category_id', 'left');
        $this->db->where('a.id', $ad_id);
        
        return $this->db->get()->row();
    }

    /**
     * Görüntülenme sayısını artır
     */
    public function increment_view_count($ad_id) {
        $this->db->set('view_count', 'view_count + 1', FALSE);
        $this->db->where('id', $ad_id);
        return $this->db->update($this->table);
    }

    /**
     * Kullanıcının diğer ilanları
     */
    public function get_user_other_ads($user_id, $exclude_ad_id, $limit = 6) {
        $this->db->select('a.*, p.display_name, p.age, p.profile_photo, c.name as city_name');
        $this->db->from($this->table . ' a');
        $this->db->join('profiles p', 'p.user_id = a.user_id', 'left');
        $this->db->join('cities c', 'c.id = a.city_id', 'left');
        $this->db->where('a.user_id', $user_id);
        $this->db->where('a.id !=', $exclude_ad_id);
        $this->db->where('a.status', 'active');
        $this->db->order_by('a.created_at', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }

    /**
     * Benzer ilanlar
     */
    public function get_similar_ads($city_id, $category_id, $exclude_ad_id, $limit = 6) {
        $this->db->select('a.*, p.display_name, p.age, p.profile_photo, c.name as city_name');
        $this->db->from($this->table . ' a');
        $this->db->join('profiles p', 'p.user_id = a.user_id', 'left');
        $this->db->join('cities c', 'c.id = a.city_id', 'left');
        $this->db->where('a.status', 'active');
        $this->db->where('a.id !=', $exclude_ad_id);
        
        // Önce aynı şehir ve kategori
        $this->db->group_start();
        $this->db->where('a.city_id', $city_id);
        $this->db->where('a.category_id', $category_id);
        $this->db->group_end();
        
        $this->db->order_by('a.created_at', 'DESC');
        $this->db->limit($limit);
        
        $result = $this->db->get()->result();
        
        // Yeterli sonuç yoksa sadece şehir bazlı ara
        if (count($result) < $limit) {
            $remaining = $limit - count($result);
            $exclude_ids = array_column($result, 'id');
            $exclude_ids[] = $exclude_ad_id;
            
            $this->db->select('a.*, p.display_name, p.age, p.profile_photo, c.name as city_name');
            $this->db->from($this->table . ' a');
            $this->db->join('profiles p', 'p.user_id = a.user_id', 'left');
            $this->db->join('cities c', 'c.id = a.city_id', 'left');
            $this->db->where('a.status', 'active');
            $this->db->where('a.city_id', $city_id);
            $this->db->where_not_in('a.id', $exclude_ids);
            $this->db->order_by('a.created_at', 'DESC');
            $this->db->limit($remaining);
            
            $additional = $this->db->get()->result();
            $result = array_merge($result, $additional);
        }
        
        return $result;
    }

    /**
     * Şehir bazlı ilan sayısı
     */
    public function count_ads_by_city($city_id) {
        $this->db->where('city_id', $city_id);
        $this->db->where('status', 'active');
        return $this->db->count_all_results($this->table);
    }

    /**
     * Şehir bazlı ilanlar
     */
    public function get_ads_by_city($city_id, $limit = 20, $offset = 0) {
        $this->db->select('a.*, p.display_name, p.age, p.profile_photo, c.name as city_name, cat.name as category_name');
        $this->db->from($this->table . ' a');
        $this->db->join('profiles p', 'p.user_id = a.user_id', 'left');
        $this->db->join('cities c', 'c.id = a.city_id', 'left');
        $this->db->join('categories cat', 'cat.id = a.category_id', 'left');
        $this->db->where('a.city_id', $city_id);
        $this->db->where('a.status', 'active');
        $this->db->order_by('a.is_featured', 'DESC');
        $this->db->order_by('a.created_at', 'DESC');
        $this->db->limit($limit, $offset);
        
        return $this->db->get()->result();
    }

    /**
     * Kategori bazlı ilan sayısı
     */
    public function count_ads_by_category($category_id) {
        $this->db->where('category_id', $category_id);
        $this->db->where('status', 'active');
        return $this->db->count_all_results($this->table);
    }

    /**
     * Kategori bazlı ilanlar
     */
    public function get_ads_by_category($category_id, $limit = 20, $offset = 0) {
        $this->db->select('a.*, p.display_name, p.age, p.profile_photo, c.name as city_name, cat.name as category_name');
        $this->db->from($this->table . ' a');
        $this->db->join('profiles p', 'p.user_id = a.user_id', 'left');
        $this->db->join('cities c', 'c.id = a.city_id', 'left');
        $this->db->join('categories cat', 'cat.id = a.category_id', 'left');
        $this->db->where('a.category_id', $category_id);
        $this->db->where('a.status', 'active');
        $this->db->order_by('a.is_featured', 'DESC');
        $this->db->order_by('a.created_at', 'DESC');
        $this->db->limit($limit, $offset);
        
        return $this->db->get()->result();
    }

    /**
     * İlan oluştur
     */
    public function create_ad($data) {
        return $this->db->insert($this->table, $data);
    }

    /**
     * İlan güncelle
     */
    public function update_ad($ad_id, $data) {
        $this->db->where('id', $ad_id);
        return $this->db->update($this->table, $data);
    }

    /**
     * İlan sil
     */
    public function delete_ad($ad_id) {
        $this->db->where('id', $ad_id);
        return $this->db->delete($this->table);
    }

    /**
     * Kullanıcının ilanları
     */
    public function get_user_ads($user_id, $limit = null, $offset = 0) {
        $this->db->select('a.*, c.name as city_name, cat.name as category_name');
        $this->db->from($this->table . ' a');
        $this->db->join('cities c', 'c.id = a.city_id', 'left');
        $this->db->join('categories cat', 'cat.id = a.category_id', 'left');
        $this->db->where('a.user_id', $user_id);
        $this->db->order_by('a.created_at', 'DESC');
        
        if ($limit) {
            $this->db->limit($limit, $offset);
        }
        
        return $this->db->get()->result();
    }
}
