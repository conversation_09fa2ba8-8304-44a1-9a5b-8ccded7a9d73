<?php
/**
 * Influencer Detail Page
 * Influencer detay say<PERSON><PERSON><PERSON> - profil, platformlar, kampanyalar
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Check permissions
requirePermission('influencers_view');

$influencer_id = $_GET['id'] ?? null;

if (!$influencer_id) {
    $_SESSION['error_message'] = 'Influencer ID belirtilmedi.';
    redirectTo('influencers.php');
}

// Get influencer data
try {
    global $database;
    $pdo = $database->connect();
    
    // Get influencer basic info
    $stmt = $pdo->prepare("SELECT * FROM influencers WHERE id = ?");
    $stmt->execute([$influencer_id]);
    $influencer = $stmt->fetch();
    
    if (!$influencer) {
        $_SESSION['error_message'] = 'Influencer bulunamadı.';
        redirectTo('influencers.php');
    }
    
    // Get platforms
    $stmt = $pdo->prepare("SELECT * FROM influencer_platforms WHERE influencer_id = ? ORDER BY platform");
    $stmt->execute([$influencer_id]);
    $platforms = $stmt->fetchAll();
    
    // Get pricing
    $stmt = $pdo->prepare("SELECT * FROM influencer_pricing WHERE influencer_id = ? ORDER BY platform, content_type");
    $stmt->execute([$influencer_id]);
    $pricing = $stmt->fetchAll();
    
    // Get campaigns
    $stmt = $pdo->prepare("
        SELECT c.*, b.name as brand_name, b.logo as brand_logo
        FROM campaigns c 
        LEFT JOIN brands b ON c.brand_id = b.id 
        WHERE c.influencer_id = ? 
        ORDER BY c.created_at DESC
    ");
    $stmt->execute([$influencer_id]);
    $campaigns = $stmt->fetchAll();
    
    // Get notes
    $stmt = $pdo->prepare("
        SELECT n.*, u.full_name as created_by_name
        FROM notes n 
        LEFT JOIN users u ON n.created_by = u.id 
        WHERE n.influencer_id = ? 
        ORDER BY n.created_at DESC
    ");
    $stmt->execute([$influencer_id]);
    $notes = $stmt->fetchAll();
    
    // Get files
    $stmt = $pdo->prepare("
        SELECT f.*, u.full_name as uploaded_by_name
        FROM files f 
        LEFT JOIN users u ON f.uploaded_by = u.id 
        WHERE f.influencer_id = ? 
        ORDER BY f.created_at DESC
    ");
    $stmt->execute([$influencer_id]);
    $files = $stmt->fetchAll();
    
    // Get tags
    $stmt = $pdo->prepare("
        SELECT t.* 
        FROM tags t 
        INNER JOIN influencer_tags it ON t.id = it.tag_id 
        WHERE it.influencer_id = ?
    ");
    $stmt->execute([$influencer_id]);
    $tags = $stmt->fetchAll();
    
} catch (Exception $e) {
    $_SESSION['error_message'] = 'Veri alınırken hata oluştu.';
    error_log('Influencer detail error: ' . $e->getMessage());
    redirectTo('influencers.php');
}

// Page settings
$page_title = $influencer['first_name'] . ' ' . $influencer['last_name'] . ' - Detay';

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<!-- Influencer Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <?php if ($influencer['profile_photo']): ?>
                            <img src="<?php echo UPLOAD_URL . 'profiles/' . $influencer['profile_photo']; ?>" 
                                 class="rounded-circle border border-white border-3" width="100" height="100" style="object-fit: cover;">
                        <?php else: ?>
                            <div class="bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center border border-white border-3" 
                                 style="width: 100px; height: 100px;">
                                <i class="fas fa-user fa-2x text-white"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col">
                        <h2 class="mb-2"><?php echo htmlspecialchars($influencer['first_name'] . ' ' . $influencer['last_name']); ?></h2>
                        <div class="row">
                            <div class="col-md-6">
                                <?php if ($influencer['city']): ?>
                                    <p class="mb-1"><i class="fas fa-map-marker-alt me-2"></i><?php echo htmlspecialchars($influencer['city']); ?></p>
                                <?php endif; ?>
                                <?php if ($influencer['birth_date']): ?>
                                    <p class="mb-1"><i class="fas fa-birthday-cake me-2"></i><?php echo date_diff(date_create($influencer['birth_date']), date_create('today'))->y; ?> yaş</p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1">
                                    <span class="badge bg-white text-dark">
                                        <?php 
                                        $work_types = ['freelance' => 'Freelance', 'contract' => 'Sözleşmeli', 'exclusive' => 'Özel'];
                                        echo $work_types[$influencer['work_type']] ?? $influencer['work_type']; 
                                        ?>
                                    </span>
                                </p>
                                <p class="mb-1">
                                    <span class="badge bg-<?php echo $influencer['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                        <?php 
                                        $statuses = ['active' => 'Aktif', 'inactive' => 'Pasif', 'blacklisted' => 'Kara Liste'];
                                        echo $statuses[$influencer['status']] ?? $influencer['status']; 
                                        ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="dropdown">
                            <button class="btn btn-light dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-cog me-2"></i>İşlemler
                            </button>
                            <ul class="dropdown-menu">
                                <?php if (hasPermission('influencers_manage')): ?>
                                    <li><a class="dropdown-item" href="influencers.php?action=edit&id=<?php echo $influencer['id']; ?>">
                                        <i class="fas fa-edit me-2"></i>Düzenle
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                <?php endif; ?>
                                <li><a class="dropdown-item" href="campaigns.php?action=add&influencer_id=<?php echo $influencer['id']; ?>">
                                    <i class="fas fa-plus me-2"></i>Yeni Kampanya
                                </a></li>
                                <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#addNoteModal">
                                    <i class="fas fa-sticky-note me-2"></i>Not Ekle
                                </a></li>
                                <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#uploadFileModal">
                                    <i class="fas fa-upload me-2"></i>Dosya Yükle
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Tags -->
                <?php if (!empty($tags)): ?>
                    <div class="mt-3">
                        <?php foreach ($tags as $tag): ?>
                            <span class="badge bg-white bg-opacity-25 me-2"><?php echo htmlspecialchars($tag['name']); ?></span>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Content Tabs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="detailTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button">
                            <i class="fas fa-user me-2"></i>Genel Bilgiler
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="platforms-tab" data-bs-toggle="tab" data-bs-target="#platforms" type="button">
                            <i class="fas fa-share-alt me-2"></i>Platformlar (<?php echo count($platforms); ?>)
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="pricing-tab" data-bs-toggle="tab" data-bs-target="#pricing" type="button">
                            <i class="fas fa-money-bill-wave me-2"></i>Fiyatlandırma
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="campaigns-tab" data-bs-toggle="tab" data-bs-target="#campaigns" type="button">
                            <i class="fas fa-bullhorn me-2"></i>Kampanyalar (<?php echo count($campaigns); ?>)
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="notes-tab" data-bs-toggle="tab" data-bs-target="#notes" type="button">
                            <i class="fas fa-sticky-note me-2"></i>Notlar (<?php echo count($notes); ?>)
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="files-tab" data-bs-toggle="tab" data-bs-target="#files" type="button">
                            <i class="fas fa-folder me-2"></i>Dosyalar (<?php echo count($files); ?>)
                        </button>
                    </li>
                </ul>
            </div>
            
            <div class="card-body">
                <div class="tab-content" id="detailTabsContent">
                    <!-- Overview Tab -->
                    <div class="tab-pane fade show active" id="overview" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="mb-3">İletişim Bilgileri</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="30%"><strong>E-posta:</strong></td>
                                        <td><?php echo $influencer['email'] ? htmlspecialchars($influencer['email']) : '-'; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Telefon:</strong></td>
                                        <td><?php echo $influencer['phone'] ? htmlspecialchars($influencer['phone']) : '-'; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Adres:</strong></td>
                                        <td><?php echo $influencer['address'] ? htmlspecialchars($influencer['address']) : '-'; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Şehir:</strong></td>
                                        <td><?php echo $influencer['city'] ? htmlspecialchars($influencer['city']) : '-'; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Ülke:</strong></td>
                                        <td><?php echo htmlspecialchars($influencer['country']); ?></td>
                                    </tr>
                                </table>
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="mb-3">Kişisel Bilgiler</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="30%"><strong>Doğum Tarihi:</strong></td>
                                        <td><?php echo $influencer['birth_date'] ? date('d.m.Y', strtotime($influencer['birth_date'])) : '-'; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Yaş:</strong></td>
                                        <td><?php echo $influencer['birth_date'] ? date_diff(date_create($influencer['birth_date']), date_create('today'))->y . ' yaş' : '-'; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Cinsiyet:</strong></td>
                                        <td>
                                            <?php 
                                            $genders = ['male' => 'Erkek', 'female' => 'Kadın', 'other' => 'Diğer'];
                                            echo $influencer['gender'] ? $genders[$influencer['gender']] : '-'; 
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Çalışma Tipi:</strong></td>
                                        <td>
                                            <?php 
                                            $work_types = ['freelance' => 'Freelance', 'contract' => 'Sözleşmeli', 'exclusive' => 'Özel'];
                                            echo $work_types[$influencer['work_type']] ?? $influencer['work_type']; 
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Kayıt Tarihi:</strong></td>
                                        <td><?php echo date('d.m.Y H:i', strtotime($influencer['created_at'])); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <?php if ($influencer['bio']): ?>
                            <hr>
                            <h6 class="mb-3">Biyografi</h6>
                            <p class="text-muted"><?php echo nl2br(htmlspecialchars($influencer['bio'])); ?></p>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Platforms Tab -->
                    <div class="tab-pane fade" id="platforms" role="tabpanel">
                        <?php if (!empty($platforms)): ?>
                            <div class="row">
                                <?php foreach ($platforms as $platform): ?>
                                    <div class="col-md-6 mb-4">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-body">
                                                <div class="d-flex align-items-center mb-3">
                                                    <i class="<?php echo PLATFORMS[$platform['platform']]['icon'] ?? 'fas fa-globe'; ?> fa-2x me-3"
                                                       style="color: <?php echo PLATFORMS[$platform['platform']]['color'] ?? '#6c757d'; ?>"></i>
                                                    <div>
                                                        <h6 class="mb-0"><?php echo PLATFORMS[$platform['platform']]['name'] ?? ucfirst($platform['platform']); ?></h6>
                                                        <small class="text-muted">@<?php echo htmlspecialchars($platform['username']); ?></small>
                                                    </div>
                                                    <?php if ($platform['verified']): ?>
                                                        <i class="fas fa-check-circle text-primary ms-auto"></i>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <div class="row text-center">
                                                    <div class="col-4">
                                                        <div class="border-end">
                                                            <h6 class="mb-0"><?php echo formatNumber($platform['followers_count']); ?></h6>
                                                            <small class="text-muted">Takipçi</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="border-end">
                                                            <h6 class="mb-0"><?php echo formatNumber($platform['avg_likes']); ?></h6>
                                                            <small class="text-muted">Ort. Beğeni</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <h6 class="mb-0"><?php echo number_format($platform['engagement_rate'], 2); ?>%</h6>
                                                        <small class="text-muted">Etkileşim</small>
                                                    </div>
                                                </div>
                                                
                                                <?php if ($platform['profile_url']): ?>
                                                    <div class="mt-3">
                                                        <a href="<?php echo htmlspecialchars($platform['profile_url']); ?>" 
                                                           target="_blank" class="btn btn-outline-primary btn-sm w-100">
                                                            <i class="fas fa-external-link-alt me-2"></i>Profili Görüntüle
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-share-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Henüz platform eklenmemiş</h5>
                                <p class="text-muted">Bu influencer için sosyal medya platformları ekleyin.</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPlatformModal">
                                    <i class="fas fa-plus me-2"></i>Platform Ekle
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Other tabs content will be added in next parts -->
                    <div class="tab-pane fade" id="pricing" role="tabpanel">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Fiyatlandırma sekmesi bir sonraki adımda tamamlanacak.
                        </div>
                    </div>
                    
                    <div class="tab-pane fade" id="campaigns" role="tabpanel">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Kampanyalar sekmesi bir sonraki adımda tamamlanacak.
                        </div>
                    </div>
                    
                    <div class="tab-pane fade" id="notes" role="tabpanel">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Notlar sekmesi bir sonraki adımda tamamlanacak.
                        </div>
                    </div>
                    
                    <div class="tab-pane fade" id="files" role="tabpanel">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Dosyalar sekmesi bir sonraki adımda tamamlanacak.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Page specific scripts
$page_scripts = '
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
}

.nav-tabs .nav-link.active {
    background-color: transparent;
    border-bottom: 2px solid #667eea;
    color: #667eea;
}

.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
';

// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
