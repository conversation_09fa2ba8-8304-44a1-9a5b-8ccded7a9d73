<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Şehir Model
 * 
 * Şehir işlemleri için model sınıfı
 * 
 * @package EscortSite
 * <AUTHOR> Site Developer
 * @version 1.0
 */
class City_model extends CI_Model {

    protected $table = 'cities';

    public function __construct() {
        parent::__construct();
    }

    /**
     * Aktif şehirleri getir
     */
    public function get_active_cities() {
        $this->db->where('is_active', 1);
        $this->db->order_by('sort_order', 'ASC');
        $this->db->order_by('name', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Aktif şehir sayısını getir
     */
    public function count_active_cities() {
        $this->db->where('is_active', 1);
        return $this->db->count_all_results($this->table);
    }

    /**
     * Şehiri slug ile getir
     */
    public function get_city_by_slug($slug) {
        $this->db->where('slug', $slug);
        $this->db->where('is_active', 1);
        return $this->db->get($this->table)->row();
    }

    /**
     * Şehiri ID ile getir
     */
    public function get_city_by_id($id) {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }

    /**
     * Tüm şehirleri getir (admin için)
     */
    public function get_all_cities() {
        $this->db->order_by('sort_order', 'ASC');
        $this->db->order_by('name', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Şehir oluştur
     */
    public function create_city($data) {
        return $this->db->insert($this->table, $data);
    }

    /**
     * Şehir güncelle
     */
    public function update_city($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Şehir sil
     */
    public function delete_city($id) {
        $this->db->where('id', $id);
        return $this->db->delete($this->table);
    }

    /**
     * Slug kontrolü
     */
    public function check_slug_exists($slug, $exclude_id = null) {
        $this->db->where('slug', $slug);
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        return $this->db->count_all_results($this->table) > 0;
    }

    /**
     * Şehir istatistikleri
     */
    public function get_city_stats($city_id) {
        // İlan sayısı
        $this->db->where('city_id', $city_id);
        $this->db->where('status', 'active');
        $ad_count = $this->db->count_all_results('ads');

        // Escort sayısı
        $this->db->select('COUNT(DISTINCT p.user_id) as escort_count');
        $this->db->from('profiles p');
        $this->db->join('ads a', 'a.user_id = p.user_id');
        $this->db->where('p.city_id', $city_id);
        $this->db->where('a.status', 'active');
        $escort_result = $this->db->get()->row();
        $escort_count = $escort_result ? $escort_result->escort_count : 0;

        return [
            'ad_count' => $ad_count,
            'escort_count' => $escort_count
        ];
    }
}
