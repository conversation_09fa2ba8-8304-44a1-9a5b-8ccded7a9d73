<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <title><?php echo isset($page_title) ? $page_title : 'Escort İlan Sitesi'; ?></title>
    
    <!-- Meta Tags -->
    <meta name="description" content="<?php echo isset($meta_description) ? $meta_description : 'Türkiye\'nin en güvenilir escort ilan sitesi. Kaliteli ve doğrulanmış escort ilanları.'; ?>">
    <meta name="keywords" content="<?php echo isset($meta_keywords) ? $meta_keywords : 'escort, ilan, türkiye, güvenilir, kaliteli'; ?>">
    <meta name="author" content="Escort İlan Sitesi">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo isset($page_title) ? $page_title : 'Escort İlan Sitesi'; ?>">
    <meta property="og:description" content="<?php echo isset($meta_description) ? $meta_description : 'Türkiye\'nin en güvenilir escort ilan sitesi'; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo current_url(); ?>">
    <meta property="og:site_name" content="Escort İlan Sitesi">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo base_url('assets/img/favicon.ico'); ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo base_url('assets/css/style.css'); ?>" rel="stylesheet">
    
    <!-- Age Verification Modal CSS -->
    <style>
        .age-verification-modal {
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
        }
        .age-verification-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        .age-verification-icon {
            font-size: 4rem;
            color: #fff;
            margin-bottom: 1rem;
        }
        .age-btn {
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        .age-btn-yes {
            background: #28a745;
            border: none;
            color: white;
        }
        .age-btn-yes:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        .age-btn-no {
            background: #dc3545;
            border: none;
            color: white;
        }
        .age-btn-no:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Age Verification Modal -->
    <div class="modal fade age-verification-modal" id="ageVerificationModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content age-verification-content border-0">
                <div class="modal-body text-center p-5">
                    <div class="age-verification-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h2 class="text-white mb-4">Yaş Doğrulama</h2>
                    <p class="text-white mb-4 fs-5">
                        Bu site yetişkin içeriği barındırmaktadır.<br>
                        18 yaşından büyük müsünüz?
                    </p>
                    <div class="d-flex justify-content-center">
                        <button type="button" class="btn age-btn age-btn-yes" onclick="confirmAge(true)">
                            <i class="fas fa-check me-2"></i>Evet, 18+ yaşındayım
                        </button>
                        <button type="button" class="btn age-btn age-btn-no" onclick="confirmAge(false)">
                            <i class="fas fa-times me-2"></i>Hayır
                        </button>
                    </div>
                    <p class="text-white-50 mt-4 small">
                        Bu siteye erişerek 18 yaşından büyük olduğunuzu beyan etmiş olursunuz.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="<?php echo base_url(); ?>">
                <i class="fas fa-heart text-danger me-2"></i>
                EscortSite
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo base_url(); ?>">
                            <i class="fas fa-home me-1"></i>Ana Sayfa
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo site_url('home/search'); ?>">
                            <i class="fas fa-search me-1"></i>İlan Ara
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-map-marker-alt me-1"></i>Şehirler
                        </a>
                        <ul class="dropdown-menu">
                            <?php if (isset($cities) && !empty($cities)): ?>
                                <?php foreach ($cities as $city): ?>
                                    <li><a class="dropdown-item" href="<?php echo site_url('home/city/' . $city->slug); ?>"><?php echo $city->name; ?></a></li>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <li><a class="dropdown-item" href="<?php echo site_url('home/city/istanbul'); ?>">İstanbul</a></li>
                                <li><a class="dropdown-item" href="<?php echo site_url('home/city/ankara'); ?>">Ankara</a></li>
                                <li><a class="dropdown-item" href="<?php echo site_url('home/city/izmir'); ?>">İzmir</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo site_url('home/about'); ?>">
                            <i class="fas fa-info-circle me-1"></i>Hakkımızda
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo site_url('home/contact'); ?>">
                            <i class="fas fa-envelope me-1"></i>İletişim
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php
                    $CI =& get_instance();
                    $user_id = $CI->session->userdata('user_id');
                    $username = $CI->session->userdata('username');
                    ?>
                    <?php if ($user_id): ?>
                        <!-- Giriş yapmış kullanıcı menüsü -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo $username ? $username : 'Kullanıcı'; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="<?php echo site_url('dashboard'); ?>">
                                    <i class="fas fa-tachometer-alt me-2"></i>Panel
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo site_url('dashboard/profile'); ?>">
                                    <i class="fas fa-user-edit me-2"></i>Profil
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo site_url('messages'); ?>">
                                    <i class="fas fa-comments me-2"></i>Mesajlar
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo site_url('auth/logout'); ?>">
                                    <i class="fas fa-sign-out-alt me-2"></i>Çıkış Yap
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <!-- Giriş yapmamış kullanıcı menüsü -->
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo site_url('auth/login'); ?>">
                                <i class="fas fa-sign-in-alt me-1"></i>Giriş Yap
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-primary text-white ms-2 px-3" href="<?php echo site_url('auth/register'); ?>">
                                <i class="fas fa-user-plus me-1"></i>Kayıt Ol
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <?php if ($this->session->flashdata('success')): ?>
            <div class="container mt-3">
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $this->session->flashdata('success'); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($this->session->flashdata('error')): ?>
            <div class="container mt-3">
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $this->session->flashdata('error'); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($this->session->flashdata('warning')): ?>
            <div class="container mt-3">
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $this->session->flashdata('warning'); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        <?php endif; ?>

    <script>
        // Yaş doğrulama kontrolü
        document.addEventListener('DOMContentLoaded', function() {
            // Çerez kontrolü
            if (!getCookie('age_verified')) {
                var ageModal = new bootstrap.Modal(document.getElementById('ageVerificationModal'));
                ageModal.show();
            }
        });

        function confirmAge(isAdult) {
            if (isAdult) {
                // 30 gün süreyle çerez oluştur
                setCookie('age_verified', 'true', 30);
                var ageModal = bootstrap.Modal.getInstance(document.getElementById('ageVerificationModal'));
                ageModal.hide();
            } else {
                // Başka bir siteye yönlendir
                window.location.href = 'https://www.google.com';
            }
        }

        function setCookie(name, value, days) {
            var expires = "";
            if (days) {
                var date = new Date();
                date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                expires = "; expires=" + date.toUTCString();
            }
            document.cookie = name + "=" + (value || "") + expires + "; path=/";
        }

        function getCookie(name) {
            var nameEQ = name + "=";
            var ca = document.cookie.split(';');
            for (var i = 0; i < ca.length; i++) {
                var c = ca[i];
                while (c.charAt(0) == ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        }
    </script>
