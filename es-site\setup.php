<?php
// <PERSON><PERSON>lum scripti
require_once 'application/config/database.php';

$host = $db['default']['hostname'];
$username = $db['default']['username'];
$password = $db['default']['password'];
$database = $db['default']['database'];

echo "<h1>Escort Site Kurulum</h1>";

try {
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>1. Veritabanı Oluşturma</h2>";
    
    // Veritabanını oluştur
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_turkish_ci");
    echo "<p>✅ Veritabanı oluşturuldu: $database</p>";
    
    // Veritabanını seç
    $pdo->exec("USE `$database`");
    
    echo "<h2>2. Test Kullanıcıları Ekleme</h2>";
    
    // Test kullanıcıları ekle
    $password_hash = '$2y$10$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW'; // test123
    
    // Önce mevcut test kullanıcılarını sil
    $pdo->exec("DELETE FROM `users` WHERE `email` IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')");
    
    // Test kullanıcıları ekle
    $stmt = $pdo->prepare("INSERT INTO `users` (`username`, `email`, `password`, `role`, `status`, `email_verified`, `created_at`) VALUES (?, ?, ?, ?, 'active', 1, NOW())");
    
    $users = [
        ['test_escort', '<EMAIL>', $password_hash, 'escort'],
        ['test_customer', '<EMAIL>', $password_hash, 'customer'],
        ['admin_user', '<EMAIL>', $password_hash, 'admin']
    ];
    
    foreach ($users as $user) {
        $stmt->execute($user);
        echo "<p>✅ Kullanıcı eklendi: {$user[1]} (Rol: {$user[3]})</p>";
    }
    
    echo "<h2>3. Test Verileri</h2>";
    
    // Şehirler
    $cities = [
        ['İstanbul', 'istanbul'],
        ['Ankara', 'ankara'],
        ['İzmir', 'izmir'],
        ['Bursa', 'bursa'],
        ['Antalya', 'antalya']
    ];
    
    $pdo->exec("DELETE FROM `cities` WHERE `slug` IN ('istanbul', 'ankara', 'izmir', 'bursa', 'antalya')");
    $stmt = $pdo->prepare("INSERT INTO `cities` (`name`, `slug`, `status`) VALUES (?, ?, 'active')");
    
    foreach ($cities as $city) {
        $stmt->execute($city);
    }
    echo "<p>✅ " . count($cities) . " şehir eklendi</p>";
    
    // Kategoriler
    $categories = [
        ['Escort', 'escort', 'Profesyonel escort hizmetleri'],
        ['Masaj', 'masaj', 'Rahatlatıcı masaj hizmetleri'],
        ['Eşlik', 'eslik', 'Nezaket eşlik hizmetleri'],
        ['VIP', 'vip', 'VIP özel hizmetler']
    ];
    
    $pdo->exec("DELETE FROM `categories` WHERE `slug` IN ('escort', 'masaj', 'eslik', 'vip')");
    $stmt = $pdo->prepare("INSERT INTO `categories` (`name`, `slug`, `description`, `status`) VALUES (?, ?, ?, 'active')");
    
    foreach ($categories as $category) {
        $stmt->execute($category);
    }
    echo "<p>✅ " . count($categories) . " kategori eklendi</p>";
    
    // Test profili (escort için)
    $pdo->exec("DELETE FROM `profiles` WHERE `user_id` = 1");
    $stmt = $pdo->prepare("INSERT INTO `profiles` (`user_id`, `display_name`, `age`, `city_id`, `description`, `height`, `weight`, `hair_color`, `eye_color`, `services`, `price_range`, `is_verified`) VALUES (1, 'Ayşe', 25, 1, 'Profesyonel ve güler yüzlü hizmet. İstanbul genelinde hizmet vermekteyim.', 165, 55, 'Kahverengi', 'Yeşil', ?, '500-1000', 1)");
    $stmt->execute(['["Escort", "Eşlik"]']);
    echo "<p>✅ Test profili eklendi</p>";
    
    // Test ilanları
    $pdo->exec("DELETE FROM `ads` WHERE `user_id` = 1");
    $stmt = $pdo->prepare("INSERT INTO `ads` (`user_id`, `title`, `description`, `city_id`, `category_id`, `price`, `status`, `view_count`, `is_featured`, `created_at`) VALUES (1, ?, ?, 1, 1, ?, 'active', ?, 0, NOW())");
    
    $ads = [
        ['Profesyonel Escort Hizmeti - İstanbul', 'Merhaba, ben Ayşe. İstanbul genelinde profesyonel escort hizmeti vermekteyim. Nezaket ve gizlilik önceliğimdir. Kaliteli hizmet için benimle iletişime geçebilirsiniz.', 750, 156],
        ['VIP Eşlik Hizmeti - Özel Davetler', 'Özel davetler, iş yemekleri ve sosyal etkinlikler için eşlik hizmeti. Kültürlü ve zarif bir eşlik arıyorsanız doğru adrestesiniz.', 1200, 23]
    ];
    
    foreach ($ads as $ad) {
        $stmt->execute($ad);
    }
    echo "<p>✅ " . count($ads) . " test ilanı eklendi</p>";
    
    echo "<h2>✅ Kurulum Tamamlandı!</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Test Hesapları (Şifre: test123)</h3>";
    echo "<ul>";
    echo "<li><strong>Escort:</strong> <EMAIL></li>";
    echo "<li><strong>Müşteri:</strong> <EMAIL></li>";
    echo "<li><strong>Admin:</strong> <EMAIL></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='index.php/auth/login' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Giriş Sayfasına Git</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Kurulum Hatası</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>Çözüm önerileri:</strong></p>";
    echo "<ul>";
    echo "<li>XAMPP'ta MySQL'in çalıştığından emin olun</li>";
    echo "<li>database_setup.sql dosyasını önce phpMyAdmin'de çalıştırın</li>";
    echo "<li>application/config/database.php dosyasındaki ayarları kontrol edin</li>";
    echo "</ul>";
}
?>
